using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 文件处理服务
    /// </summary>
    public class FileProcessingService
    {
        private static readonly Lazy<FileProcessingService> _instance =
            new Lazy<FileProcessingService>(() => new FileProcessingService());

        public static FileProcessingService Instance => _instance.Value;

        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isProcessing = false;

        /// <summary>
        /// 处理进度事件
        /// </summary>
        public event EventHandler<ProcessProgressEventArgs>? ProgressChanged;

        /// <summary>
        /// 处理完成事件
        /// </summary>
        public event EventHandler<ProcessCompletedEventArgs>? ProcessCompleted;

        /// <summary>
        /// 文件处理事件
        /// </summary>
        public event EventHandler<FileProcessedEventArgs>? FileProcessed;

        private FileProcessingService() { }

        /// <summary>
        /// 获取指定目录下的PowerPoint文件
        /// </summary>
        public List<string> GetPowerPointFiles(string sourcePath, bool includeSubfolders, List<string> supportedFormats)
        {
            var files = new List<string>();

            if (!Directory.Exists(sourcePath))
                return files;

            try
            {
                var searchOption = includeSubfolders ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

                foreach (var format in supportedFormats)
                {
                    var pattern = $"*{format}";
                    var formatFiles = Directory.GetFiles(sourcePath, pattern, searchOption);
                    files.AddRange(formatFiles);
                }

                // 去重并排序
                files = files.Distinct().OrderBy(f => f).ToList();

                // 根据配置决定是否过滤临时文件（以~$开头的文件）
                var config = ConfigService.Instance.GetConfig();
                if (config.ProcessSettings.SkipTemporaryFiles)
                {
                    var originalCount = files.Count;
                    var tempFiles = new List<string>();

                    files = files.Where(file =>
                    {
                        var fileName = Path.GetFileName(file);
                        var isTempFile = fileName.StartsWith("~$");
                        if (isTempFile)
                        {
                            tempFiles.Add(fileName);
                        }
                        return !isTempFile;
                    }).ToList();

                    // 记录过滤结果
                    if (tempFiles.Any())
                    {
                        LogService.Instance.LogFileProcessDetail($"过滤掉 {tempFiles.Count} 个临时文件（以~$开头）");
                        foreach (var tempFile in tempFiles.Take(5)) // 只记录前5个
                        {
                            LogService.Instance.LogFileProcessDetail($"  跳过临时文件: {tempFile}");
                        }
                        if (tempFiles.Count > 5)
                        {
                            LogService.Instance.LogFileProcessDetail($"  ... 还有 {tempFiles.Count - 5} 个临时文件被跳过");
                        }
                    }

                    LogService.Instance.LogFileProcessDetail($"文件扫描完成: 找到 {originalCount} 个文件，过滤后剩余 {files.Count} 个文件");
                }
                else
                {
                    LogService.Instance.LogFileProcessDetail($"文件扫描完成: 找到 {files.Count} 个文件（未过滤临时文件）");
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"扫描文件时出错: {ex.Message}", ex, sourcePath);
            }

            return files;
        }

        /// <summary>
        /// 开始处理文件
        /// </summary>
        public async Task StartProcessingAsync(ProcessingOptions options)
        {
            if (_isProcessing)
            {
                throw new InvalidOperationException("已有处理任务在进行中");
            }

            _isProcessing = true;
            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                await ProcessFilesAsync(options, _cancellationTokenSource.Token);
            }
            finally
            {
                _isProcessing = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 停止处理
        /// </summary>
        public void StopProcessing()
        {
            _cancellationTokenSource?.Cancel();
        }

        /// <summary>
        /// 是否正在处理
        /// </summary>
        public bool IsProcessing => _isProcessing;

        /// <summary>
        /// 处理文件
        /// </summary>
        private async Task ProcessFilesAsync(ProcessingOptions options, CancellationToken cancellationToken)
        {
            var startTime = DateTime.Now;

            // 记录处理开始的详细信息
            LogService.Instance.LogFileProcessDetail("=== 开始文件批量处理 ===");
            LogService.Instance.LogFileProcessDetail($"源路径: {options.SourcePath}");
            LogService.Instance.LogFileProcessDetail($"输出路径: {options.OutputPath}");
            LogService.Instance.LogFileProcessDetail($"包含子文件夹: {options.IncludeSubfolders}");
            LogService.Instance.LogFileProcessDetail($"保持目录结构: {options.KeepDirectoryStructure}");
            LogService.Instance.LogFileProcessDetail($"处理模式: {(options.ProcessSourceDirectly ? "直接处理源文件" : options.CopyFiles ? "复制后处理" : "移动后处理")}");
            LogService.Instance.LogFileProcessDetail($"线程数: {options.ThreadCount}");
            LogService.Instance.LogFileProcessDetail($"重试次数: {options.RetryCount}");
            LogService.Instance.LogFileProcessDetail($"批处理大小: {options.BatchSize}");

            var files = GetPowerPointFiles(options.SourcePath, options.IncludeSubfolders, options.SupportedFormats);
            LogService.Instance.LogFileProcessDetail($"扫描到的文件数量: {files.Count}");

            var stats = new ProcessingStats
            {
                TotalFiles = files.Count,
                StartTime = startTime
            };

            // 记录处理开始日志
            LogService.Instance.LogProcessStart($"开始批量处理，共找到 {files.Count} 个文件", options.SourcePath);

            OnProgressChanged(new ProcessProgressEventArgs(0, stats));

            if (files.Count == 0)
            {
                LogService.Instance.LogFileProcessDetail("没有找到符合条件的文件");
                LogService.Instance.LogProcessComplete("处理完成：没有找到符合条件的文件", options.SourcePath);
                OnProcessCompleted(new ProcessCompletedEventArgs(stats, "没有找到符合条件的文件"));
                return;
            }

            // 记录支持的文件格式
            LogService.Instance.LogFileProcessDetail($"支持的文件格式: {string.Join(", ", options.SupportedFormats)}");

            // 记录找到的文件列表（前10个）
            var filesToLog = files.Take(10).ToList();
            LogService.Instance.LogFileProcessDetail($"找到的文件（前10个）:");
            for (int i = 0; i < filesToLog.Count; i++)
            {
                LogService.Instance.LogFileProcessDetail($"  {i + 1}. {Path.GetFileName(filesToLog[i])}");
            }
            if (files.Count > 10)
            {
                LogService.Instance.LogFileProcessDetail($"  ... 还有 {files.Count - 10} 个文件");
            }

            // 创建输出目录
            if (!Directory.Exists(options.OutputPath))
            {
                Directory.CreateDirectory(options.OutputPath);
                LogService.Instance.LogFileOperation($"创建输出目录: {options.OutputPath}");
            }

            // 分批处理
            var batches = CreateBatches(files, options.BatchSize);
            var semaphore = new SemaphoreSlim(options.ThreadCount, options.ThreadCount);

            var tasks = batches.Select(async batch =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    await ProcessBatchAsync(batch, options, stats, cancellationToken);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);

            // 记录处理完成的详细统计
            var endTime = DateTime.Now;
            stats.EndTime = endTime;
            var elapsedTime = endTime - stats.StartTime;
            var successRate = stats.TotalFiles > 0 ? (double)stats.SuccessCount / stats.TotalFiles * 100 : 0;

            // 记录详细的性能统计
            LogService.Instance.LogPerformanceStats("=== 批量处理性能统计 ===");
            LogService.Instance.LogPerformanceStats($"总文件数: {stats.TotalFiles}");
            LogService.Instance.LogPerformanceStats($"成功处理: {stats.SuccessCount} ({successRate:F1}%)");
            LogService.Instance.LogPerformanceStats($"处理失败: {stats.FailureCount}");
            LogService.Instance.LogPerformanceStats($"总耗时: {FormatTimeSpan(elapsedTime)}");
            LogService.Instance.LogPerformanceStats($"平均速度: {stats.ProcessingSpeed:F1} 文件/分钟");
            LogService.Instance.LogPerformanceStats($"开始时间: {stats.StartTime:yyyy-MM-dd HH:mm:ss}");
            LogService.Instance.LogPerformanceStats($"结束时间: {endTime:yyyy-MM-dd HH:mm:ss}");

            if (stats.TotalFiles > 0)
            {
                var avgTimePerFile = elapsedTime.TotalSeconds / stats.TotalFiles;
                LogService.Instance.LogPerformanceStats($"平均每文件耗时: {avgTimePerFile:F2} 秒");
            }

            LogService.Instance.LogFileProcessDetail("=== 文件批量处理完成 ===");
            LogService.Instance.LogProcessComplete(
                $"批量处理完成 - 总计: {stats.TotalFiles} 个文件, 成功: {stats.SuccessCount} 个, 失败: {stats.FailureCount} 个, " +
                $"成功率: {successRate:F1}%, 总耗时: {FormatTimeSpan(elapsedTime)}, 平均速度: {stats.ProcessingSpeed:F1} 文件/分钟",
                options.SourcePath);

            OnProcessCompleted(new ProcessCompletedEventArgs(stats, "处理完成"));
        }

        /// <summary>
        /// 分批处理文件
        /// </summary>
        private List<List<string>> CreateBatches(List<string> files, int batchSize)
        {
            var batches = new List<List<string>>();

            for (int i = 0; i < files.Count; i += batchSize)
            {
                var batch = files.Skip(i).Take(batchSize).ToList();
                batches.Add(batch);
            }

            return batches;
        }

        /// <summary>
        /// 处理批次
        /// </summary>
        private async Task ProcessBatchAsync(List<string> batch, ProcessingOptions options,
            ProcessingStats stats, CancellationToken cancellationToken)
        {
            foreach (var file in batch)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await ProcessSingleFileAsync(file, options, stats, cancellationToken);
            }
        }

        /// <summary>
        /// 处理单个文件
        /// </summary>
        private async Task ProcessSingleFileAsync(string sourceFile, ProcessingOptions options,
            ProcessingStats stats, CancellationToken cancellationToken)
        {
            var fileName = Path.GetFileName(sourceFile);
            var retryCount = 0;
            bool success = false;
            var fileStartTime = DateTime.Now;

            LogService.Instance.LogFileProcessDetail($"开始处理文件: {fileName}", sourceFile);

            // 记录文件信息
            try
            {
                var fileInfo = new FileInfo(sourceFile);
                LogService.Instance.LogFileProcessDetail($"文件大小: {fileInfo.Length / 1024.0:F1} KB, 修改时间: {fileInfo.LastWriteTime}", sourceFile);
            }
            catch (Exception ex)
            {
                LogService.Instance.LogFileProcessDetail($"无法获取文件信息: {ex.Message}", sourceFile);
            }

            while (retryCount <= options.RetryCount && !success && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    if (retryCount > 0)
                    {
                        LogService.Instance.LogFileProcessDetail($"第 {retryCount + 1} 次尝试处理文件: {fileName}", sourceFile);
                    }

                    // 计算目标文件路径
                    var targetFile = CalculateTargetPath(sourceFile, options);
                    LogService.Instance.LogFileProcessDetail($"目标文件路径: {targetFile}", sourceFile);

                    // 确保目标目录存在
                    var targetDir = Path.GetDirectoryName(targetFile);
                    if (!string.IsNullOrEmpty(targetDir) && !Directory.Exists(targetDir))
                    {
                        Directory.CreateDirectory(targetDir);
                        LogService.Instance.LogFileProcessDetail($"创建目标目录: {targetDir}", sourceFile);
                    }

                    // 复制或移动文件
                    if (options.ProcessSourceDirectly)
                    {
                        // 直接处理源文件
                        LogService.Instance.LogFileProcessDetail("直接处理源文件", sourceFile);
                        await ProcessPowerPointFileAsync(sourceFile, cancellationToken);
                    }
                    else
                    {
                        // 先复制/移动文件，再处理
                        LogService.Instance.LogFileProcessDetail($"执行文件操作: {(options.CopyFiles ? "复制" : "移动")}", sourceFile);
                        var actualTargetFile = await HandleFileOperationWithConflict(sourceFile, targetFile, options, cancellationToken);

                        if (!string.IsNullOrEmpty(actualTargetFile))
                        {
                            LogService.Instance.LogFileProcessDetail($"文件操作完成，开始处理: {actualTargetFile}", sourceFile);
                            await ProcessPowerPointFileAsync(actualTargetFile, cancellationToken);
                        }
                        else
                        {
                            // 文件被跳过 - 计入失败统计
                            LogService.Instance.LogFileProcessDetail($"文件因冲突被跳过: {fileName}", sourceFile);
                            LogService.Instance.LogFileOperation($"文件 {fileName} 因冲突被跳过", sourceFile);

                            lock (stats)
                            {
                                stats.FailureCount++;
                            }
                            OnFileProcessed(new FileProcessedEventArgs(fileName, false, "文件因冲突被跳过"));
                            return;
                        }
                    }

                    success = true;
                    lock (stats)
                    {
                        stats.SuccessCount++;
                    }

                    var processingTime = DateTime.Now - fileStartTime;
                    LogService.Instance.LogFileProcessDetail($"文件处理成功，耗时: {processingTime.TotalSeconds:F2} 秒", sourceFile);
                    LogService.Instance.LogProcessComplete($"文件处理成功: {fileName}", sourceFile);
                    OnFileProcessed(new FileProcessedEventArgs(fileName, true, null));
                }
                catch (Exception ex)
                {
                    retryCount++;
                    var processingTime = DateTime.Now - fileStartTime;
                    LogService.Instance.LogException($"处理文件 {fileName} 失败 (第{retryCount}次尝试，耗时: {processingTime.TotalSeconds:F2} 秒)", ex, sourceFile);

                    if (retryCount > options.RetryCount)
                    {
                        lock (stats)
                        {
                            stats.FailureCount++;
                        }
                        LogService.Instance.LogFileProcessDetail($"文件处理最终失败，已达到最大重试次数: {fileName}", sourceFile);
                        OnFileProcessed(new FileProcessedEventArgs(fileName, false, ex.Message));
                    }
                    else
                    {
                        // 等待一段时间后重试
                        LogService.Instance.LogFileProcessDetail($"等待 1 秒后重试: {fileName}", sourceFile);
                        await Task.Delay(1000, cancellationToken);
                    }
                }
            }

            // 优化进度更新频率，避免UI卡顿 - 每处理完一个文件后更新进度
            var processed = stats.SuccessCount + stats.FailureCount;
            var progress = (int)((double)processed / stats.TotalFiles * 100);

            // 降低进度更新频率：每处理10个文件或达到特定进度节点时才更新
            if (processed % 10 == 0 || progress % 5 == 0 || processed == stats.TotalFiles)
            {
                OnProgressChanged(new ProcessProgressEventArgs(progress, stats));
            }
        }

        /// <summary>
        /// 处理文件操作中的冲突
        /// </summary>
        /// <param name="sourceFile">源文件路径</param>
        /// <param name="targetFile">目标文件路径</param>
        /// <param name="options">处理选项</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>实际的目标文件路径，如果跳过则返回null</returns>
        private Task<string?> HandleFileOperationWithConflict(string sourceFile, string targetFile, ProcessingOptions options, CancellationToken cancellationToken)
        {
            return Task.FromResult<string?>(HandleFileOperationWithConflictSync(sourceFile, targetFile, options));
        }

        /// <summary>
        /// 处理文件操作冲突（同步版本）
        /// </summary>
        private string? HandleFileOperationWithConflictSync(string sourceFile, string targetFile, ProcessingOptions options)
        {
            try
            {
                // 检查目标文件是否存在
                if (!File.Exists(targetFile))
                {
                    // 目标文件不存在，直接复制或移动
                    if (options.CopyFiles)
                    {
                        File.Copy(sourceFile, targetFile);
                        LogService.Instance.LogFileOperation($"复制文件: {sourceFile} -> {targetFile}");
                    }
                    else
                    {
                        File.Move(sourceFile, targetFile);
                        LogService.Instance.LogFileOperation($"移动文件: {sourceFile} -> {targetFile}");
                    }
                    return targetFile;
                }

                // 目标文件存在，根据冲突处理方式处理
                switch (options.ConflictHandling)
                {
                    case FilenameConflictHandlingType.Overwrite:
                        // 覆盖现有文件
                        if (options.CopyFiles)
                        {
                            File.Copy(sourceFile, targetFile, true);
                        }
                        else
                        {
                            File.Delete(targetFile);
                            File.Move(sourceFile, targetFile);
                        }
                        return targetFile;

                    case FilenameConflictHandlingType.Skip:
                        // 跳过冲突文件
                        LogService.Instance.LogFileOperation($"文件已存在，跳过处理: {targetFile}");
                        return null;

                    case FilenameConflictHandlingType.AutoRename:
                        // 自动重命名
                        var uniqueTargetFile = GenerateUniqueFilename(targetFile);
                        if (options.CopyFiles)
                        {
                            File.Copy(sourceFile, uniqueTargetFile);
                        }
                        else
                        {
                            File.Move(sourceFile, uniqueTargetFile);
                        }
                        return uniqueTargetFile;

                    case FilenameConflictHandlingType.Ask:
                        // 询问用户（目前默认使用自动重命名）
                        // TODO: 在实际应用中，这里应该弹出对话框询问用户
                        var askUniqueTargetFile = GenerateUniqueFilename(targetFile);
                        if (options.CopyFiles)
                        {
                            File.Copy(sourceFile, askUniqueTargetFile);
                        }
                        else
                        {
                            File.Move(sourceFile, askUniqueTargetFile);
                        }
                        return askUniqueTargetFile;

                    default:
                        // 默认覆盖
                        if (options.CopyFiles)
                        {
                            File.Copy(sourceFile, targetFile, true);
                        }
                        else
                        {
                            File.Delete(targetFile);
                            File.Move(sourceFile, targetFile);
                        }
                        return targetFile;
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"处理文件操作冲突失败: {ex.Message}", ex, sourceFile);
                throw;
            }
        }

        /// <summary>
        /// 生成唯一的文件名
        /// </summary>
        /// <param name="originalPath">原始文件路径</param>
        /// <returns>唯一的文件路径</returns>
        private string GenerateUniqueFilename(string originalPath)
        {
            var directory = Path.GetDirectoryName(originalPath) ?? "";
            var fileName = Path.GetFileNameWithoutExtension(originalPath);
            var extension = Path.GetExtension(originalPath);

            var index = 1;
            string newPath;

            do
            {
                var newFileName = $"{fileName}_{index}";
                newPath = Path.Combine(directory, newFileName + extension);
                index++;
            }
            while (File.Exists(newPath) && index < 10000); // 防止无限循环

            if (index >= 10000)
            {
                throw new InvalidOperationException("无法生成唯一的文件名");
            }

            return newPath;
        }

        /// <summary>
        /// 处理文件名替换时的冲突
        /// </summary>
        /// <param name="originalPath">原始文件路径</param>
        /// <param name="newPath">新文件路径</param>
        /// <param name="conflictHandling">冲突处理方式</param>
        /// <returns>处理后的文件路径，如果跳过则返回null</returns>
        private string? HandleFilenameReplacementConflict(string originalPath, string newPath, FilenameConflictHandlingType conflictHandling)
        {
            try
            {
                // 如果新文件路径不存在，直接返回
                if (!File.Exists(newPath))
                {
                    return newPath;
                }

                switch (conflictHandling)
                {
                    case FilenameConflictHandlingType.Overwrite:
                        // 覆盖现有文件 - 先删除目标文件
                        File.Delete(newPath);
                        LogService.Instance.LogFileOperation($"覆盖现有文件: {newPath}", originalPath);
                        return newPath;

                    case FilenameConflictHandlingType.Skip:
                        // 跳过冲突文件
                        LogService.Instance.LogFileOperation($"文件已存在，跳过重命名: {newPath}", originalPath);
                        return null;

                    case FilenameConflictHandlingType.AutoRename:
                        // 自动重命名
                        var uniquePath = GenerateUniqueFilename(newPath);
                        LogService.Instance.LogFileOperation($"自动重命名避免冲突: {newPath} -> {uniquePath}", originalPath);
                        return uniquePath;

                    case FilenameConflictHandlingType.Ask:
                        // 询问用户（目前默认使用自动重命名）
                        // TODO: 在实际应用中，这里应该弹出对话框询问用户
                        var askUniquePath = GenerateUniqueFilename(newPath);
                        LogService.Instance.LogFileOperation($"询问用户处理冲突（默认自动重命名）: {newPath} -> {askUniquePath}", originalPath);
                        return askUniquePath;

                    default:
                        return newPath;
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"处理文件名替换冲突失败: {ex.Message}", ex, originalPath);
                throw;
            }
        }

        /// <summary>
        /// 计算目标文件路径
        /// </summary>
        private string CalculateTargetPath(string sourceFile, ProcessingOptions options)
        {
            // 如果直接处理源文件，返回源文件路径（用于格式转换等操作）
            if (options.ProcessSourceDirectly)
            {
                return sourceFile;
            }

            var fileName = Path.GetFileName(sourceFile);

            // 注意：文件名替换现在在第8步处理，这里不再进行文件名替换
            // 文件名替换将在内容处理完成后的第8步进行

            // 计算输出目录中的目标路径
            if (options.KeepDirectoryStructure)
            {
                var relativePath = Path.GetRelativePath(options.SourcePath, sourceFile);
                var directory = Path.GetDirectoryName(relativePath) ?? "";
                return Path.Combine(options.OutputPath, directory, fileName);
            }
            else
            {
                return Path.Combine(options.OutputPath, fileName);
            }
        }

        /// <summary>
        /// 执行带重试的处理步骤
        /// </summary>
        /// <param name="stepName">步骤名称</param>
        /// <param name="stepAction">步骤执行动作</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        private async Task<bool> ExecuteStepWithRetryAsync(string stepName, Func<Task> stepAction, int retryCount, string filePath)
        {
            var currentRetry = 0;

            while (currentRetry <= retryCount)
            {
                try
                {
                    if (currentRetry > 0)
                    {
                        LogService.Instance.LogFileProcessDetail($"{stepName} - 第 {currentRetry + 1} 次尝试", filePath);
                    }

                    await stepAction();

                    if (currentRetry > 0)
                    {
                        LogService.Instance.LogFileProcessDetail($"{stepName} - 重试成功", filePath);
                    }

                    return true; // 成功
                }
                catch (Exception ex)
                {
                    currentRetry++;
                    LogService.Instance.LogProcessError($"{stepName} - 第 {currentRetry} 次尝试失败: {ex.Message}", ex, filePath);

                    if (currentRetry > retryCount)
                    {
                        LogService.Instance.LogProcessError($"{stepName} - 达到最大重试次数 ({retryCount})，步骤失败", ex, filePath);
                        return false; // 失败
                    }

                    // 等待一段时间后重试
                    await Task.Delay(1000, CancellationToken.None);
                }
            }

            return false;
        }

        /// <summary>
        /// 处理PowerPoint文件
        /// </summary>
        private async Task ProcessPowerPointFileAsync(string filePath, CancellationToken cancellationToken)
        {
            var fileName = Path.GetFileName(filePath);
            var processingStartTime = DateTime.Now;
            var hasAnyStepFailed = false; // 标记是否有任何步骤失败
            var config = ConfigService.Instance.GetConfig();
            var retryCount = config.ProcessSettings.RetryCount; // 获取重试次数

            try
            {
                LogService.Instance.LogFileProcessDetail($"=== 开始处理PowerPoint文件: {fileName} ===", filePath);

                // 首先进行文档删除判断（文件级别检查）
                var contentDeletionService = new ContentDeletionService();
                var documentDeletionResult = contentDeletionService.ShouldDeleteDocument(filePath, null, config.ContentDeletionSettings.DocumentDeletion);

                // 记录文档删除检查结果
                if (documentDeletionResult.CheckDetails.Any())
                {
                    LogService.Instance.LogFileProcessDetail($"文档删除检查结果: {string.Join("; ", documentDeletionResult.CheckDetails)}", filePath);
                }

                // 如果需要删除文档（基于文件级别检查），直接删除文件并返回
                if (documentDeletionResult.ShouldDelete)
                {
                    LogService.Instance.LogFileOperation($"文档符合删除条件，删除原因: {string.Join("; ", documentDeletionResult.DeletionReasons)}", filePath);

                    try
                    {
                        File.Delete(filePath);
                        LogService.Instance.LogFileOperation($"文档已删除: {fileName}", filePath);
                        return; // 文档已删除，无需继续处理
                    }
                    catch (Exception deleteEx)
                    {
                        LogService.Instance.LogProcessError($"删除文档失败: {deleteEx.Message}", deleteEx, filePath);
                        throw new InvalidOperationException($"删除文档失败: {deleteEx.Message}", deleteEx);
                    }
                }

                // 加载演示文稿
                LogService.Instance.LogFileProcessDetail("正在加载演示文稿...", filePath);
                using var presentation = new Aspose.Slides.Presentation(filePath);

                // 记录演示文稿基本信息
                LogService.Instance.LogFileProcessDetail($"演示文稿加载成功 - 幻灯片数量: {presentation.Slides.Count}", filePath);
                LogService.Instance.LogFileProcessDetail($"演示文稿尺寸: {presentation.SlideSize.Size.Width} x {presentation.SlideSize.Size.Height}", filePath);
                LogService.Instance.LogFileProcessDetail($"演示文稿格式: {presentation.SourceFormat}", filePath);

                // 进行完整的文档删除判断（包括内容级别检查）
                var fullDeletionResult = contentDeletionService.ShouldDeleteDocument(filePath, presentation, config.ContentDeletionSettings.DocumentDeletion);

                // 记录完整检查结果
                if (fullDeletionResult.CheckDetails.Any())
                {
                    LogService.Instance.LogFileProcessDetail($"完整文档删除检查结果: {string.Join("; ", fullDeletionResult.CheckDetails)}", filePath);
                }

                // 如果需要删除文档（基于完整检查），删除文件并返回
                if (fullDeletionResult.ShouldDelete)
                {
                    LogService.Instance.LogFileOperation($"文档符合删除条件，删除原因: {string.Join("; ", fullDeletionResult.DeletionReasons)}", filePath);

                    try
                    {
                        presentation.Dispose(); // 先释放资源
                        File.Delete(filePath);
                        LogService.Instance.LogFileOperation($"文档已删除: {fileName}", filePath);
                        return; // 文档已删除，无需继续处理
                    }
                    catch (Exception deleteEx)
                    {
                        LogService.Instance.LogProcessError($"删除文档失败: {deleteEx.Message}", deleteEx, filePath);
                        throw new InvalidOperationException($"删除文档失败: {deleteEx.Message}", deleteEx);
                    }
                }

                LogService.Instance.LogFileProcessDetail("已获取应用配置", filePath);

                // 按照指定顺序执行9个处理功能，根据功能启用状态进行处理
                // 正确顺序：内容删除设置、内容替换设置、页面设置、PPT格式设置、匹配段落格式、页眉页脚设置、文档属性设置、文件名替换、PPT格式转换

                // 1. 内容删除设置
                if (config.FunctionEnabled.ContainsKey("内容删除设置") && config.FunctionEnabled["内容删除设置"])
                {
                    LogService.Instance.LogFileOperation($"开始执行内容删除设置", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("内容删除设置", async () =>
                    {
                        var contentDeletionService = new ContentDeletionService();
                        var deletionResult = await contentDeletionService.ApplyContentDeletionAsync(presentation, config.ContentDeletionSettings);

                        if (!deletionResult.IsSuccess)
                        {
                            throw new InvalidOperationException($"内容删除处理失败: {deletionResult.ErrorMessage}");
                        }

                        LogService.Instance.LogFileOperation($"内容删除设置处理完成: {deletionResult.Message}", filePath);
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("内容删除设置步骤最终失败，继续执行后续步骤", null, filePath);
                    }
                }

                // 2. 内容替换设置
                if (config.FunctionEnabled.ContainsKey("内容替换设置") && config.FunctionEnabled["内容替换设置"])
                {
                    LogService.Instance.LogFileOperation($"开始执行内容替换设置", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("内容替换设置", async () =>
                    {
                        if (config.ContentReplacementSettings.TextReplacement.EnableTextReplacement ||
                            config.ContentReplacementSettings.ShapeReplacement.EnableShapeReplacement ||
                            config.ContentReplacementSettings.FontReplacement.EnableFontReplacement ||
                            config.ContentReplacementSettings.ColorReplacement.EnableColorReplacement)
                        {
                            var contentReplacementService = new ContentReplacementService();
                            var contentResult = await contentReplacementService.ApplyContentReplacementAsync(
                                presentation,
                                config.ContentReplacementSettings);

                            if (!contentResult.IsSuccess)
                            {
                                throw new InvalidOperationException($"内容替换处理失败: {contentResult.Message}");
                            }

                            LogService.Instance.LogFileOperation($"内容替换设置处理完成", filePath);
                        }
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("内容替换设置步骤最终失败，继续执行后续步骤", null, filePath);
                    }
                }

                // 3. 页面设置
                if (config.FunctionEnabled.ContainsKey("页面设置") && config.FunctionEnabled["页面设置"])
                {
                    LogService.Instance.LogFileOperation($"开始执行页面设置", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("页面设置", async () =>
                    {
                        var pageSetupService = new PageSetupService();
                        var pageSetupResult = await pageSetupService.ApplyPageSetupAsync(presentation, config.PageSetupSettings);

                        if (!pageSetupResult.IsSuccess)
                        {
                            throw new InvalidOperationException($"页面设置处理失败: {pageSetupResult.ErrorMessage}");
                        }

                        LogService.Instance.LogFileOperation($"页面设置处理完成: {pageSetupResult.Message}", filePath);
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("页面设置步骤最终失败，继续执行后续步骤", null, filePath);
                    }
                }

                // 4. PPT格式设置
                if (config.FunctionEnabled.ContainsKey("PPT格式设置") && config.FunctionEnabled["PPT格式设置"])
                {
                    LogService.Instance.LogFileOperation($"开始执行PPT格式设置", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("PPT格式设置", async () =>
                    {
                        var formatSettingsService = new PPTFormatSettingsService();
                        // 传递空的设置对象，实际的格式设置应该从配置中获取
                        var formatResult = await formatSettingsService.ApplyPPTFormatSettingsAsync(presentation, null);

                        if (!formatResult.IsSuccess)
                        {
                            throw new InvalidOperationException($"PPT格式设置处理失败: {formatResult.ErrorMessage}");
                        }

                        LogService.Instance.LogFileOperation($"PPT格式设置处理完成: {formatResult.Message}", filePath);
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("PPT格式设置步骤最终失败，继续执行后续步骤", null, filePath);
                    }
                }

                // 5. 匹配段落格式
                if (config.FunctionEnabled.ContainsKey("匹配段落格式") && config.FunctionEnabled["匹配段落格式"])
                {
                    LogService.Instance.LogFileOperation($"开始执行匹配段落格式", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("匹配段落格式", async () =>
                    {
                        if (config.ParagraphFormatMatchingSettings.EnableParagraphFormatMatching)
                        {
                            var paragraphService = new ParagraphFormatMatchingService();
                            var result = await paragraphService.ApplyParagraphFormatMatchingAsync(
                                presentation,
                                config.ParagraphFormatMatchingSettings);

                            if (!result.IsSuccess)
                            {
                                throw new InvalidOperationException($"段落格式匹配处理失败: {result.Message}");
                            }

                            LogService.Instance.LogFileOperation($"匹配段落格式处理完成", filePath);
                        }
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("匹配段落格式步骤最终失败，继续执行后续步骤", null, filePath);
                    }
                }

                // 6. PPT页脚设置
                if (config.FunctionEnabled.ContainsKey("PPT页脚设置") && config.FunctionEnabled["PPT页脚设置"])
                {
                    LogService.Instance.LogFileOperation($"开始执行页眉页脚设置", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("页眉页脚设置", () =>
                    {
                        if (config.HeaderFooterSettings.IsEnabled)
                        {
                            var headerFooterProcessor = new HeaderFooterProcessor(config.HeaderFooterSettings);
                            var headerFooterResult = headerFooterProcessor.ProcessPresentation(presentation, filePath);

                            if (!headerFooterResult.IsSuccess)
                            {
                                throw new InvalidOperationException($"页眉页脚处理失败: {headerFooterResult.ErrorMessage}");
                            }

                            LogService.Instance.LogFileOperation($"页眉页脚设置处理完成", filePath);
                        }
                        return Task.CompletedTask;
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("页眉页脚设置步骤最终失败，继续执行后续步骤", null, filePath);
                    }
                }

                // 7. 文档属性设置
                if (config.FunctionEnabled.ContainsKey("文档属性") && config.FunctionEnabled["文档属性"])
                {
                    LogService.Instance.LogFileOperation($"开始执行文档属性设置", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("文档属性设置", async () =>
                    {
                        var documentPropertiesService = new DocumentPropertiesService();
                        var propertiesResult = await documentPropertiesService.ApplyDocumentPropertiesAsync(presentation, config.DocumentPropertiesSettings);

                        if (!propertiesResult.IsSuccess)
                        {
                            throw new InvalidOperationException($"文档属性处理失败: {propertiesResult.ErrorMessage}");
                        }

                        LogService.Instance.LogFileOperation($"文档属性设置处理完成: {propertiesResult.Message}", filePath);
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("文档属性设置步骤最终失败，继续执行后续步骤", null, filePath);
                    }
                }

                // 保存演示文稿（在文件名替换之前保存）
                // 根据原始文件扩展名确定保存格式，保持原始格式
                var saveFormat = GetSaveFormatFromExtension(Path.GetExtension(filePath));
                presentation.Save(filePath, saveFormat);
                LogService.Instance.LogFileOperation($"演示文稿保存完成", filePath);

                // 8. 文件名替换 - 在内容处理完成后进行文件重命名
                if (config.FunctionEnabled.ContainsKey("文件名替换") && config.FunctionEnabled["文件名替换"])
                {
                    LogService.Instance.LogFileOperation($"开始执行文件名替换", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("文件名替换", () =>
                    {
                        if (config.FilenameReplacementSettings.PatternReplacement.IsEnabled)
                        {
                            var filenameService = new FilenameReplacementService();
                            // 使用全局冲突处理设置，而不是文件名替换功能的独立设置
                            var globalConflictHandling = config.ProcessSettings.GlobalConflictHandling;
                            var newFilePath = filenameService.ApplyFilenameReplacement(filePath, config.FilenameReplacementSettings, globalConflictHandling);

                            // 如果文件名发生了变化，进行重命名
                            if (!string.Equals(filePath, newFilePath, StringComparison.OrdinalIgnoreCase))
                            {
                                // 根据全局冲突处理方式处理文件名冲突
                                newFilePath = HandleFilenameReplacementConflict(filePath, newFilePath, globalConflictHandling);

                                if (!string.IsNullOrEmpty(newFilePath))
                                {
                                    File.Move(filePath, newFilePath);
                                    LogService.Instance.LogFileOperation($"文件重命名: {filePath} -> {newFilePath}", filePath);

                                    // 更新文件路径用于后续的格式转换
                                    filePath = newFilePath;
                                }
                            }
                        }
                        LogService.Instance.LogFileOperation($"文件名替换处理完成", filePath);
                        return Task.CompletedTask;
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("文件名替换步骤最终失败，继续执行后续步骤", null, filePath);
                    }
                }

                // 9. PPT格式转换
                if (config.FunctionEnabled.ContainsKey("PPT格式转换") && config.FunctionEnabled["PPT格式转换"])
                {
                    LogService.Instance.LogFileOperation($"开始执行PPT格式转换", filePath);

                    var stepSuccess = await ExecuteStepWithRetryAsync("PPT格式转换", async () =>
                    {
                        await ApplyFormatConversionAsync(filePath, config.PPTFormatConversionSettings, cancellationToken);
                        LogService.Instance.LogFileOperation($"PPT格式转换处理完成", filePath);
                    }, retryCount, filePath);

                    if (!stepSuccess)
                    {
                        hasAnyStepFailed = true;
                        LogService.Instance.LogProcessError("PPT格式转换步骤最终失败", null, filePath);
                    }
                }

                await Task.Delay(50, cancellationToken); // 短暂延迟以避免文件锁定

                var processingTime = DateTime.Now - processingStartTime;

                // 根据是否有步骤失败来判断整体处理结果
                if (hasAnyStepFailed)
                {
                    LogService.Instance.LogFileProcessDetail($"=== PowerPoint文件处理完成但有步骤失败: {fileName}，总耗时: {processingTime.TotalSeconds:F2} 秒 ===", filePath);
                    throw new InvalidOperationException("文件处理过程中有一个或多个步骤失败");
                }
                else
                {
                    LogService.Instance.LogFileProcessDetail($"=== PowerPoint文件处理完成: {fileName}，总耗时: {processingTime.TotalSeconds:F2} 秒 ===", filePath);
                }
            }
            catch (Exception ex)
            {
                var processingTime = DateTime.Now - processingStartTime;
                LogService.Instance.LogException($"处理PowerPoint文件 {fileName} 时发生错误，耗时: {processingTime.TotalSeconds:F2} 秒", ex, filePath);
                throw;
            }
        }



        /// <summary>
        /// 触发进度变更事件
        /// </summary>
        protected virtual void OnProgressChanged(ProcessProgressEventArgs e)
        {
            ProgressChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 触发处理完成事件
        /// </summary>
        protected virtual void OnProcessCompleted(ProcessCompletedEventArgs e)
        {
            ProcessCompleted?.Invoke(this, e);
        }

        /// <summary>
        /// 格式化时间跨度为可读字符串
        /// </summary>
        /// <param name="timeSpan">时间跨度</param>
        /// <returns>格式化的时间字符串</returns>
        private static string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
            {
                return $"{(int)timeSpan.TotalDays}天 {timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
            else if (timeSpan.TotalHours >= 1)
            {
                return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
            else if (timeSpan.TotalMinutes >= 1)
            {
                return $"{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
            else
            {
                return $"{timeSpan.Seconds}.{timeSpan.Milliseconds:D3}秒";
            }
        }

        /// <summary>
        /// 应用格式转换
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="conversionSettings">转换设置</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ApplyFormatConversionAsync(string filePath, PPTFormatConversionSettings conversionSettings, CancellationToken cancellationToken)
        {
            try
            {
                var conversionService = new PPTFormatConversionService();
                var fileDirectory = Path.GetDirectoryName(filePath) ?? "";
                var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);

                // PDF转换
                if (conversionSettings.PDFSettings.EnablePDFConversion)
                {
                    var pdfOutputPath = Path.Combine(fileDirectory, $"{fileNameWithoutExtension}.pdf");
                    conversionService.ConvertToPDF(filePath, pdfOutputPath, conversionSettings.PDFSettings);
                    LogService.Instance.LogFileOperation($"已转换为PDF: {pdfOutputPath}", filePath);
                }

                // 图片转换
                if (conversionSettings.ImageSettings.EnablePNGConversion ||
                    conversionSettings.ImageSettings.EnableJPEGConversion ||
                    conversionSettings.ImageSettings.EnableBMPConversion ||
                    conversionSettings.ImageSettings.EnableTIFFConversion ||
                    conversionSettings.ImageSettings.EnableSVGConversion)
                {
                    conversionService.ConvertToImages(filePath, fileDirectory, conversionSettings.ImageSettings);
                    LogService.Instance.LogFileOperation($"已转换为图片格式: {fileDirectory}", filePath);
                }

                // HTML转换
                if (conversionSettings.WebSettings.EnableHTMLConversion)
                {
                    var htmlOutputPath = Path.Combine(fileDirectory, $"{fileNameWithoutExtension}.html");
                    conversionService.ConvertToHTML(filePath, htmlOutputPath, conversionSettings.WebSettings);
                    LogService.Instance.LogFileOperation($"已转换为HTML: {htmlOutputPath}", filePath);
                }

                // XAML转换
                if (conversionSettings.WebSettings.EnableXAMLConversion)
                {
                    var xamlOutputPath = Path.Combine(fileDirectory, $"{fileNameWithoutExtension}.xaml");
                    conversionService.ConvertToXAML(filePath, xamlOutputPath, conversionSettings.WebSettings);
                    LogService.Instance.LogFileOperation($"已转换为XAML: {xamlOutputPath}", filePath);
                }



                await Task.Delay(10, cancellationToken); // 短暂延迟
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"格式转换失败: {ex.Message}", ex, filePath);
                throw; // 抛出异常，让重试机制处理
            }
        }





        /// <summary>
        /// 根据文件扩展名获取保存格式 - 基于Aspose.Slides SaveFormat枚举
        /// 支持所有PowerPoint和OpenDocument格式，确保与API规范一致
        /// </summary>
        /// <param name="extension">文件扩展名</param>
        /// <returns>对应的保存格式</returns>
        private Aspose.Slides.Export.SaveFormat GetSaveFormatFromExtension(string extension)
        {
            return extension.ToLowerInvariant() switch
            {
                ".ppt" => Aspose.Slides.Export.SaveFormat.Ppt,        // PowerPoint 97-2003 演示文稿
                ".pptx" => Aspose.Slides.Export.SaveFormat.Pptx,      // PowerPoint 演示文稿
                ".pptm" => Aspose.Slides.Export.SaveFormat.Pptm,      // PowerPoint 启用宏的演示文稿
                ".ppsx" => Aspose.Slides.Export.SaveFormat.Ppsx,      // PowerPoint 幻灯片放映
                ".ppsm" => Aspose.Slides.Export.SaveFormat.Ppsm,      // PowerPoint 启用宏的幻灯片放映
                ".potx" => Aspose.Slides.Export.SaveFormat.Potx,      // PowerPoint 模板
                ".potm" => Aspose.Slides.Export.SaveFormat.Potm,      // PowerPoint 启用宏的模板
                ".odp" => Aspose.Slides.Export.SaveFormat.Odp,        // OpenDocument 演示文稿
                ".otp" => Aspose.Slides.Export.SaveFormat.Otp,        // OpenDocument 演示文稿模板
                _ => Aspose.Slides.Export.SaveFormat.Pptx             // 默认使用PPTX格式
            };
        }

        /// <summary>
        /// 触发文件处理事件
        /// </summary>
        protected virtual void OnFileProcessed(FileProcessedEventArgs e)
        {
            FileProcessed?.Invoke(this, e);
        }
    }
}
