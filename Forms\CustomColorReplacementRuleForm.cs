using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 自定义颜色替换规则编辑窗体
    /// </summary>
    public partial class CustomColorReplacementRuleForm : Form
    {
        private CustomColorReplacementRule _rule;
        private bool _isEditMode;

        // 控件
        private TextBox txtRuleName = null!;
        private Button btnSourceColor = null!;
        private Button btnTargetColor = null!;
        private CheckBox chkApplyToTextColor = null!;
        private CheckBox chkApplyToFillColor = null!;
        private CheckBox chkApplyToBorderColor = null!;
        private CheckBox chkApplyToBackgroundColor = null!;
        private CheckBox chkIsEnabled = null!;
        private Button btnOK = null!;
        private Button btnCancel = null!;

        private Color _sourceColor = Color.Black;
        private Color _targetColor = Color.Black;

        public CustomColorReplacementRule Rule => _rule;

        public CustomColorReplacementRuleForm(CustomColorReplacementRule? rule = null)
        {
            InitializeComponent();
            _isEditMode = rule != null;
            _rule = rule ?? new CustomColorReplacementRule();

            InitializeForm();
            LoadRuleData();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = _isEditMode ? "编辑自定义颜色替换规则" : "添加自定义颜色替换规则";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            int yPos = 20;
            int leftMargin = 20;
            int labelWidth = 100;
            int controlWidth = 280;

            // 规则名称
            var lblRuleName = new Label
            {
                Text = "规则名称:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblRuleName);

            txtRuleName = new TextBox
            {
                Name = "txtRuleName",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25)
            };
            this.Controls.Add(txtRuleName);
            yPos += 35;

            // 源颜色
            var lblSourceColor = new Label
            {
                Text = "源颜色:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblSourceColor);

            btnSourceColor = new Button
            {
                Text = "选择颜色",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(120, 30),
                BackColor = _sourceColor
            };
            this.Controls.Add(btnSourceColor);
            yPos += 40;

            // 目标颜色
            var lblTargetColor = new Label
            {
                Text = "目标颜色:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblTargetColor);

            btnTargetColor = new Button
            {
                Text = "选择颜色",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(120, 30),
                BackColor = _targetColor
            };
            this.Controls.Add(btnTargetColor);
            yPos += 50;

            // 应用范围
            var grpApplyRange = new GroupBox
            {
                Text = "应用范围",
                Location = new Point(leftMargin, yPos),
                Size = new Size(controlWidth + labelWidth + 10, 120)
            };
            this.Controls.Add(grpApplyRange);

            chkApplyToTextColor = new CheckBox
            {
                Text = "应用到文本颜色",
                Location = new Point(15, 25),
                Size = new Size(150, 25),
                Checked = true
            };
            grpApplyRange.Controls.Add(chkApplyToTextColor);

            chkApplyToFillColor = new CheckBox
            {
                Text = "应用到填充颜色",
                Location = new Point(15, 55),
                Size = new Size(150, 25),
                Checked = true
            };
            grpApplyRange.Controls.Add(chkApplyToFillColor);

            chkApplyToBorderColor = new CheckBox
            {
                Text = "应用到边框颜色",
                Location = new Point(200, 25),
                Size = new Size(150, 25)
            };
            grpApplyRange.Controls.Add(chkApplyToBorderColor);

            chkApplyToBackgroundColor = new CheckBox
            {
                Text = "应用到背景颜色",
                Location = new Point(200, 55),
                Size = new Size(150, 25)
            };
            grpApplyRange.Controls.Add(chkApplyToBackgroundColor);

            yPos += 130;

            // 是否启用
            chkIsEnabled = new CheckBox
            {
                Text = "启用此规则",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(150, 25),
                Checked = true
            };
            this.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(this.Width - 180, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            this.Controls.Add(btnOK);

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(this.Width - 95, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(btnCancel);
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            btnOK.Click += BtnOK_Click;
            btnSourceColor.Click += BtnSourceColor_Click;
            btnTargetColor.Click += BtnTargetColor_Click;
        }

        /// <summary>
        /// 选择源颜色
        /// </summary>
        private void BtnSourceColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _sourceColor };
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _sourceColor = colorDialog.Color;
                btnSourceColor.BackColor = _sourceColor;
                btnSourceColor.Text = $"RGB({_sourceColor.R},{_sourceColor.G},{_sourceColor.B})";
            }
        }

        /// <summary>
        /// 选择目标颜色
        /// </summary>
        private void BtnTargetColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _targetColor };
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _targetColor = colorDialog.Color;
                btnTargetColor.BackColor = _targetColor;
                btnTargetColor.Text = $"RGB({_targetColor.R},{_targetColor.G},{_targetColor.B})";
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRuleData();
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtRuleName.Text))
            {
                MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRuleName.Focus();
                return false;
            }

            if (!chkApplyToTextColor.Checked && !chkApplyToFillColor.Checked &&
                !chkApplyToBorderColor.Checked && !chkApplyToBackgroundColor.Checked)
            {
                MessageBox.Show("请至少选择一种应用范围", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            txtRuleName.Text = _rule.RuleName;

            // 设置颜色
            if (!string.IsNullOrEmpty(_rule.SourceColor))
            {
                try
                {
                    _sourceColor = ColorTranslator.FromHtml(_rule.SourceColor);
                    btnSourceColor.BackColor = _sourceColor;
                    btnSourceColor.Text = $"RGB({_sourceColor.R},{_sourceColor.G},{_sourceColor.B})";
                }
                catch { }
            }

            if (!string.IsNullOrEmpty(_rule.TargetColor))
            {
                try
                {
                    _targetColor = ColorTranslator.FromHtml(_rule.TargetColor);
                    btnTargetColor.BackColor = _targetColor;
                    btnTargetColor.Text = $"RGB({_targetColor.R},{_targetColor.G},{_targetColor.B})";
                }
                catch { }
            }

            chkApplyToTextColor.Checked = _rule.ApplyToTextColor;
            chkApplyToFillColor.Checked = _rule.ApplyToFillColor;
            chkApplyToBorderColor.Checked = _rule.ApplyToBorderColor;
            chkApplyToBackgroundColor.Checked = _rule.ApplyToBackgroundColor;
            chkIsEnabled.Checked = _rule.IsEnabled;
        }

        /// <summary>
        /// 保存规则数据
        /// </summary>
        private void SaveRuleData()
        {
            _rule.RuleName = txtRuleName.Text.Trim();
            _rule.SourceColor = ColorTranslator.ToHtml(_sourceColor);
            _rule.TargetColor = ColorTranslator.ToHtml(_targetColor);
            _rule.ApplyToTextColor = chkApplyToTextColor.Checked;
            _rule.ApplyToFillColor = chkApplyToFillColor.Checked;
            _rule.ApplyToBorderColor = chkApplyToBorderColor.Checked;
            _rule.ApplyToBackgroundColor = chkApplyToBackgroundColor.Checked;
            _rule.IsEnabled = chkIsEnabled.Checked;
            _rule.CreatedTime = _isEditMode ? _rule.CreatedTime : DateTime.Now;
        }
    }
}
