namespace PPTPiliangChuli.Models
{
    /// <summary>
    /// 定时设置模型 - 用于ScheduleSettingsForm的数据绑定
    /// </summary>
    public class ScheduleSettings
    {
        /// <summary>
        /// 是否启用定时处理
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// 定时模式：0=一次性启动，1=指定时间启动，2=定时启动
        /// </summary>
        public int ScheduleMode { get; set; } = 0;

        #region 一次性启动设置

        /// <summary>
        /// 一次性启动时间
        /// </summary>
        public DateTime OneTimeRunTime { get; set; } = DateTime.Now.AddHours(1);

        #endregion

        #region 指定时间启动设置

        /// <summary>
        /// 运行频率：0=每年，1=每月，2=每天，3=每小时
        /// </summary>
        public int RunFrequency { get; set; } = 2;

        /// <summary>
        /// 每年运行（兼容性属性）
        /// </summary>
        public bool RunYearly { get; set; } = false;

        /// <summary>
        /// 每月运行（兼容性属性）
        /// </summary>
        public bool RunMonthly { get; set; } = false;

        /// <summary>
        /// 每天运行（兼容性属性）
        /// </summary>
        public bool RunDaily { get; set; } = true;

        /// <summary>
        /// 每小时运行（兼容性属性）
        /// </summary>
        public bool RunHourly { get; set; } = false;

        /// <summary>
        /// 月份（1-12）
        /// </summary>
        public int Month { get; set; } = DateTime.Now.Month;

        /// <summary>
        /// 日期（1-31）
        /// </summary>
        public int Day { get; set; } = DateTime.Now.Day;

        /// <summary>
        /// 小时（0-23）
        /// </summary>
        public int Hour { get; set; } = DateTime.Now.Hour;

        /// <summary>
        /// 分钟（0-59）
        /// </summary>
        public int Minute { get; set; } = DateTime.Now.Minute;

        /// <summary>
        /// 秒（0-59）
        /// </summary>
        public int Second { get; set; } = 0;

        #endregion

        #region 定时启动设置

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now.AddMinutes(30);

        /// <summary>
        /// 间隔天数
        /// </summary>
        public int IntervalDays { get; set; } = 0;

        /// <summary>
        /// 间隔小时
        /// </summary>
        public int IntervalHours { get; set; } = 1;

        /// <summary>
        /// 间隔分钟
        /// </summary>
        public int IntervalMinutes { get; set; } = 0;

        /// <summary>
        /// 间隔秒数
        /// </summary>
        public int IntervalSeconds { get; set; } = 0;

        #endregion

        #region 高级设置

        /// <summary>
        /// 是否使用运行次数限制
        /// </summary>
        public bool UseLimitedRuns { get; set; } = false;

        /// <summary>
        /// 最大运行次数
        /// </summary>
        public int MaxRunCount { get; set; } = 1;

        /// <summary>
        /// 是否使用过期时间
        /// </summary>
        public bool UseExpirationTime { get; set; } = false;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpirationTime { get; set; } = DateTime.Now.AddDays(1);

        #endregion

        /// <summary>
        /// 转换为ScheduleProcessingConfig
        /// </summary>
        /// <returns></returns>
        public ScheduleProcessingConfig ToScheduleProcessingConfig()
        {
            var config = new ScheduleProcessingConfig();

            // 设置定时类型
            switch (ScheduleMode)
            {
                case 0:
                    config.ScheduleType = ScheduleType.OneTime;
                    config.OneTimeExecutionTime = OneTimeRunTime;
                    break;
                case 1:
                    config.ScheduleType = ScheduleType.Recurring;
                    config.RecurringType = (RecurringType)RunFrequency;
                    config.Month = Month;
                    config.Day = Day;
                    config.Hour = Hour;
                    config.Minute = Minute;
                    config.Second = Second;
                    break;
                case 2:
                    config.ScheduleType = ScheduleType.Countdown;
                    config.CountdownFirstExecutionTime = StartTime;
                    config.CountdownInterval = new TimeSpan(IntervalDays, IntervalHours, IntervalMinutes, IntervalSeconds);
                    break;
            }

            // 设置高级设置
            if (UseLimitedRuns)
            {
                config.LimitationType = LimitationType.Count;
                config.LimitCount = MaxRunCount;
            }
            else if (UseExpirationTime)
            {
                config.LimitationType = LimitationType.Time;
                config.LimitTime = ExpirationTime;
            }
            else
            {
                config.LimitationType = LimitationType.Unlimited;
            }

            return config;
        }

        /// <summary>
        /// 从ScheduleProcessingConfig创建ScheduleSettings
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        public static ScheduleSettings FromScheduleProcessingConfig(ScheduleProcessingConfig config)
        {
            var settings = new ScheduleSettings();

            // 设置定时类型
            switch (config.ScheduleType)
            {
                case ScheduleType.OneTime:
                    settings.ScheduleMode = 0;
                    settings.OneTimeRunTime = config.OneTimeExecutionTime;
                    break;
                case ScheduleType.Recurring:
                    settings.ScheduleMode = 1;
                    settings.RunFrequency = (int)config.RecurringType;
                    settings.Month = config.Month;
                    settings.Day = config.Day;
                    settings.Hour = config.Hour;
                    settings.Minute = config.Minute;
                    settings.Second = config.Second;
                    break;
                case ScheduleType.Countdown:
                    settings.ScheduleMode = 2;
                    settings.StartTime = config.CountdownFirstExecutionTime;
                    settings.IntervalDays = config.CountdownInterval.Days;
                    settings.IntervalHours = config.CountdownInterval.Hours;
                    settings.IntervalMinutes = config.CountdownInterval.Minutes;
                    settings.IntervalSeconds = config.CountdownInterval.Seconds;
                    break;
            }

            // 设置高级设置
            switch (config.LimitationType)
            {
                case LimitationType.Count:
                    settings.UseLimitedRuns = true;
                    settings.MaxRunCount = config.LimitCount;
                    break;
                case LimitationType.Time:
                    settings.UseExpirationTime = true;
                    settings.ExpirationTime = config.LimitTime;
                    break;
                default:
                    settings.UseLimitedRuns = false;
                    settings.UseExpirationTime = false;
                    break;
            }

            return settings;
        }
    }
}
