using System;
using System.IO;
using System.Windows.Forms;
using Aspose.Slides;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 初始化应用程序
            Application.SetHighDpiMode(HighDpiMode.SystemAware);
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // 创建必要的目录（必须在日志服务之前）
                CreateDirectories();

                // 记录程序启动信息
                LogApplicationStartup();

                // 初始化配置服务
                InitializeConfigService();

                // 设置Aspose.Slides许可证
                SetAsposeSlicesLicense();

                // 记录系统环境信息
                LogSystemEnvironment();

                // 启动主窗体
                Services.LogService.Instance.LogApplicationInit("启动主窗体");
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                // 记录启动错误
                try
                {
                    Services.LogService.Instance.LogException("程序启动失败", ex);
                }
                catch
                {
                    // 日志服务可能未初始化，忽略错误
                }

                MessageBox.Show($"程序启动失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 程序退出时刷新日志
                try
                {
                    Services.LogService.Instance.LogApplicationInit("程序正在退出");
                    Services.LogService.Instance.FlushLogs();
                }
                catch
                {
                    // 忽略日志错误
                }
            }
        }

        /// <summary>
        /// 记录程序启动信息
        /// </summary>
        private static void LogApplicationStartup()
        {
            try
            {
                Services.LogService.Instance.LogApplicationInit("=== PPT批量处理工具启动 ===");
                Services.LogService.Instance.LogApplicationInit($"程序版本: {Application.ProductVersion}");
                Services.LogService.Instance.LogApplicationInit($"启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Services.LogService.Instance.LogApplicationInit($"工作目录: {Application.StartupPath}");
                Services.LogService.Instance.LogApplicationInit($"操作系统: {Environment.OSVersion}");
                Services.LogService.Instance.LogApplicationInit($".NET版本: {Environment.Version}");
                Services.LogService.Instance.LogApplicationInit($"机器名称: {Environment.MachineName}");
                Services.LogService.Instance.LogApplicationInit($"用户名: {Environment.UserName}");
            }
            catch
            {
                // 忽略日志错误
            }
        }

        /// <summary>
        /// 初始化配置服务
        /// </summary>
        private static void InitializeConfigService()
        {
            try
            {
                Services.LogService.Instance.LogConfigLoad("开始初始化配置服务");

                // ConfigService在首次访问Instance时自动初始化
                var config = Services.ConfigService.Instance.GetConfig();

                Services.LogService.Instance.LogConfigLoad("配置服务初始化完成");
                Services.LogService.Instance.LogConfigLoad($"已加载 {config.FunctionEnabled.Count} 个功能模块配置");
            }
            catch (Exception ex)
            {
                Services.LogService.Instance.LogException("配置服务初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 记录系统环境信息
        /// </summary>
        private static void LogSystemEnvironment()
        {
            try
            {
                Services.LogService.Instance.LogSystemStatus("=== 系统环境信息 ===");
                Services.LogService.Instance.LogSystemStatus($"处理器数量: {Environment.ProcessorCount}");
                Services.LogService.Instance.LogSystemStatus($"工作集内存: {Environment.WorkingSet / 1024 / 1024} MB");
                Services.LogService.Instance.LogSystemStatus($"系统目录: {Environment.SystemDirectory}");
                Services.LogService.Instance.LogSystemStatus($"当前目录: {Environment.CurrentDirectory}");

                // 检查Aspose.Slides.dll文件信息
                string asposeDllPath = Path.Combine(Application.StartupPath, "Aspose.Slides.dll");
                if (File.Exists(asposeDllPath))
                {
                    var fileInfo = new FileInfo(asposeDllPath);
                    Services.LogService.Instance.LogSystemStatus($"Aspose.Slides.dll: 存在, 大小: {fileInfo.Length / 1024 / 1024} MB, 修改时间: {fileInfo.LastWriteTime}");
                }
                else
                {
                    Services.LogService.Instance.LogSystemStatus("Aspose.Slides.dll: 不存在");
                }
            }
            catch (Exception ex)
            {
                Services.LogService.Instance.LogException("记录系统环境信息失败", ex);
            }
        }

        /// <summary>
        /// 设置Aspose.Slides许可证
        /// </summary>
        private static void SetAsposeSlicesLicense()
        {
            try
            {
                Services.LogService.Instance.LogLicense("=== 开始加载Aspose.Slides许可证 ===");

                string licenseFile = Path.Combine(Application.StartupPath, "Aspose.Total.NET.lic");
                Services.LogService.Instance.LogLicense($"许可证文件路径: {licenseFile}");

                bool licenseExists = File.Exists(licenseFile);
                Services.LogService.Instance.LogLicense($"许可证文件存在: {licenseExists}");

                if (licenseExists)
                {
                    // 记录许可证文件信息
                    var fileInfo = new FileInfo(licenseFile);
                    Services.LogService.Instance.LogLicense($"许可证文件大小: {fileInfo.Length} 字节");
                    Services.LogService.Instance.LogLicense($"许可证文件修改时间: {fileInfo.LastWriteTime}");

                    // 加载许可证
                    Services.LogService.Instance.LogLicense("正在加载许可证...");
                    var license = new License();
                    license.SetLicense(licenseFile);
                    Services.LogService.Instance.LogLicense("许可证文件加载完成");

                    // 验证许可证是否成功加载
                    Services.LogService.Instance.LogLicense("正在验证许可证有效性...");
                    using var presentation = new Presentation();

                    // 创建一个简单的幻灯片来测试许可证
                    var slide = presentation.Slides[0];
                    slide.Shapes.AddAutoShape(ShapeType.Rectangle, 100, 100, 200, 100);

                    Services.LogService.Instance.LogLicense($"许可证验证成功 - 幻灯片数量: {presentation.Slides.Count}, 形状数量: {slide.Shapes.Count}");
                    Services.LogService.Instance.LogLicense("=== Aspose.Slides许可证加载成功 ===");
                }
                else
                {
                    Services.LogService.Instance.LogLicense("许可证文件不存在，将使用试用版", LogLevel.Warning);
                    Services.LogService.Instance.LogLicense("=== 使用Aspose.Slides试用版 ===", LogLevel.Warning);

                    MessageBox.Show($"许可证文件不存在，将使用试用版\n路径: {licenseFile}", "警告",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                // 许可证加载失败，继续使用试用版
                Services.LogService.Instance.LogException("许可证加载失败，将使用试用版", ex);

                string errorMessage = $"许可证加载失败，将使用试用版：{ex.Message}";
                MessageBox.Show(errorMessage, "警告",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 创建必要的目录
        /// </summary>
        private static void CreateDirectories()
        {
            string appPath = Application.StartupPath;
            
            // 创建配置目录
            string configPath = Path.Combine(appPath, "Config");
            if (!Directory.Exists(configPath))
                Directory.CreateDirectory(configPath);

            // 创建日志目录
            string logPath = Path.Combine(appPath, "Log");
            if (!Directory.Exists(logPath))
                Directory.CreateDirectory(logPath);
        }
    }
}
