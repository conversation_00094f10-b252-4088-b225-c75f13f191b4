using System;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 定时处理服务 - 独立于窗体的定时处理逻辑
    /// </summary>
    public class ScheduleProcessingService
    {
        #region 单例模式

        private static readonly Lazy<ScheduleProcessingService> _instance = 
            new Lazy<ScheduleProcessingService>(() => new ScheduleProcessingService());
        
        public static ScheduleProcessingService Instance => _instance.Value;

        private ScheduleProcessingService() 
        {
            InitializeTimers();
        }

        #endregion

        #region 私有字段

        /// <summary>
        /// 定时器 - 用于一次性启动
        /// </summary>
        private System.Windows.Forms.Timer? _oneTimeTimer;

        /// <summary>
        /// 定时器 - 用于指定时间启动
        /// </summary>
        private System.Windows.Forms.Timer? _recurringTimer;

        /// <summary>
        /// 定时器 - 用于倒计时启动
        /// </summary>
        private System.Windows.Forms.Timer? _countdownTimer;

        /// <summary>
        /// 状态检查定时器
        /// </summary>
        private System.Windows.Forms.Timer? _statusTimer;

        /// <summary>
        /// 是否正在运行
        /// </summary>
        private bool _isRunning = false;

        /// <summary>
        /// 当前运行次数
        /// </summary>
        private int _currentRunCount = 0;

        /// <summary>
        /// 下次启动时间
        /// </summary>
        private DateTime _nextRunTime;

        /// <summary>
        /// 当前定时处理配置
        /// </summary>
        private ScheduleProcessingConfig? _currentConfig;

        /// <summary>
        /// 主窗体引用
        /// </summary>
        private Form? _mainForm;

        #endregion

        #region 公共属性

        /// <summary>
        /// 是否正在运行定时处理
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 当前运行次数
        /// </summary>
        public int CurrentRunCount => _currentRunCount;

        /// <summary>
        /// 下次执行时间
        /// </summary>
        public DateTime NextRunTime => _nextRunTime;

        /// <summary>
        /// 当前定时处理模式描述
        /// </summary>
        public string CurrentModeDescription { get; private set; } = "";

        #endregion

        #region 事件定义

        /// <summary>
        /// 状态变更事件
        /// </summary>
        public event EventHandler<ScheduleStatusChangedEventArgs>? StatusChanged;

        /// <summary>
        /// 执行开始事件
        /// </summary>
        public event EventHandler<ScheduleExecutionEventArgs>? ExecutionStarted;

        /// <summary>
        /// 执行完成事件
        /// </summary>
        public event EventHandler<ScheduleExecutionEventArgs>? ExecutionCompleted;

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置主窗体引用
        /// </summary>
        /// <param name="mainForm">主窗体</param>
        public void SetMainForm(Form mainForm)
        {
            _mainForm = mainForm;
        }

        /// <summary>
        /// 启动定时处理
        /// </summary>
        /// <param name="config">定时处理配置</param>
        public bool StartScheduledProcessing(ScheduleProcessingConfig config)
        {
            try
            {
                if (_isRunning)
                {
                    return false; // 已在运行中
                }

                _currentConfig = config;
                _currentRunCount = 0;

                switch (config.ScheduleType)
                {
                    case ScheduleType.OneTime:
                        StartOneTimeProcessing(config);
                        break;
                    case ScheduleType.Recurring:
                        StartRecurringProcessing(config);
                        break;
                    case ScheduleType.Countdown:
                        StartCountdownProcessing(config);
                        break;
                    default:
                        return false;
                }

                _isRunning = true;
                _statusTimer?.Start();
                
                OnStatusChanged("定时处理已启动", false);
                return true;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"启动定时处理失败：{ex.Message}", true);
                return false;
            }
        }

        /// <summary>
        /// 停止定时处理
        /// </summary>
        public void StopScheduledProcessing()
        {
            try
            {
                // 停止所有定时器
                _oneTimeTimer?.Stop();
                _recurringTimer?.Stop();
                _countdownTimer?.Stop();
                _statusTimer?.Stop();

                _isRunning = false;
                _currentConfig = null;
                CurrentModeDescription = "";
                
                OnStatusChanged("定时处理已停止", false);
            }
            catch (Exception ex)
            {
                OnStatusChanged($"停止定时处理失败：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 获取当前状态描述
        /// </summary>
        public string GetCurrentStatusDescription()
        {
            if (!_isRunning)
            {
                return "定时处理未运行";
            }

            try
            {
                DateTime now = DateTime.Now;
                TimeSpan remaining = _nextRunTime - now;

                if (remaining.TotalSeconds > 0)
                {
                    string timeText = "";
                    if (remaining.Days > 0)
                        timeText += $"{remaining.Days}天";
                    if (remaining.Hours > 0)
                        timeText += $"{remaining.Hours}小时";
                    if (remaining.Minutes > 0)
                        timeText += $"{remaining.Minutes}分";
                    timeText += $"{remaining.Seconds}秒";

                    return $"{CurrentModeDescription} - 距离下次执行还有 {timeText}";
                }
                else
                {
                    return $"{CurrentModeDescription} - 准备执行";
                }
            }
            catch
            {
                return $"{CurrentModeDescription} - 运行中";
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimers()
        {
            _statusTimer = new System.Windows.Forms.Timer();
            _statusTimer.Interval = 1000; // 每秒更新一次
            _statusTimer.Tick += StatusTimer_Tick;
        }

        /// <summary>
        /// 启动一次性处理
        /// </summary>
        private void StartOneTimeProcessing(ScheduleProcessingConfig config)
        {
            DateTime targetTime = config.OneTimeExecutionTime;
            TimeSpan delay = targetTime - DateTime.Now;

            if (delay.TotalMilliseconds <= 0)
            {
                throw new ArgumentException("执行时间不能早于当前时间");
            }

            _oneTimeTimer = new System.Windows.Forms.Timer();
            _oneTimeTimer.Interval = (int)Math.Min(delay.TotalMilliseconds, int.MaxValue);
            _oneTimeTimer.Tick += OneTimeTimer_Tick;
            _oneTimeTimer.Start();

            _nextRunTime = targetTime;
            CurrentModeDescription = "一次性启动";
        }

        /// <summary>
        /// 启动指定时间处理
        /// </summary>
        private void StartRecurringProcessing(ScheduleProcessingConfig config)
        {
            _recurringTimer = new System.Windows.Forms.Timer();
            _recurringTimer.Interval = 60000; // 每分钟检查一次
            _recurringTimer.Tick += RecurringTimer_Tick;
            _recurringTimer.Start();

            // 计算首次执行时间
            CalculateFirstRecurringTime(config);
            CurrentModeDescription = GetRecurringModeDescription(config);
        }

        /// <summary>
        /// 启动倒计时处理
        /// </summary>
        private void StartCountdownProcessing(ScheduleProcessingConfig config)
        {
            _nextRunTime = config.CountdownFirstExecutionTime;

            _countdownTimer = new System.Windows.Forms.Timer();
            _countdownTimer.Interval = 10000; // 每10秒检查一次
            _countdownTimer.Tick += CountdownTimer_Tick;
            _countdownTimer.Start();

            CurrentModeDescription = "倒计时启动";
        }

        #endregion

        #region 定时器事件处理

        /// <summary>
        /// 状态定时器事件
        /// </summary>
        private void StatusTimer_Tick(object? sender, EventArgs e)
        {
            if (_isRunning)
            {
                OnStatusChanged(GetCurrentStatusDescription(), false);
            }
        }

        /// <summary>
        /// 一次性启动定时器事件
        /// </summary>
        private void OneTimeTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                // 停止定时器
                _oneTimeTimer?.Stop();

                // 执行处理
                ExecuteProcessing("一次性启动");

                // 停止定时处理
                StopScheduledProcessing();
            }
            catch (Exception ex)
            {
                OnStatusChanged($"一次性启动执行失败：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 指定时间启动定时器事件
        /// </summary>
        private void RecurringTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                if (_currentConfig == null) return;

                DateTime now = DateTime.Now;

                // 检查是否到达执行时间
                if (ShouldExecuteRecurring(now))
                {
                    // 执行处理
                    ExecuteProcessing("指定时间启动");

                    // 计算下次执行时间
                    CalculateNextRecurringTime(_currentConfig);

                    // 检查是否需要停止
                    if (ShouldStopExecution())
                    {
                        StopScheduledProcessing();
                    }
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"指定时间启动执行失败：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 倒计时启动定时器事件
        /// </summary>
        private void CountdownTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                if (_currentConfig == null) return;

                DateTime now = DateTime.Now;

                // 检查是否到达执行时间
                if (now >= _nextRunTime)
                {
                    // 执行处理
                    ExecuteProcessing("倒计时启动");

                    // 计算下次执行时间
                    CalculateNextCountdownTime(_currentConfig);

                    // 检查是否需要停止
                    if (ShouldStopExecution())
                    {
                        StopScheduledProcessing();
                    }
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"倒计时启动执行失败：{ex.Message}", true);
            }
        }

        #endregion

        #region 执行逻辑

        /// <summary>
        /// 执行处理
        /// </summary>
        /// <param name="triggerType">触发类型</param>
        private void ExecuteProcessing(string triggerType)
        {
            try
            {
                _currentRunCount++;
                OnExecutionStarted(triggerType, _currentRunCount);

                // 调用主窗体的开始处理方法
                if (_mainForm != null)
                {
                    if (_mainForm.InvokeRequired)
                    {
                        _mainForm.Invoke(new Action(() => {
                            ExecuteMainFormProcessing(triggerType);
                        }));
                    }
                    else
                    {
                        ExecuteMainFormProcessing(triggerType);
                    }
                }
                else
                {
                    OnStatusChanged($"{triggerType} - 执行失败：主窗体引用为空", true);
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"{triggerType} - 第{_currentRunCount}次执行失败：{ex.Message}", true);
            }
        }

        /// <summary>
        /// 执行主窗体处理方法
        /// </summary>
        private void ExecuteMainFormProcessing(string triggerType)
        {
            try
            {
                if (_mainForm == null) return;

                // 通过反射获取源路径和输出路径
                var sourcePathField = _mainForm.GetType().GetField("txtSourcePath",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                var outputPathField = _mainForm.GetType().GetField("txtOutputPath",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (sourcePathField?.GetValue(_mainForm) is TextBox sourceTextBox &&
                    outputPathField?.GetValue(_mainForm) is TextBox outputTextBox)
                {
                    // 检查主窗体是否有有效的路径设置
                    if (string.IsNullOrWhiteSpace(sourceTextBox.Text) ||
                        string.IsNullOrWhiteSpace(outputTextBox.Text))
                    {
                        OnStatusChanged($"{triggerType} - 执行失败：源路径或输出路径未设置", true);
                        return;
                    }

                    // 调用主窗体的处理方法
                    var startProcessingMethod = _mainForm.GetType().GetMethod("StartProcessing",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (startProcessingMethod != null)
                    {
                        startProcessingMethod.Invoke(_mainForm, null);
                        OnExecutionCompleted(triggerType, _currentRunCount, true);
                    }
                    else
                    {
                        OnStatusChanged($"{triggerType} - 执行失败：无法找到处理方法", true);
                    }
                }
                else
                {
                    OnStatusChanged($"{triggerType} - 执行失败：无法获取路径设置", true);
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"{triggerType} - 执行失败：{ex.Message}", true);
                OnExecutionCompleted(triggerType, _currentRunCount, false);
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 判断是否应该执行指定时间启动
        /// </summary>
        private bool ShouldExecuteRecurring(DateTime now)
        {
            if (_currentConfig == null) return false;

            // 检查是否到达执行时间（精确到分钟，允许1分钟的误差范围）
            return now >= _nextRunTime && now < _nextRunTime.AddMinutes(1);
        }

        /// <summary>
        /// 计算首次指定时间启动时间
        /// </summary>
        private void CalculateFirstRecurringTime(ScheduleProcessingConfig config)
        {
            DateTime now = DateTime.Now;

            try
            {
                switch (config.RecurringType)
                {
                    case RecurringType.Yearly:
                        // 每年启动：检查今年的指定时间是否已过
                        var thisYearTime = new DateTime(now.Year, config.Month, config.Day,
                            config.Hour, config.Minute, config.Second);
                        _nextRunTime = thisYearTime > now ? thisYearTime :
                            new DateTime(now.Year + 1, config.Month, config.Day,
                                config.Hour, config.Minute, config.Second);
                        break;

                    case RecurringType.Monthly:
                        // 每月启动：检查本月的指定时间是否已过
                        var thisMonthTime = new DateTime(now.Year, now.Month,
                            Math.Min(config.Day, DateTime.DaysInMonth(now.Year, now.Month)),
                            config.Hour, config.Minute, config.Second);
                        if (thisMonthTime > now)
                        {
                            _nextRunTime = thisMonthTime;
                        }
                        else
                        {
                            var nextMonth = now.AddMonths(1);
                            _nextRunTime = new DateTime(nextMonth.Year, nextMonth.Month,
                                Math.Min(config.Day, DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month)),
                                config.Hour, config.Minute, config.Second);
                        }
                        break;

                    case RecurringType.Daily:
                        // 每天启动：检查今天的指定时间是否已过
                        var todayTime = now.Date.Add(new TimeSpan(config.Hour, config.Minute, config.Second));
                        _nextRunTime = todayTime > now ? todayTime : todayTime.AddDays(1);
                        break;

                    case RecurringType.Hourly:
                        // 每时启动：检查当前小时的指定时间是否已过
                        var thisHourTime = new DateTime(now.Year, now.Month, now.Day, now.Hour,
                            config.Minute, config.Second);
                        if (thisHourTime > now)
                        {
                            _nextRunTime = thisHourTime;
                        }
                        else
                        {
                            _nextRunTime = thisHourTime.AddHours(1);
                        }
                        break;
                }
            }
            catch (ArgumentOutOfRangeException)
            {
                // 如果日期计算出错，使用安全的默认值
                _nextRunTime = now.AddHours(1);
                OnStatusChanged("时间计算出现问题，已调整为1小时后执行", true);
            }
        }

        /// <summary>
        /// 计算下次指定时间启动时间
        /// </summary>
        private void CalculateNextRecurringTime(ScheduleProcessingConfig config)
        {
            try
            {
                switch (config.RecurringType)
                {
                    case RecurringType.Yearly:
                        // 每年启动：下一年的同一时间
                        _nextRunTime = _nextRunTime.AddYears(1);
                        // 处理闰年2月29日的情况
                        if (config.Month == 2 && config.Day == 29 && !DateTime.IsLeapYear(_nextRunTime.Year))
                        {
                            _nextRunTime = new DateTime(_nextRunTime.Year, 2, 28,
                                config.Hour, config.Minute, config.Second);
                        }
                        break;

                    case RecurringType.Monthly:
                        // 每月启动：下个月的同一时间
                        var nextMonth = _nextRunTime.AddMonths(1);
                        var targetDay = Math.Min(config.Day, DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month));
                        _nextRunTime = new DateTime(nextMonth.Year, nextMonth.Month, targetDay,
                            config.Hour, config.Minute, config.Second);
                        break;

                    case RecurringType.Daily:
                        // 每天启动：下一天的同一时间
                        _nextRunTime = _nextRunTime.AddDays(1);
                        break;

                    case RecurringType.Hourly:
                        // 每时启动：下一小时的同一时间
                        _nextRunTime = _nextRunTime.AddHours(1);
                        break;
                }
            }
            catch (ArgumentOutOfRangeException)
            {
                // 如果日期计算出错，使用安全的默认值
                _nextRunTime = DateTime.Now.AddHours(1);
                OnStatusChanged("时间计算出现问题，已调整为1小时后执行", true);
            }
        }

        /// <summary>
        /// 计算下次倒计时启动时间
        /// </summary>
        private void CalculateNextCountdownTime(ScheduleProcessingConfig config)
        {
            _nextRunTime = _nextRunTime.Add(config.CountdownInterval);
        }

        /// <summary>
        /// 判断是否应该停止执行
        /// </summary>
        private bool ShouldStopExecution()
        {
            if (_currentConfig == null) return true;

            // 检查运行次数限制
            if (_currentConfig.LimitationType == LimitationType.Count)
            {
                return _currentRunCount >= _currentConfig.LimitCount;
            }

            // 检查过期时间限制
            if (_currentConfig.LimitationType == LimitationType.Time)
            {
                return DateTime.Now >= _currentConfig.LimitTime;
            }

            // 无限制
            return false;
        }

        /// <summary>
        /// 获取指定时间启动模式描述
        /// </summary>
        private string GetRecurringModeDescription(ScheduleProcessingConfig config)
        {
            return config.RecurringType switch
            {
                RecurringType.Yearly => "每年启动",
                RecurringType.Monthly => "每月启动",
                RecurringType.Daily => "每天启动",
                RecurringType.Hourly => "每时启动",
                _ => "指定时间启动"
            };
        }

        #endregion

        #region 事件触发方法

        /// <summary>
        /// 触发状态变更事件
        /// </summary>
        private void OnStatusChanged(string message, bool isError)
        {
            StatusChanged?.Invoke(this, new ScheduleStatusChangedEventArgs(message, isError));
        }

        /// <summary>
        /// 触发执行开始事件
        /// </summary>
        private void OnExecutionStarted(string triggerType, int runCount)
        {
            ExecutionStarted?.Invoke(this, new ScheduleExecutionEventArgs(triggerType, runCount, true));
        }

        /// <summary>
        /// 触发执行完成事件
        /// </summary>
        private void OnExecutionCompleted(string triggerType, int runCount, bool success)
        {
            ExecutionCompleted?.Invoke(this, new ScheduleExecutionEventArgs(triggerType, runCount, success));
        }

        #endregion

        #region 资源释放

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                StopScheduledProcessing();

                _oneTimeTimer?.Dispose();
                _recurringTimer?.Dispose();
                _countdownTimer?.Dispose();
                _statusTimer?.Dispose();
            }
            catch
            {
                // 忽略释放资源时的错误
            }
        }

        #endregion
    }
}
