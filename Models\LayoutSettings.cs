using System.Drawing;

namespace PPTPiliangChuli.Models
{
    /// <summary>
    /// 布局设置模型类 - 包含PPT幻灯片布局的详细配置属性，支持标题、内容、两栏、图片和自定义布局设置
    /// </summary>
    public class LayoutSettings
    {
        /// <summary>
        /// 启用布局设置 - 控制是否启用整个布局设置功能
        /// </summary>
        public bool EnableLayout { get; set; } = false;

        /// <summary>
        /// 标题幻灯片布局设置
        /// </summary>
        public TitleSlideLayoutSettings TitleSlideLayout { get; set; } = new TitleSlideLayoutSettings();

        /// <summary>
        /// 内容布局设置
        /// </summary>
        public ContentLayoutSettings ContentLayout { get; set; } = new ContentLayoutSettings();

        /// <summary>
        /// 两栏布局设置
        /// </summary>
        public TwoColumnLayoutSettings TwoColumnLayout { get; set; } = new TwoColumnLayoutSettings();

        /// <summary>
        /// 图片布局设置
        /// </summary>
        public PictureLayoutSettings PictureLayout { get; set; } = new PictureLayoutSettings();

        /// <summary>
        /// 自定义布局设置
        /// </summary>
        public CustomLayoutSettings CustomLayout { get; set; } = new CustomLayoutSettings();
    }

    /// <summary>
    /// 标题幻灯片布局设置
    /// </summary>
    public class TitleSlideLayoutSettings
    {
        /// <summary>
        /// 启用标题布局 - 控制是否设置标题幻灯片的布局模板和样式
        /// </summary>
        public bool EnableTitleLayout { get; set; } = false;

        /// <summary>
        /// 布局模板：标题居中、标题左对齐、标题右对齐、标题顶部、标题底部、全屏标题、分栏标题、图文标题
        /// </summary>
        public string LayoutTemplate { get; set; } = "标题居中";

        /// <summary>
        /// 标题位置：顶部、上部、中部、下部、底部、自定义
        /// </summary>
        public string TitlePosition { get; set; } = "中部";

        /// <summary>
        /// 文字对齐：左对齐、居中对齐、右对齐、两端对齐、分散对齐、自定义
        /// </summary>
        public string TitleAlignment { get; set; } = "居中对齐";

        /// <summary>
        /// 标题字体
        /// </summary>
        public string TitleFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 标题字体大小
        /// </summary>
        public int TitleFontSize { get; set; } = 36;

        /// <summary>
        /// 标题颜色（十六进制格式）
        /// </summary>
        public string TitleColor { get; set; } = "#1F4E79";

        /// <summary>
        /// 标题是否粗体
        /// </summary>
        public bool TitleBold { get; set; } = true;

        /// <summary>
        /// 标题是否斜体
        /// </summary>
        public bool TitleItalic { get; set; } = false;

        /// <summary>
        /// 显示副标题
        /// </summary>
        public bool ShowSubtitle { get; set; } = true;

        /// <summary>
        /// 副标题字体
        /// </summary>
        public string SubtitleFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 副标题字体大小
        /// </summary>
        public int SubtitleFontSize { get; set; } = 18;

        /// <summary>
        /// 副标题颜色（十六进制格式）
        /// </summary>
        public string SubtitleColor { get; set; } = "#666666";

        /// <summary>
        /// 背景颜色（十六进制格式）
        /// </summary>
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 自动调整文字大小
        /// </summary>
        public bool AutoResizeText { get; set; } = true;

        /// <summary>
        /// 获取标题颜色的Color对象
        /// </summary>
        public Color GetTitleColor()
        {
            return ColorTranslator.FromHtml(TitleColor);
        }

        /// <summary>
        /// 获取副标题颜色的Color对象
        /// </summary>
        public Color GetSubtitleColor()
        {
            return ColorTranslator.FromHtml(SubtitleColor);
        }

        /// <summary>
        /// 获取背景颜色的Color对象
        /// </summary>
        public Color GetBackgroundColor()
        {
            return ColorTranslator.FromHtml(BackgroundColor);
        }
    }

    /// <summary>
    /// 内容布局设置
    /// </summary>
    public class ContentLayoutSettings
    {
        /// <summary>
        /// 启用内容布局 - 控制是否设置内容幻灯片的布局类型和边距设置
        /// </summary>
        public bool EnableContentLayout { get; set; } = false;

        /// <summary>
        /// 标题区域样式：标准标题、大标题、小标题、无标题、自定义标题、艺术标题
        /// </summary>
        public string TitleAreaStyle { get; set; } = "标准标题";

        /// <summary>
        /// 标题高度（像素）
        /// </summary>
        public int TitleHeight { get; set; } = 60;

        /// <summary>
        /// 内容对齐：顶部对齐、中部对齐、底部对齐、左对齐、右对齐、居中对齐
        /// </summary>
        public string ContentAlignment { get; set; } = "中部对齐";

        /// <summary>
        /// 布局类型：纯文本、文本和图片、图片和文本、纯图片、表格、图表、媒体、混合内容
        /// </summary>
        public string LayoutType { get; set; } = "纯文本";

        /// <summary>
        /// 布局样式：单栏布局、双栏布局、三栏布局、网格布局、流式布局、固定布局、响应式布局、自定义布局
        /// </summary>
        public string LayoutStyle { get; set; } = "单栏布局";

        /// <summary>
        /// 上边距（像素）
        /// </summary>
        public int MarginTop { get; set; } = 10;

        /// <summary>
        /// 左边距（像素）
        /// </summary>
        public int MarginLeft { get; set; } = 10;

        /// <summary>
        /// 右边距（像素）
        /// </summary>
        public int MarginRight { get; set; } = 10;

        /// <summary>
        /// 下边距（像素）
        /// </summary>
        public int MarginBottom { get; set; } = 10;

        /// <summary>
        /// 内容字体
        /// </summary>
        public string ContentFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 内容字体大小
        /// </summary>
        public int ContentFontSize { get; set; } = 14;

        /// <summary>
        /// 内容颜色（十六进制格式）
        /// </summary>
        public string ContentColor { get; set; } = "#000000";

        /// <summary>
        /// 行间距倍数
        /// </summary>
        public double LineSpacing { get; set; } = 1.2;

        /// <summary>
        /// 段落间距（像素）
        /// </summary>
        public int ParagraphSpacing { get; set; } = 6;

        /// <summary>
        /// 获取内容颜色的Color对象
        /// </summary>
        public Color GetContentColor()
        {
            return ColorTranslator.FromHtml(ContentColor);
        }
    }

    /// <summary>
    /// 两栏布局设置
    /// </summary>
    public class TwoColumnLayoutSettings
    {
        /// <summary>
        /// 启用两栏布局 - 控制是否设置两栏布局的栏宽度和间距
        /// </summary>
        public bool EnableTwoColumnLayout { get; set; } = false;

        /// <summary>
        /// 左栏宽度百分比（0-100）
        /// </summary>
        public int LeftColumnWidth { get; set; } = 50;

        /// <summary>
        /// 右栏宽度百分比（0-100）
        /// </summary>
        public int RightColumnWidth { get; set; } = 50;

        /// <summary>
        /// 栏间距（像素）
        /// </summary>
        public int ColumnSpacing { get; set; } = 20;

        /// <summary>
        /// 显示栏标题
        /// </summary>
        public bool ShowColumnTitles { get; set; } = false;

        /// <summary>
        /// 左栏标题
        /// </summary>
        public string LeftColumnTitle { get; set; } = "左栏内容";

        /// <summary>
        /// 右栏标题
        /// </summary>
        public string RightColumnTitle { get; set; } = "右栏内容";

        /// <summary>
        /// 栏内容字体
        /// </summary>
        public string ColumnFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 栏内容字体大小
        /// </summary>
        public int ColumnFontSize { get; set; } = 12;

        /// <summary>
        /// 栏内容颜色（十六进制格式）
        /// </summary>
        public string ColumnColor { get; set; } = "#000000";

        /// <summary>
        /// 栏标题字体
        /// </summary>
        public string TitleFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 栏标题字体大小
        /// </summary>
        public int TitleFontSize { get; set; } = 14;

        /// <summary>
        /// 栏标题颜色（十六进制格式）
        /// </summary>
        public string TitleColor { get; set; } = "#1F4E79";

        /// <summary>
        /// 栏标题是否粗体
        /// </summary>
        public bool TitleBold { get; set; } = true;

        /// <summary>
        /// 获取栏内容颜色的Color对象
        /// </summary>
        public Color GetColumnColor()
        {
            return ColorTranslator.FromHtml(ColumnColor);
        }

        /// <summary>
        /// 获取栏标题颜色的Color对象
        /// </summary>
        public Color GetTitleColor()
        {
            return ColorTranslator.FromHtml(TitleColor);
        }
    }

    /// <summary>
    /// 图片布局设置
    /// </summary>
    public class PictureLayoutSettings
    {
        /// <summary>
        /// 启用图片布局 - 控制是否设置图片布局的尺寸、位置和排列方式
        /// </summary>
        public bool EnablePictureLayout { get; set; } = false;

        /// <summary>
        /// 图片宽度（像素）
        /// </summary>
        public int PictureWidth { get; set; } = 300;

        /// <summary>
        /// 图片高度（像素）
        /// </summary>
        public int PictureHeight { get; set; } = 200;

        /// <summary>
        /// 图片位置：居中、左上角、右上角、左下角、右下角、左中、右中、上中、下中
        /// </summary>
        public string PicturePosition { get; set; } = "居中";

        /// <summary>
        /// 图片数量：单张、两张、三张、四张、多张
        /// </summary>
        public string PictureCount { get; set; } = "单张";

        /// <summary>
        /// 排列类型：网格排列、流式排列、瀑布流、自由排列
        /// </summary>
        public string ArrangementType { get; set; } = "网格排列";

        /// <summary>
        /// 图片间距（像素）
        /// </summary>
        public int PictureSpacing { get; set; } = 10;

        /// <summary>
        /// 行间距（像素）
        /// </summary>
        public int RowSpacing { get; set; } = 15;

        /// <summary>
        /// 列间距（像素）
        /// </summary>
        public int ColumnSpacing { get; set; } = 15;

        /// <summary>
        /// 显示边框
        /// </summary>
        public bool ShowBorder { get; set; } = false;

        /// <summary>
        /// 边框样式：实线、虚线、点线、双线
        /// </summary>
        public string BorderStyle { get; set; } = "实线";

        /// <summary>
        /// 边框宽度（像素）
        /// </summary>
        public int BorderWidth { get; set; } = 1;

        /// <summary>
        /// 边框颜色（十六进制格式）
        /// </summary>
        public string BorderColor { get; set; } = "#000000";

        /// <summary>
        /// 显示阴影
        /// </summary>
        public bool ShowShadow { get; set; } = false;

        /// <summary>
        /// 阴影颜色（十六进制格式）
        /// </summary>
        public string ShadowColor { get; set; } = "#808080";

        /// <summary>
        /// 图片说明字体
        /// </summary>
        public string CaptionFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 图片说明字体大小
        /// </summary>
        public int CaptionFontSize { get; set; } = 10;

        /// <summary>
        /// 图片说明颜色（十六进制格式）
        /// </summary>
        public string CaptionColor { get; set; } = "#666666";

        /// <summary>
        /// 图片说明位置：图片上方、图片下方、图片左侧、图片右侧
        /// </summary>
        public string CaptionPosition { get; set; } = "图片下方";

        /// <summary>
        /// 获取边框颜色的Color对象
        /// </summary>
        public Color GetBorderColor()
        {
            return ColorTranslator.FromHtml(BorderColor);
        }

        /// <summary>
        /// 获取阴影颜色的Color对象
        /// </summary>
        public Color GetShadowColor()
        {
            return ColorTranslator.FromHtml(ShadowColor);
        }

        /// <summary>
        /// 获取图片说明颜色的Color对象
        /// </summary>
        public Color GetCaptionColor()
        {
            return ColorTranslator.FromHtml(CaptionColor);
        }
    }

    /// <summary>
    /// 自定义布局设置
    /// </summary>
    public class CustomLayoutSettings
    {
        /// <summary>
        /// 启用自定义布局 - 控制是否设置自定义布局的名称、模板和样式
        /// </summary>
        public bool EnableCustomLayout { get; set; } = false;

        /// <summary>
        /// 布局名称
        /// </summary>
        public string LayoutName { get; set; } = "自定义布局";

        /// <summary>
        /// 基础模板：空白、标题、内容、两栏、图片、表格、图表
        /// </summary>
        public string BaseTemplate { get; set; } = "空白";

        /// <summary>
        /// 背景颜色（十六进制格式）
        /// </summary>
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 默认字体
        /// </summary>
        public string DefaultFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 默认字体大小
        /// </summary>
        public int DefaultFontSize { get; set; } = 14;

        /// <summary>
        /// 默认颜色（十六进制格式）
        /// </summary>
        public string DefaultColor { get; set; } = "#000000";

        /// <summary>
        /// 启用网格
        /// </summary>
        public bool GridEnabled { get; set; } = true;

        /// <summary>
        /// 网格间距（像素）
        /// </summary>
        public int GridSpacing { get; set; } = 10;

        /// <summary>
        /// 对齐到网格
        /// </summary>
        public bool SnapToGrid { get; set; } = true;

        /// <summary>
        /// 显示参考线
        /// </summary>
        public bool ShowGuides { get; set; } = true;

        /// <summary>
        /// 获取背景颜色的Color对象
        /// </summary>
        public Color GetBackgroundColor()
        {
            return ColorTranslator.FromHtml(BackgroundColor);
        }

        /// <summary>
        /// 获取默认颜色的Color对象
        /// </summary>
        public Color GetDefaultColor()
        {
            return ColorTranslator.FromHtml(DefaultColor);
        }
    }
}
