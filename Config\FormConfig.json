{
  // 窗体配置文件 - 控制主窗体的显示设置
  // 此文件定义了窗体大小、位置、状态和记忆功能等参数

  // 窗体宽度 - 主窗体的宽度（像素）
  "Width": 1200,

  // 窗体高度 - 主窗体的高度（像素）
  "Height": 800,

  // 窗体左边距 - 主窗体距离屏幕左边的距离（像素）
  "Left": 100,

  // 窗体上边距 - 主窗体距离屏幕顶部的距离（像素）
  "Top": 100,

  // 窗体状态 - 窗体的显示状态：0=正常，1=最小化，2=最大化
  "WindowState": 0,

  // 记住位置 - 是否记住窗体关闭时的位置，下次启动时恢复
  "RememberPosition": true,

  // 记住大小 - 是否记住窗体关闭时的大小，下次启动时恢复
  "RememberSize": true,

  // 居中启动 - 是否在屏幕中央启动窗体（优先级低于记住位置）
  "StartCentered": true
}
