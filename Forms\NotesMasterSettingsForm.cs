using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 备注母版设置窗体
    /// </summary>
    public partial class NotesMasterSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 备注母版设置
        /// </summary>
        public NotesMasterSettings Settings { get; private set; } = new NotesMasterSettings();

        #region 控件字段
        private ComboBox? _cmbNotesTextFont;
        private NumericUpDown? _nudNotesTextSize;
        private Button? _btnNotesTextColor;
        private ComboBox? _cmbSlidePosition;
        private NumericUpDown? _nudSlideWidth, _nudSlideHeight;
        private CheckBox? _chkShowHeader, _chkShowFooter, _chkShowPageNumber, _chkShowDate;
        private TextBox? _txtHeaderText, _txtFooterText;
        private ComboBox? _cmbDateFormat;
        private Button? _btnOK, _btnCancel, _btnReset;
        
        // 颜色存储
        private Color _notesTextColor = Color.Black;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public NotesMasterSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(NotesMasterSettingsForm));
            SuspendLayout();
            // 
            // NotesMasterSettingsForm
            // 
            ClientSize = new Size(684, 591);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            MinimumSize = new Size(700, 580);
            Name = "NotesMasterSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "备注母版设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateNotesTextGroup();
            CreateSlideLayoutGroup();
            CreateHeaderFooterGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建备注文本组
        /// </summary>
        private void CreateNotesTextGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "备注文本格式",
                Location = new Point(30, 30),
                Size = new Size(640, 120),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular)
            };

            var lblFont = new Label
            {
                Text = "字体:",
                Location = new Point(20, 40),
                Size = new Size(60, 30),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbNotesTextFont = new ComboBox
            {
                Location = new Point(100, 40),
                Size = new Size(240, 30),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 10,
                DropDownHeight = 200
            };

            var lblSize = new Label
            {
                Text = "大小:",
                Location = new Point(360, 40),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudNotesTextSize = new NumericUpDown
            {
                Location = new Point(450, 40),
                Size = new Size(100, 28),
                Minimum = 8,
                Maximum = 72,
                Value = 12,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblColor = new Label
            {
                Text = "颜色:",
                Location = new Point(20, 80),
                Size = new Size(80, 40),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnNotesTextColor = new Button
            {
                Location = new Point(110, 80),
                Size = new Size(110, 40),
                BackColor = _notesTextColor,
                Text = "选择颜色",
                Font = new Font("Microsoft YaHei UI", 10F),
                FlatStyle = FlatStyle.Standard
            };
            _btnNotesTextColor.Click += BtnNotesTextColor_Click;

            groupBox.Controls.AddRange(new Control[] {
                lblFont, _cmbNotesTextFont, lblSize, _nudNotesTextSize, lblColor, _btnNotesTextColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建幻灯片布局组
        /// </summary>
        private void CreateSlideLayoutGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "幻灯片缩略图设置",
                Location = new Point(30, 170),
                Size = new Size(640, 120),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular)
            };

            var lblPosition = new Label
            {
                Text = "位置:",
                Location = new Point(20, 40),
                Size = new Size(60, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSlidePosition = new ComboBox
            {
                Location = new Point(100, 38),
                Size = new Size(120, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 8,
                DropDownHeight = 160
            };

            var lblWidth = new Label
            {
                Text = "宽度:",
                Location = new Point(220, 40),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudSlideWidth = new NumericUpDown
            {
                Location = new Point(310, 38),
                Size = new Size(150, 28),
                Minimum = 50,
                Maximum = 500,
                Value = 200,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblHeight = new Label
            {
                Text = "高度:",
                Location = new Point(20, 80),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudSlideHeight = new NumericUpDown
            {
                Location = new Point(480, 38),
                Size = new Size(80, 28),
                Minimum = 50,
                Maximum = 500,
                Value = 150,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblPosition, _cmbSlidePosition, lblWidth, _nudSlideWidth, lblHeight, _nudSlideHeight
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建页眉页脚组
        /// </summary>
        private void CreateHeaderFooterGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "页眉页脚设置",
                Location = new Point(30, 310),
                Size = new Size(640, 180),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular)
            };

            // 第一行复选框
            _chkShowHeader = new CheckBox
            {
                Text = "显示页眉",
                Location = new Point(30, 35),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _chkShowFooter = new CheckBox
            {
                Text = "显示页脚",
                Location = new Point(170, 35),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _chkShowPageNumber = new CheckBox
            {
                Text = "显示页码",
                Location = new Point(310, 35),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _chkShowDate = new CheckBox
            {
                Text = "显示日期",
                Location = new Point(450, 35),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            // 页眉文本
            var lblHeader = new Label
            {
                Text = "页眉文本:",
                Location = new Point(20, 85),
                Size = new Size(120, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtHeaderText = new TextBox
            {
                Location = new Point(140, 73),
                Size = new Size(440, 28),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            // 页脚文本
            var lblFooter = new Label
            {
                Text = "页脚文本:",
                Location = new Point(20, 115),
                Size = new Size(120, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtFooterText = new TextBox
            {
                Location = new Point(140, 113),
                Size = new Size(240, 28),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblDateFormat = new Label
            {
                Text = "日期格式:",
                Location = new Point(400, 115),
                Size = new Size(100, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbDateFormat = new ComboBox
            {
                Location = new Point(510, 113),
                Size = new Size(90, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            groupBox.Controls.AddRange(new Control[] {
                _chkShowHeader, _chkShowFooter, _chkShowPageNumber, _chkShowDate,
                lblHeader, _txtHeaderText, lblFooter, _txtFooterText, lblDateFormat, _cmbDateFormat
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button
            {
                Text = "确定(&O)",
                Location = new Point(375, 510),
                Size = new Size(90, 40),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消(&C)",
                Location = new Point(485, 510),
                Size = new Size(90, 40),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _btnReset = new Button
            {
                Text = "重置(&R)",
                Location = new Point(590, 510),
                Size = new Size(80, 40),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 加载字体列表
            var commonFonts = new string[]
            {
                "Microsoft YaHei UI", "Microsoft YaHei", "SimSun", "SimHei", "KaiTi",
                "Arial", "Times New Roman", "Calibri", "Verdana", "Tahoma"
            };
            _cmbNotesTextFont?.Items.AddRange(commonFonts);
            if (_cmbNotesTextFont != null)
                _cmbNotesTextFont.SelectedItem = "Microsoft YaHei UI";

            // 加载位置选项
            var positions = new string[] { "上方", "下方", "左侧", "右侧" };
            _cmbSlidePosition?.Items.AddRange(positions);
            if (_cmbSlidePosition != null)
                _cmbSlidePosition.SelectedItem = "上方";

            // 加载日期格式
            var dateFormats = new string[] { "短", "长", "无" };
            _cmbDateFormat?.Items.AddRange(dateFormats);
            if (_cmbDateFormat != null)
                _cmbDateFormat.SelectedItem = "短";
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 备注文本颜色按钮点击事件
        /// </summary>
        private void BtnNotesTextColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = _notesTextColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _notesTextColor = colorDialog.Color;
                if (_btnNotesTextColor != null)
                    _btnNotesTextColor.BackColor = _notesTextColor;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new NotesMasterSettings
                {
                    NotesTextFont = _cmbNotesTextFont?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    NotesTextSize = (int)(_nudNotesTextSize?.Value ?? 12),
                    NotesTextColor = _notesTextColor,
                    SlidePosition = _cmbSlidePosition?.SelectedItem?.ToString() ?? "上方",
                    SlideWidth = (int)(_nudSlideWidth?.Value ?? 200),
                    SlideHeight = (int)(_nudSlideHeight?.Value ?? 150),
                    ShowHeader = _chkShowHeader?.Checked ?? false,
                    ShowFooter = _chkShowFooter?.Checked ?? false,
                    ShowPageNumber = _chkShowPageNumber?.Checked ?? false,
                    ShowDate = _chkShowDate?.Checked ?? false,
                    HeaderText = _txtHeaderText?.Text ?? "",
                    FooterText = _txtFooterText?.Text ?? "",
                    DateFormat = _cmbDateFormat?.SelectedItem?.ToString() ?? "短"
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();
            _notesTextColor = Color.Black;
            if (_btnNotesTextColor != null)
                _btnNotesTextColor.BackColor = _notesTextColor;
        }

        #endregion
    }

    /// <summary>
    /// 备注母版设置类
    /// </summary>
    public class NotesMasterSettings
    {
        public string NotesTextFont { get; set; } = "Microsoft YaHei UI";
        public int NotesTextSize { get; set; } = 12;
        public Color NotesTextColor { get; set; } = Color.Black;
        public string SlidePosition { get; set; } = "上方";
        public int SlideWidth { get; set; } = 200;
        public int SlideHeight { get; set; } = 150;
        public bool ShowHeader { get; set; } = false;
        public bool ShowFooter { get; set; } = false;
        public bool ShowPageNumber { get; set; } = false;
        public bool ShowDate { get; set; } = false;
        public string HeaderText { get; set; } = "";
        public string FooterText { get; set; } = "";
        public string DateFormat { get; set; } = "短";
    }
}
