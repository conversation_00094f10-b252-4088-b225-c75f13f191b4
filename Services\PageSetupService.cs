// PPT页面设置处理服务
// 功能：处理PPT页面尺寸、方向、背景等设置，支持多种单位转换和预设尺寸
// 作者：PPT批量处理工具开发团队
// 创建时间：2024年
// 最后修改：2024年

using System;
using System.Threading.Tasks;
using Aspose.Slides;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 页面设置处理服务
    /// </summary>
    public class PageSetupService
    {
        /// <summary>
        /// 应用页面设置
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="settings">页面设置</param>
        /// <returns>处理结果</returns>
        public async Task<ProcessingResult> ApplyPageSetupAsync(IPresentation presentation, PageSetupSettings settings)
        {
            try
            {
                var result = new ProcessingResult { IsSuccess = true };

                await Task.Run(() =>
                {
                    // 获取幻灯片尺寸（Aspose.Slides中的尺寸单位是点，1英寸=72点，1厘米=28.35点）
                    float widthInPoints = ConvertToPoints(settings.Width, settings.Unit);
                    float heightInPoints = ConvertToPoints(settings.Height, settings.Unit);

                    // 设置幻灯片尺寸
                    presentation.SlideSize.SetSize(widthInPoints, heightInPoints, SlideSizeScaleType.DoNotScale);

                    // 设置方向
                    if (settings.IsLandscape)
                    {
                        // 横向：确保宽度大于高度
                        if (widthInPoints < heightInPoints)
                        {
                            presentation.SlideSize.SetSize(heightInPoints, widthInPoints, SlideSizeScaleType.DoNotScale);
                        }
                    }
                    else
                    {
                        // 纵向：确保高度大于宽度
                        if (heightInPoints < widthInPoints)
                        {
                            presentation.SlideSize.SetSize(heightInPoints, widthInPoints, SlideSizeScaleType.DoNotScale);
                        }
                    }

                    // 应用背景设置
                    if (settings.ApplyToAllSlides && settings.BackgroundSettings != null)
                    {
                        ApplyBackgroundSettings(presentation, settings.BackgroundSettings);
                    }

                    result.Message = $"页面设置已应用: {settings.GetSizeDescription()}";
                });

                return result;
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"页面设置处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 将尺寸转换为点单位
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="unit">单位</param>
        /// <returns>点单位的数值</returns>
        private float ConvertToPoints(float value, string unit)
        {
            return unit switch
            {
                "厘米" => value * 28.35f,      // 1厘米 = 28.35点
                "英寸" => value * 72f,         // 1英寸 = 72点
                "毫米" => value * 2.835f,      // 1毫米 = 2.835点
                "像素" => value * 0.75f,       // 1像素 = 0.75点（96DPI）
                "点" => value,                 // 点就是点
                _ => value * 28.35f            // 默认按厘米处理
            };
        }

        /// <summary>
        /// 应用预设尺寸
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="sizeType">尺寸类型</param>
        /// <param name="isLandscape">是否横向</param>
        public void ApplyPresetSize(IPresentation presentation, string sizeType, bool isLandscape)
        {
            float width, height;

            // 根据尺寸类型设置默认尺寸（单位：点）
            switch (sizeType)
            {
                case "Standard":
                    width = 720f;   // 10英寸
                    height = 540f;  // 7.5英寸
                    break;
                case "Widescreen":
                    width = 960f;   // 13.33英寸
                    height = 540f;  // 7.5英寸
                    break;
                case "A4":
                    width = 595f;   // A4宽度
                    height = 842f;  // A4高度
                    break;
                case "Letter":
                    width = 612f;   // Letter宽度
                    height = 792f;  // Letter高度
                    break;
                default:
                    width = 720f;
                    height = 540f;
                    break;
            }

            // 根据方向调整
            if (!isLandscape && width > height)
            {
                // 纵向：交换宽高
                (width, height) = (height, width);
            }
            else if (isLandscape && height > width)
            {
                // 横向：交换宽高
                (width, height) = (height, width);
            }

            presentation.SlideSize.SetSize(width, height, SlideSizeScaleType.DoNotScale);
        }

        /// <summary>
        /// 应用背景设置
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="backgroundSettings">背景设置</param>
        private void ApplyBackgroundSettings(IPresentation presentation, BackgroundSettings backgroundSettings)
        {
            try
            {
                foreach (var slide in presentation.Slides)
                {
                    switch (backgroundSettings.BackgroundType)
                    {
                        case "None":
                            // 无背景 - 使用默认背景
                            slide.Background.Type = BackgroundType.OwnBackground;
                            slide.Background.FillFormat.FillType = FillType.Solid;
                            slide.Background.FillFormat.SolidFillColor.Color = System.Drawing.Color.White;
                            break;

                        case "SolidColor":
                            // 纯色背景
                            slide.Background.Type = BackgroundType.OwnBackground;
                            slide.Background.FillFormat.FillType = FillType.Solid;
                            var solidColor = System.Drawing.ColorTranslator.FromHtml(backgroundSettings.BackgroundColor);
                            slide.Background.FillFormat.SolidFillColor.Color = solidColor;
                            break;

                        case "Gradient":
                            // 渐变背景
                            slide.Background.Type = BackgroundType.OwnBackground;
                            slide.Background.FillFormat.FillType = FillType.Gradient;
                            var gradientFormat = slide.Background.FillFormat.GradientFormat;
                            gradientFormat.GradientStops.Clear();

                            var startColor = System.Drawing.ColorTranslator.FromHtml(backgroundSettings.GradientStartColor);
                            var endColor = System.Drawing.ColorTranslator.FromHtml(backgroundSettings.GradientEndColor);

                            gradientFormat.GradientStops.Add(0f, startColor);
                            gradientFormat.GradientStops.Add(1f, endColor);

                            // 设置渐变方向 - 根据Aspose.Slides API规范设置正确的渐变方向
                            gradientFormat.GradientDirection = backgroundSettings.GradientDirection switch
                            {
                                "Vertical" => GradientDirection.FromCorner1,    // 垂直渐变
                                "Diagonal" => GradientDirection.FromCorner2,    // 对角线渐变
                                "Horizontal" => GradientDirection.FromCorner1,  // 水平渐变
                                _ => GradientDirection.FromCorner1              // 默认水平渐变
                            };
                            break;

                        case "Image":
                            // 图片背景
                            if (!string.IsNullOrEmpty(backgroundSettings.ImagePath) &&
                                System.IO.File.Exists(backgroundSettings.ImagePath))
                            {
                                slide.Background.Type = BackgroundType.OwnBackground;
                                slide.Background.FillFormat.FillType = FillType.Picture;

                                var image = presentation.Images.AddImage(System.IO.File.ReadAllBytes(backgroundSettings.ImagePath));
                                slide.Background.FillFormat.PictureFillFormat.Picture.Image = image;

                                // 设置图片填充模式 - 根据Aspose.Slides API规范设置正确的图片填充方式
                                slide.Background.FillFormat.PictureFillFormat.PictureFillMode = backgroundSettings.ImageFillMode switch
                                {
                                    "平铺" => PictureFillMode.Tile,        // 平铺模式
                                    "居中" => PictureFillMode.Stretch,     // 拉伸模式（Aspose.Slides没有直接的居中模式）
                                    "适应" => PictureFillMode.Stretch,     // 拉伸模式
                                    "填充" => PictureFillMode.Stretch,     // 拉伸模式
                                    "拉伸" => PictureFillMode.Stretch,     // 拉伸模式
                                    _ => PictureFillMode.Stretch           // 默认拉伸模式
                                };
                            }
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断主要处理流程
                System.Diagnostics.Debug.WriteLine($"应用背景设置时出错: {ex.Message}");
            }
        }
    }
}
