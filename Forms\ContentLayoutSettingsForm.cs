using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容布局设置窗体
    /// </summary>
    public partial class ContentLayoutSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 内容布局设置
        /// </summary>
        public ContentLayoutSettings Settings { get; private set; } = new ContentLayoutSettings();

        #region 控件字段
        private ComboBox? _cmbTitlePosition, _cmbContentAlignment, _cmbLayoutType;
        private NumericUpDown? _nudTitleHeight, _nudContentMarginTop, _nudContentMarginLeft, _nudContentMarginRight, _nudContentMarginBottom;
        private CheckBox? _chkShowTitle, _chkAutoResize, _chkKeepAspectRatio;
        private ComboBox? _cmbTextImageLayout;
        private NumericUpDown? _nudImageWidth, _nudImageHeight;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ContentLayoutSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            // 设置窗体图标 - 使用项目根目录的favicon.ico文件
            try
            {
                this.Icon = new Icon("favicon.ico");
            }
            catch
            {
                // 如果图标文件不存在，忽略错误
            }

            this.Text = "内容布局设置";
            this.Size = new Size(750, 650);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.ShowIcon = true;  // 显示图标
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Font = new Font("Microsoft YaHei UI", 10F);
            this.MinimumSize = new Size(750, 650);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateTitleAreaGroup();
            CreateContentAreaGroup();
            CreateTextImageLayoutGroup();
            CreateOptionsGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建标题区域组
        /// </summary>
        private void CreateTitleAreaGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "标题区域设置",
                Location = new Point(30, 30),
                Size = new Size(690, 120),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            _chkShowTitle = new CheckBox
            {
                Text = "显示标题",
                Location = new Point(30, 40),
                Size = new Size(120, 25),
                Checked = true,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblPosition = new Label
            {
                Text = "标题位置:",
                Location = new Point(170, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitlePosition = new ComboBox
            {
                Location = new Point(270, 38),
                Size = new Size(140, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            var lblHeight = new Label
            {
                Text = "标题高度:",
                Location = new Point(430, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudTitleHeight = new NumericUpDown
            {
                Location = new Point(530, 38),
                Size = new Size(100, 28),
                Minimum = 20,
                Maximum = 200,
                Value = 60,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                _chkShowTitle, lblPosition, _cmbTitlePosition, lblHeight, _nudTitleHeight
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建内容区域组
        /// </summary>
        private void CreateContentAreaGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "内容区域设置",
                Location = new Point(30, 170),
                Size = new Size(690, 160),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            // 第一行：内容对齐和布局类型
            var lblAlignment = new Label
            {
                Text = "内容对齐:",
                Location = new Point(30, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbContentAlignment = new ComboBox
            {
                Location = new Point(130, 38),
                Size = new Size(120, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            var lblLayout = new Label
            {
                Text = "布局类型:",
                Location = new Point(270, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbLayoutType = new ComboBox
            {
                Location = new Point(370, 38),
                Size = new Size(140, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            // 第二行：边距设置
            var lblMargins = new Label
            {
                Text = "内容边距:",
                Location = new Point(30, 80),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblTop = new Label
            {
                Text = "上:",
                Location = new Point(130, 80),
                Size = new Size(30, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudContentMarginTop = new NumericUpDown
            {
                Location = new Point(165, 78),
                Size = new Size(70, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 10,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblLeft = new Label
            {
                Text = "左:",
                Location = new Point(250, 80),
                Size = new Size(30, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudContentMarginLeft = new NumericUpDown
            {
                Location = new Point(285, 78),
                Size = new Size(70, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 10,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblRight = new Label
            {
                Text = "右:",
                Location = new Point(370, 80),
                Size = new Size(30, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudContentMarginRight = new NumericUpDown
            {
                Location = new Point(405, 78),
                Size = new Size(70, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 10,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblBottom = new Label
            {
                Text = "下:",
                Location = new Point(490, 80),
                Size = new Size(30, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudContentMarginBottom = new NumericUpDown
            {
                Location = new Point(525, 78),
                Size = new Size(70, 28),
                Minimum = 0,
                Maximum = 100,
                Value = 10,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblAlignment, _cmbContentAlignment, lblLayout, _cmbLayoutType,
                lblMargins, lblTop, _nudContentMarginTop, lblLeft, _nudContentMarginLeft,
                lblRight, _nudContentMarginRight, lblBottom, _nudContentMarginBottom
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建文本图片布局组
        /// </summary>
        private void CreateTextImageLayoutGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "文本图片布局设置",
                Location = new Point(30, 350),
                Size = new Size(690, 140),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            // 第一行：布局方式
            var lblTextImage = new Label
            {
                Text = "布局方式:",
                Location = new Point(30, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTextImageLayout = new ComboBox
            {
                Location = new Point(130, 38),
                Size = new Size(180, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 8,
                DropDownHeight = 160
            };

            // 第二行：图片尺寸设置
            var lblImageSize = new Label
            {
                Text = "图片尺寸:",
                Location = new Point(30, 80),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblWidth = new Label
            {
                Text = "宽:",
                Location = new Point(130, 80),
                Size = new Size(40, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudImageWidth = new NumericUpDown
            {
                Location = new Point(175, 78),
                Size = new Size(80, 28),
                Minimum = 50,
                Maximum = 800,
                Value = 300,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblHeight = new Label
            {
                Text = "高:",
                Location = new Point(275, 80),
                Size = new Size(40, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            _nudImageHeight = new NumericUpDown
            {
                Location = new Point(320, 78),
                Size = new Size(80, 28),
                Minimum = 50,
                Maximum = 600,
                Value = 200,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _chkKeepAspectRatio = new CheckBox
            {
                Text = "保持宽高比",
                Location = new Point(420, 80),
                Size = new Size(120, 25),
                Checked = true,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblTextImage, _cmbTextImageLayout, lblImageSize, lblWidth, _nudImageWidth,
                lblHeight, _nudImageHeight, _chkKeepAspectRatio
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建选项组
        /// </summary>
        private void CreateOptionsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "其他选项设置",
                Location = new Point(30, 510),
                Size = new Size(690, 80),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            _chkAutoResize = new CheckBox
            {
                Text = "自动调整内容大小",
                Location = new Point(30, 40),
                Size = new Size(180, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _btnPreview = new Button
            {
                Text = "预览效果(&P)",
                Location = new Point(580, 38),
                Size = new Size(100, 28),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnPreview.Click += BtnPreview_Click;

            groupBox.Controls.AddRange(new Control[] { _chkAutoResize, _btnPreview });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button
            {
                Text = "确定(&O)",
                Location = new Point(450, 610),
                Size = new Size(90, 35),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消(&C)",
                Location = new Point(555, 610),
                Size = new Size(90, 35),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _btnReset = new Button
            {
                Text = "重置(&R)",
                Location = new Point(660, 610),
                Size = new Size(80, 35),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 标题位置选项
            var titlePositions = new string[] { "顶部居中", "顶部左对齐", "顶部右对齐", "隐藏标题" };
            _cmbTitlePosition?.Items.AddRange(titlePositions);
            if (_cmbTitlePosition != null)
                _cmbTitlePosition.SelectedItem = "顶部居中";

            // 内容对齐选项
            var alignments = new string[] { "左对齐", "居中对齐", "右对齐", "两端对齐" };
            _cmbContentAlignment?.Items.AddRange(alignments);
            if (_cmbContentAlignment != null)
                _cmbContentAlignment.SelectedItem = "左对齐";

            // 布局类型选项
            var layoutTypes = new string[] { "标准内容", "标题和内容", "仅内容", "空白布局" };
            _cmbLayoutType?.Items.AddRange(layoutTypes);
            if (_cmbLayoutType != null)
                _cmbLayoutType.SelectedItem = "标题和内容";

            // 文本图片布局选项
            var textImageLayouts = new string[] { "文本在左图片在右", "图片在左文本在右", "文本在上图片在下", "图片在上文本在下", "仅文本", "仅图片" };
            _cmbTextImageLayout?.Items.AddRange(textImageLayouts);
            if (_cmbTextImageLayout != null)
                _cmbTextImageLayout.SelectedItem = "文本在左图片在右";
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能显示当前布局设置的效果。\n\n" +
                           "在实际应用中，这里会显示布局预览图。", "布局预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new ContentLayoutSettings
                {
                    ShowTitle = _chkShowTitle?.Checked ?? true,
                    TitlePosition = _cmbTitlePosition?.SelectedItem?.ToString() ?? "顶部居中",
                    TitleHeight = (int)(_nudTitleHeight?.Value ?? 60),
                    ContentAlignment = _cmbContentAlignment?.SelectedItem?.ToString() ?? "左对齐",
                    LayoutType = _cmbLayoutType?.SelectedItem?.ToString() ?? "标题和内容",
                    ContentMarginTop = (int)(_nudContentMarginTop?.Value ?? 10),
                    ContentMarginLeft = (int)(_nudContentMarginLeft?.Value ?? 10),
                    ContentMarginRight = (int)(_nudContentMarginRight?.Value ?? 10),
                    ContentMarginBottom = (int)(_nudContentMarginBottom?.Value ?? 10),
                    TextImageLayout = _cmbTextImageLayout?.SelectedItem?.ToString() ?? "文本在左图片在右",
                    ImageWidth = (int)(_nudImageWidth?.Value ?? 300),
                    ImageHeight = (int)(_nudImageHeight?.Value ?? 200),
                    KeepAspectRatio = _chkKeepAspectRatio?.Checked ?? true,
                    AutoResize = _chkAutoResize?.Checked ?? false
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();
            
            // 重置数值控件
            if (_nudTitleHeight != null) _nudTitleHeight.Value = 60;
            if (_nudContentMarginTop != null) _nudContentMarginTop.Value = 10;
            if (_nudContentMarginLeft != null) _nudContentMarginLeft.Value = 10;
            if (_nudContentMarginRight != null) _nudContentMarginRight.Value = 10;
            if (_nudContentMarginBottom != null) _nudContentMarginBottom.Value = 10;
            if (_nudImageWidth != null) _nudImageWidth.Value = 300;
            if (_nudImageHeight != null) _nudImageHeight.Value = 200;
            
            // 重置复选框
            if (_chkShowTitle != null) _chkShowTitle.Checked = true;
            if (_chkKeepAspectRatio != null) _chkKeepAspectRatio.Checked = true;
            if (_chkAutoResize != null) _chkAutoResize.Checked = false;
        }

        #endregion
    }

    /// <summary>
    /// 内容布局设置类
    /// </summary>
    public class ContentLayoutSettings
    {
        public bool ShowTitle { get; set; } = true;
        public string TitlePosition { get; set; } = "顶部居中";
        public int TitleHeight { get; set; } = 60;
        public string ContentAlignment { get; set; } = "左对齐";
        public string LayoutType { get; set; } = "标题和内容";
        public int ContentMarginTop { get; set; } = 10;
        public int ContentMarginLeft { get; set; } = 10;
        public int ContentMarginRight { get; set; } = 10;
        public int ContentMarginBottom { get; set; } = 10;
        public string TextImageLayout { get; set; } = "文本在左图片在右";
        public int ImageWidth { get; set; } = 300;
        public int ImageHeight { get; set; } = 200;
        public bool KeepAspectRatio { get; set; } = true;
        public bool AutoResize { get; set; } = false;
    }
}
