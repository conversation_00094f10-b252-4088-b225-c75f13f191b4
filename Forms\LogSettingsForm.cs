using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 日志设置窗体
    /// </summary>
    public partial class LogSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 日志设置
        /// </summary>
        public LogSettings LogSettings { get; private set; }

        /// <summary>
        /// 日志类型复选框字典
        /// </summary>
        private readonly Dictionary<string, CheckBox> _logTypeCheckBoxes;

        /// <summary>
        /// 日志类型信息列表
        /// </summary>
        private readonly List<LogTypeInfo> _logTypes;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化日志设置窗体
        /// </summary>
        /// <param name="logSettings">当前日志设置</param>
        public LogSettingsForm(LogSettings logSettings)
        {
            InitializeComponent();
            
            // 深拷贝日志设置，避免直接修改原对象
            LogSettings = new LogSettings
            {
                EnableLogging = logSettings.EnableLogging,
                LogLevel = logSettings.LogLevel,
                EnabledLogTypes = new Dictionary<string, bool>(logSettings.EnabledLogTypes),
                MaxLogFileSizeMB = logSettings.MaxLogFileSizeMB,
                LogRetentionDays = logSettings.LogRetentionDays
            };

            _logTypeCheckBoxes = new Dictionary<string, CheckBox>();
            _logTypes = GetLogTypes();
            
            InitializeLogTypeControls();
            LoadCurrentSettings();
            ApplyModernStyle();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取日志类型列表
        /// </summary>
        private List<LogTypeInfo> GetLogTypes()
        {
            return new List<LogTypeInfo>
            {
                new LogTypeInfo { Name = "处理开始", Description = "记录文件处理开始的日志", IsImportant = true },
                new LogTypeInfo { Name = "处理完成", Description = "记录文件处理完成的日志", IsImportant = true },
                new LogTypeInfo { Name = "处理错误", Description = "记录处理过程中发生的错误", IsImportant = true },
                new LogTypeInfo { Name = "文件操作", Description = "记录文件读取、写入等操作", IsImportant = true },
                new LogTypeInfo { Name = "配置变更", Description = "记录配置文件的变更操作", IsImportant = false },
                new LogTypeInfo { Name = "调试信息", Description = "记录详细的调试信息", IsImportant = false },
                new LogTypeInfo { Name = "性能监控", Description = "记录处理性能和耗时信息", IsImportant = false },
                new LogTypeInfo { Name = "用户操作", Description = "记录用户的界面操作", IsImportant = false },
                new LogTypeInfo { Name = "系统状态", Description = "记录系统运行状态信息", IsImportant = false },
                new LogTypeInfo { Name = "网络通信", Description = "记录网络相关的操作", IsImportant = false }
            };
        }

        /// <summary>
        /// 初始化日志类型控件
        /// </summary>
        private void InitializeLogTypeControls()
        {
            int yPosition = 25; // 增加顶部间距
            const int itemHeight = 55; // 大幅增加行高，使布局更宽松
            const int leftMargin = 30; // 增加左边距

            foreach (var logType in _logTypes)
            {
                // 确保日志设置中包含此类型
                if (!LogSettings.EnabledLogTypes.ContainsKey(logType.Name))
                {
                    LogSettings.EnabledLogTypes[logType.Name] = logType.IsImportant;
                }

                var checkBox = new CheckBox
                {
                    Name = $"chk{logType.Name}",
                    Text = $"{logType.Name} - {logType.Description}",
                    Location = new Point(leftMargin, yPosition),
                    Size = new Size(640, 45), // 大幅增加宽度和高度
                    Checked = LogSettings.EnabledLogTypes[logType.Name],
                    Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular), // 增大字体到11F
                    ForeColor = logType.IsImportant ? Color.FromArgb(33, 37, 41) : Color.FromArgb(108, 117, 125),
                    AutoSize = false,
                    TextAlign = ContentAlignment.MiddleLeft,
                    Margin = new Padding(5, 8, 5, 8) // 增加边距
                };

                // 重要日志类型使用粗体
                if (logType.IsImportant)
                {
                    checkBox.Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold); // 增大字体到11F
                }

                panelLogTypes.Controls.Add(checkBox);
                _logTypeCheckBoxes[logType.Name] = checkBox;
                yPosition += itemHeight;
            }
        }

        /// <summary>
        /// 加载当前设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // 设置总开关
                chkEnableLogging.Checked = LogSettings.EnableLogging;
                
                // 设置日志类型勾选状态
                foreach (var kvp in _logTypeCheckBoxes)
                {
                    if (LogSettings.EnabledLogTypes.ContainsKey(kvp.Key))
                    {
                        kvp.Value.Checked = LogSettings.EnabledLogTypes[kvp.Key];
                    }
                }

                // 更新控件状态
                UpdateControlsState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                // 保存总开关状态
                LogSettings.EnableLogging = chkEnableLogging.Checked;

                // 保存日志类型设置
                foreach (var kvp in _logTypeCheckBoxes)
                {
                    LogSettings.EnabledLogTypes[kvp.Key] = kvp.Value.Checked;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// 更新控件状态
        /// </summary>
        private void UpdateControlsState()
        {
            bool isEnabled = chkEnableLogging.Checked;
            
            // 启用/禁用日志类型面板
            panelLogTypes.Enabled = isEnabled;
            btnSelectAll.Enabled = isEnabled;
            btnDeselectAll.Enabled = isEnabled;
            lblLogTypesTitle.Enabled = isEnabled;

            // 更新日志类型复选框的外观
            foreach (var checkBox in _logTypeCheckBoxes.Values)
            {
                checkBox.Enabled = isEnabled;
                checkBox.ForeColor = isEnabled 
                    ? (checkBox.Font.Bold ? Color.FromArgb(33, 37, 41) : Color.FromArgb(108, 117, 125))
                    : Color.FromArgb(173, 181, 189);
            }
        }

        /// <summary>
        /// 应用现代化样式
        /// </summary>
        private void ApplyModernStyle()
        {
            // 窗体样式 - 使用更大的基础字体
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular); // 提升基础字体到10F

            // 面板样式 - 增加圆角效果和阴影感
            panelLogTypes.BackColor = Color.White;
            panelLogTypes.BorderStyle = BorderStyle.FixedSingle;

            // 按钮样式 - 增加圆角和悬停效果
            btnOK.BackColor = Color.FromArgb(0, 123, 255);
            btnOK.ForeColor = Color.White;
            btnOK.FlatStyle = FlatStyle.Flat;
            btnOK.FlatAppearance.BorderSize = 0;
            btnOK.Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular); // 确保按钮字体足够大

            btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular);

            btnSelectAll.BackColor = Color.FromArgb(40, 167, 69);
            btnSelectAll.ForeColor = Color.White;
            btnSelectAll.FlatStyle = FlatStyle.Flat;
            btnSelectAll.FlatAppearance.BorderSize = 0;
            btnSelectAll.Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular);

            btnDeselectAll.BackColor = Color.FromArgb(220, 53, 69);
            btnDeselectAll.ForeColor = Color.White;
            btnDeselectAll.FlatStyle = FlatStyle.Flat;
            btnDeselectAll.FlatAppearance.BorderSize = 0;
            btnDeselectAll.Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular);

            // 增加按钮悬停效果
            AddButtonHoverEffects();
        }

        /// <summary>
        /// 添加按钮悬停效果
        /// </summary>
        private void AddButtonHoverEffects()
        {
            // 为每个按钮添加鼠标悬停效果
            AddHoverEffect(btnOK, Color.FromArgb(0, 123, 255), Color.FromArgb(0, 86, 179));
            AddHoverEffect(btnCancel, Color.FromArgb(108, 117, 125), Color.FromArgb(90, 98, 104));
            AddHoverEffect(btnSelectAll, Color.FromArgb(40, 167, 69), Color.FromArgb(33, 136, 56));
            AddHoverEffect(btnDeselectAll, Color.FromArgb(220, 53, 69), Color.FromArgb(200, 35, 51));
        }

        /// <summary>
        /// 为按钮添加悬停效果
        /// </summary>
        private void AddHoverEffect(Button button, Color normalColor, Color hoverColor)
        {
            button.MouseEnter += (s, e) => button.BackColor = hoverColor;
            button.MouseLeave += (s, e) => button.BackColor = normalColor;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 启用日志记录复选框状态变更事件
        /// </summary>
        private void ChkEnableLogging_CheckedChanged(object sender, EventArgs e)
        {
            UpdateControlsState();
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void BtnSelectAll_Click(object sender, EventArgs e)
        {
            foreach (var checkBox in _logTypeCheckBoxes.Values)
            {
                checkBox.Checked = true;
            }
        }

        /// <summary>
        /// 取消全选按钮点击事件
        /// </summary>
        private void BtnDeselectAll_Click(object sender, EventArgs e)
        {
            foreach (var checkBox in _logTypeCheckBoxes.Values)
            {
                checkBox.Checked = false;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                SaveSettings();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion
    }

    /// <summary>
    /// 日志类型信息类
    /// </summary>
    public class LogTypeInfo
    {
        /// <summary>
        /// 日志类型名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 日志类型描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否为重要日志类型
        /// </summary>
        public bool IsImportant { get; set; } = false;
    }
}
