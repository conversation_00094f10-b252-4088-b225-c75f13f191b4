using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 关键词编辑窗体
    /// </summary>
    public partial class KeywordEditForm : Form
    {
        /// <summary>
        /// 关键词字符串（每行一个关键词）
        /// </summary>
        public string Keywords { get; set; } = string.Empty;

        /// <summary>
        /// 窗体标题
        /// </summary>
        public string FormTitle
        {
            get => lblTitle.Text;
            set
            {
                lblTitle.Text = value;
                this.Text = value;
            }
        }

        /// <summary>
        /// 描述文本
        /// </summary>
        public string DescriptionText
        {
            get => lblDescription.Text;
            set => lblDescription.Text = value;
        }

        public KeywordEditForm()
        {
            InitializeComponent();
            SetupEventHandlers();
            SetupMultilineTextBoxSupport();

            // 启用按键预处理，确保多行文本框按键不被窗体拦截
            this.KeyPreview = true;
        }

        /// <summary>
        /// 构造函数，带初始关键词
        /// </summary>
        /// <param name="initialKeywords">初始关键词字符串</param>
        public KeywordEditForm(string initialKeywords) : this()
        {
            Keywords = initialKeywords ?? string.Empty;
            LoadKeywordsToUI();
        }

        /// <summary>
        /// 构造函数，带标题和初始关键词
        /// </summary>
        /// <param name="title">窗体标题</param>
        /// <param name="initialKeywords">初始关键词字符串</param>
        public KeywordEditForm(string title, string initialKeywords) : this(initialKeywords)
        {
            FormTitle = title;
        }

        /// <summary>
        /// 构造函数，带标题、描述和初始关键词
        /// </summary>
        /// <param name="title">窗体标题</param>
        /// <param name="description">描述文本</param>
        /// <param name="initialKeywords">初始关键词字符串</param>
        public KeywordEditForm(string title, string description, string initialKeywords) : this(title, initialKeywords)
        {
            DescriptionText = description;
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                btnOK.Click += BtnOK_Click;
                btnCancel.Click += BtnCancel_Click;
                this.Load += KeywordEditForm_Load;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置事件处理器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void KeywordEditForm_Load(object? sender, EventArgs e)
        {
            try
            {
                // 设置焦点到文本框
                txtKeywords.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"窗体加载失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveUIToKeywords();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存关键词失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 加载关键词到界面
        /// </summary>
        private void LoadKeywordsToUI()
        {
            try
            {
                txtKeywords.Text = Keywords ?? string.Empty;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载关键词到界面失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存界面内容到关键词字符串
        /// </summary>
        private void SaveUIToKeywords()
        {
            try
            {
                Keywords = txtKeywords.Text?.Trim() ?? string.Empty;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存界面内容失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置多行文本框支持
        /// </summary>
        private void SetupMultilineTextBoxSupport()
        {
            try
            {
                // 为多行文本框设置必要的属性
                txtKeywords.AcceptsReturn = true; // 接受回车键
                txtKeywords.AcceptsTab = true; // 接受Tab键
                txtKeywords.WordWrap = true; // 自动换行

                // 添加按键事件处理，防止按键被窗体默认按钮捕获
                txtKeywords.KeyDown += MultilineTextBox_KeyDown;
                txtKeywords.KeyPress += MultilineTextBox_KeyPress;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置多行文本框支持失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 多行文本框按键按下事件处理
        /// </summary>
        private void MultilineTextBox_KeyDown(object? sender, KeyEventArgs e)
        {
            if (sender is TextBox textBox && textBox.Multiline)
            {
                // 阻止所有按键被窗体处理，让文本框优先处理
                e.Handled = false;
                e.SuppressKeyPress = false;

                // 特别处理一些可能被窗体捕获的按键
                switch (e.KeyCode)
                {
                    case Keys.Enter:
                        // 在多行文本框中插入换行
                        if (!e.Control && !e.Alt && !e.Shift)
                        {
                            textBox.SelectedText = Environment.NewLine;
                            e.Handled = true;
                            e.SuppressKeyPress = true;
                        }
                        break;
                    case Keys.Space:
                        // 确保空格键正常工作
                        e.Handled = false;
                        break;
                    case Keys.Tab:
                        // 在文本框中插入Tab字符
                        if (!e.Control && !e.Alt)
                        {
                            textBox.SelectedText = "\t";
                            e.Handled = true;
                            e.SuppressKeyPress = true;
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 多行文本框按键字符事件处理
        /// </summary>
        private void MultilineTextBox_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (sender is TextBox textBox && textBox.Multiline)
            {
                // 对于普通字符输入，确保不被拦截
                if (char.IsControl(e.KeyChar))
                {
                    // 对于控制字符，让文本框自己处理
                    e.Handled = false;
                }
                else
                {
                    // 对于普通字符，确保能正常输入
                    e.Handled = false;
                }
            }
        }

        /// <summary>
        /// 窗体按键预处理，确保多行文本框按键不被拦截
        /// </summary>
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            // 检查当前焦点控件是否为多行文本框
            if (this.ActiveControl is TextBox textBox && textBox.Multiline)
            {
                // 对于多行文本框，让其优先处理按键
                switch (keyData)
                {
                    case Keys.Enter:
                    case Keys.Space:
                    case Keys.Tab:
                        // 这些按键让文本框自己处理，不传递给窗体
                        return false;
                }
            }

            // 其他情况使用默认处理
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}
