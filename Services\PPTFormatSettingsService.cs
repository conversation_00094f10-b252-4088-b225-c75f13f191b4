using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Drawing;
using Aspose.Slides;
using Aspose.Slides.Charts;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// PPT格式设置处理服务 - 负责应用PPT全局格式设置，包括段落、字体、主题、母版、布局和样式设置，符合Aspose.Slides API规范
    /// </summary>
    public class PPTFormatSettingsService
    {
        /// <summary>
        /// 应用PPT格式设置
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="settings">PPT格式设置（可为null，使用默认设置）</param>
        /// <returns>处理结果</returns>
        public async Task<ProcessingResult> ApplyPPTFormatSettingsAsync(IPresentation presentation, object? settings)
        {
            try
            {
                var result = new ProcessingResult { IsSuccess = true };
                var messages = new List<string>();

                await Task.Run(() =>
                {
                    // 应用主题设置
                    ApplyThemeSettings(presentation, messages);

                    // 应用母版设置
                    ApplyMasterSlideSettings(presentation, messages);

                    // 应用布局设置
                    if (settings is PPTPiliangChuli.Models.LayoutSettings layoutSettings)
                    {
                        ApplyLayoutSettingsWithConfig(presentation, layoutSettings, messages);
                    }
                    else
                    {
                        ApplyLayoutSettings(presentation, messages);
                    }

                    // 应用样式设置
                    ApplyStyleSettings(presentation, messages);
                });

                result.Message = string.Join("; ", messages);
                return result;
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"PPT格式设置处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 应用主题设置
        /// </summary>
        private void ApplyThemeSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 获取主题
                var theme = presentation.MasterTheme;
                
                // 这里可以根据需要修改主题的颜色方案、字体方案等
                // 例如：设置默认字体
                if (theme.FontScheme != null)
                {
                    // 可以在这里设置字体方案
                    messages.Add("主题字体方案已应用");
                }

                // 设置颜色方案
                if (theme.ColorScheme != null)
                {
                    // 可以在这里设置颜色方案
                    messages.Add("主题颜色方案已应用");
                }
            }
            catch (Exception ex)
            {
                messages.Add($"应用主题设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用母版设置 - 根据配置设置母版背景、文本样式和页眉页脚
        /// </summary>
        private void ApplyMasterSlideSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 获取幻灯片母版
                var masterSlide = presentation.Masters[0];

                // 应用母版背景设置
                ApplyMasterBackground(masterSlide, messages);

                // 应用母版文本样式设置
                ApplyMasterTextStyles(masterSlide, messages);

                // 应用母版页眉页脚设置
                ApplyMasterHeaderFooter(masterSlide, messages);

                messages.Add("母版设置已应用");
            }
            catch (Exception ex)
            {
                messages.Add($"应用母版设置失败: {ex.Message}");
                LogService.Instance.LogProcessError($"应用母版设置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用母版背景设置
        /// </summary>
        private void ApplyMasterBackground(IMasterSlide masterSlide, List<string> messages)
        {
            try
            {
                // 根据配置设置母版背景
                if (masterSlide.Background != null)
                {
                    // 设置背景填充类型
                    masterSlide.Background.Type = BackgroundType.OwnBackground;

                    // 根据背景类型设置不同的填充方式
                    var fillFormat = masterSlide.Background.FillFormat;
                    fillFormat.FillType = FillType.Solid;
                    fillFormat.SolidFillColor.Color = Color.White; // 默认白色背景

                    messages.Add("母版背景已设置");
                }
            }
            catch (Exception ex)
            {
                messages.Add($"设置母版背景失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用母版文本样式设置
        /// </summary>
        private void ApplyMasterTextStyles(IMasterSlide masterSlide, List<string> messages)
        {
            try
            {
                // 遍历母版中的占位符，设置文本样式
                foreach (IShape shape in masterSlide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        // 设置文本框的段落格式
                        foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                        {
                            // 设置段落对齐方式
                            paragraph.ParagraphFormat.Alignment = TextAlignment.Left;

                            // 设置字体格式
                            foreach (IPortion portion in paragraph.Portions)
                            {
                                portion.PortionFormat.FontHeight = 14; // 默认字体大小
                                portion.PortionFormat.LatinFont = new FontData("微软雅黑"); // 默认字体
                                portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                                portion.PortionFormat.FillFormat.SolidFillColor.Color = Color.Black; // 默认黑色
                            }
                        }
                    }
                }

                messages.Add("母版文本样式已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"设置母版文本样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用母版页眉页脚设置
        /// </summary>
        private void ApplyMasterHeaderFooter(IMasterSlide masterSlide, List<string> messages)
        {
            try
            {
                // 设置母版的页眉页脚占位符
                var headerFooterManager = masterSlide.HeaderFooterManager;

                // 设置页脚可见性
                headerFooterManager.SetFooterVisibility(false); // 默认不显示页脚
                headerFooterManager.SetSlideNumberVisibility(false); // 默认不显示幻灯片编号
                headerFooterManager.SetDateTimeVisibility(false); // 默认不显示日期时间

                messages.Add("母版页眉页脚已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"设置母版页眉页脚失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用布局设置（使用配置） - 根据LayoutSettings配置设置幻灯片布局样式
        /// </summary>
        private void ApplyLayoutSettingsWithConfig(IPresentation presentation, PPTPiliangChuli.Models.LayoutSettings layoutSettings, List<string> messages)
        {
            try
            {
                if (!layoutSettings.EnableLayout)
                {
                    messages.Add("布局设置已禁用，跳过应用");
                    return;
                }

                // 应用标题幻灯片布局设置
                if (layoutSettings.TitleSlideLayout.EnableTitleLayout)
                {
                    ApplyTitleSlideLayoutSettingsWithConfig(presentation, layoutSettings.TitleSlideLayout, messages);
                }

                // 应用内容布局设置
                if (layoutSettings.ContentLayout.EnableContentLayout)
                {
                    ApplyContentLayoutSettingsWithConfig(presentation, layoutSettings.ContentLayout, messages);
                }

                // 应用两栏布局设置
                if (layoutSettings.TwoColumnLayout.EnableTwoColumnLayout)
                {
                    ApplyTwoColumnLayoutSettingsWithConfig(presentation, layoutSettings.TwoColumnLayout, messages);
                }

                // 应用图片布局设置
                if (layoutSettings.PictureLayout.EnablePictureLayout)
                {
                    ApplyPictureLayoutSettingsWithConfig(presentation, layoutSettings.PictureLayout, messages);
                }

                // 应用自定义布局设置
                if (layoutSettings.CustomLayout.EnableCustomLayout)
                {
                    ApplyCustomLayoutSettingsWithConfig(presentation, layoutSettings.CustomLayout, messages);
                }

                messages.Add("布局设置（配置模式）已应用");
            }
            catch (Exception ex)
            {
                messages.Add($"应用布局设置（配置模式）失败: {ex.Message}");
                LogService.Instance.LogProcessError($"应用布局设置（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用布局设置 - 根据配置设置幻灯片布局样式
        /// </summary>
        private void ApplyLayoutSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 遍历所有布局母版
                foreach (IMasterSlide master in presentation.Masters)
                {
                    foreach (ILayoutSlide layout in master.LayoutSlides)
                    {
                        // 应用布局样式设置
                        ApplyLayoutStyle(layout, messages);
                    }
                }

                // 应用标题幻灯片布局设置
                ApplyTitleSlideLayoutSettings(presentation, messages);

                // 应用内容布局设置
                ApplyContentLayoutSettings(presentation, messages);

                // 应用两栏布局设置
                ApplyTwoColumnLayoutSettings(presentation, messages);

                // 应用图片布局设置
                ApplyPictureLayoutSettings(presentation, messages);

                // 应用自定义布局设置
                ApplyCustomLayoutSettings(presentation, messages);

                messages.Add("布局设置已应用");
            }
            catch (Exception ex)
            {
                messages.Add($"应用布局设置失败: {ex.Message}");
                LogService.Instance.LogProcessError($"应用布局设置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用单个布局样式
        /// </summary>
        private void ApplyLayoutStyle(ILayoutSlide layout, List<string> messages)
        {
            try
            {
                // 设置布局的背景
                if (layout.Background != null)
                {
                    layout.Background.Type = BackgroundType.OwnBackground;
                    var fillFormat = layout.Background.FillFormat;
                    fillFormat.FillType = FillType.Solid;
                    fillFormat.SolidFillColor.Color = Color.White; // 默认白色背景
                }

                // 设置布局中的占位符样式
                foreach (IShape shape in layout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        // 设置占位符的文本样式
                        ApplyPlaceholderTextStyle(autoShape.TextFrame);
                    }
                }

                messages.Add($"布局 '{layout.Name}' 样式已应用");
            }
            catch (Exception ex)
            {
                // 记录错误但不中断处理
                LogService.Instance.LogProcessError($"应用布局样式失败: {ex.Message}", ex);
                messages.Add($"应用布局样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用占位符文本样式
        /// </summary>
        private void ApplyPlaceholderTextStyle(ITextFrame textFrame)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    // 设置段落格式
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Left;
                    paragraph.ParagraphFormat.SpaceAfter = 6;
                    paragraph.ParagraphFormat.SpaceBefore = 0;

                    // 设置字体格式
                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = 18; // 默认字体大小
                        portion.PortionFormat.LatinFont = new FontData("微软雅黑"); // 默认字体
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = Color.Black; // 默认黑色
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用占位符文本样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用样式设置
        /// </summary>
        private void ApplyStyleSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 遍历所有幻灯片
                foreach (ISlide slide in presentation.Slides)
                {
                    ApplySlideStyle(slide);
                }

                messages.Add("样式设置已应用");
            }
            catch (Exception ex)
            {
                messages.Add($"应用样式设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用单个幻灯片样式
        /// </summary>
        private void ApplySlideStyle(ISlide slide)
        {
            try
            {
                // 设置幻灯片背景
                if (slide.Background != null)
                {
                    // 可以在这里设置背景样式
                }

                // 设置幻灯片中的形状样式
                foreach (IShape shape in slide.Shapes)
                {
                    ApplyShapeStyle(shape);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断处理
                LogService.Instance.LogProcessError($"应用幻灯片样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用形状样式
        /// </summary>
        private void ApplyShapeStyle(IShape shape)
        {
            try
            {
                // 根据形状类型应用不同的样式
                switch (shape)
                {
                    case IAutoShape autoShape when autoShape.TextFrame != null:
                        // 应用文本框样式
                        ApplyTextFrameStyle(autoShape.TextFrame);
                        break;
                    case ITable table:
                        // 应用表格样式
                        ApplyTableStyle(table);
                        break;
                    case IChart chart:
                        // 应用图表样式
                        ApplyChartStyle(chart);
                        break;
                    case IPictureFrame pictureFrame:
                        // 应用图片样式
                        ApplyPictureStyle(pictureFrame);
                        break;
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断处理
                LogService.Instance.LogProcessError($"应用形状样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用文本框样式
        /// </summary>
        private void ApplyTextFrameStyle(ITextFrame textFrame)
        {
            // 可以在这里设置文本框的样式
            // 例如：字体、颜色、对齐方式等
        }

        /// <summary>
        /// 应用表格样式
        /// </summary>
        private void ApplyTableStyle(ITable table)
        {
            // 可以在这里设置表格的样式
            // 例如：边框、背景色、字体等
        }

        /// <summary>
        /// 应用图表样式
        /// </summary>
        private void ApplyChartStyle(IChart chart)
        {
            // 可以在这里设置图表的样式
            // 例如：颜色方案、字体、图例等
        }

        /// <summary>
        /// 应用图片样式
        /// </summary>
        private void ApplyPictureStyle(IPictureFrame pictureFrame)
        {
            // 可以在这里设置图片的样式
            // 例如：边框、阴影、效果等
        }

        /// <summary>
        /// 应用标题幻灯片布局设置
        /// </summary>
        private void ApplyTitleSlideLayoutSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 查找标题幻灯片布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var titleLayout = master.LayoutSlides.FirstOrDefault(l => l.Name.Contains("Title") || l.Name.Contains("标题"));
                    if (titleLayout != null)
                    {
                        // 设置标题幻灯片的特定样式
                        ApplyTitleLayoutStyle(titleLayout);
                        messages.Add("标题幻灯片布局已设置");
                    }
                }
            }
            catch (Exception ex)
            {
                messages.Add($"应用标题幻灯片布局失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用标题布局样式
        /// </summary>
        private void ApplyTitleLayoutStyle(ILayoutSlide titleLayout)
        {
            try
            {
                // 设置标题占位符样式
                foreach (IShape shape in titleLayout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        if (autoShape.Placeholder?.Type == PlaceholderType.Title)
                        {
                            // 设置标题样式
                            ApplyTitleTextStyle(autoShape.TextFrame);
                        }
                        else if (autoShape.Placeholder?.Type == PlaceholderType.Subtitle)
                        {
                            // 设置副标题样式
                            ApplySubtitleTextStyle(autoShape.TextFrame);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用标题布局样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用标题文本样式
        /// </summary>
        private void ApplyTitleTextStyle(ITextFrame textFrame)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Center; // 标题居中
                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = 36; // 标题字体大小
                        portion.PortionFormat.LatinFont = new FontData("微软雅黑");
                        portion.PortionFormat.FontBold = NullableBool.True; // 粗体
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = Color.DarkBlue;
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用标题文本样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用副标题文本样式
        /// </summary>
        private void ApplySubtitleTextStyle(ITextFrame textFrame)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Center; // 副标题居中
                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = 18; // 副标题字体大小
                        portion.PortionFormat.LatinFont = new FontData("微软雅黑");
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = Color.Gray;
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用副标题文本样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用内容布局设置
        /// </summary>
        private void ApplyContentLayoutSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 查找内容布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var contentLayouts = master.LayoutSlides.Where(l =>
                        l.Name.Contains("Content") || l.Name.Contains("内容") ||
                        l.Name.Contains("Title and Content") || l.Name.Contains("标题和内容"));

                    foreach (var layout in contentLayouts)
                    {
                        ApplyContentLayoutStyle(layout);
                    }
                }
                messages.Add("内容布局已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"应用内容布局失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用内容布局样式
        /// </summary>
        private void ApplyContentLayoutStyle(ILayoutSlide contentLayout)
        {
            try
            {
                foreach (IShape shape in contentLayout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        if (autoShape.Placeholder?.Type == PlaceholderType.Object ||
                            autoShape.Placeholder?.Type == PlaceholderType.Body)
                        {
                            // 设置内容区域样式
                            ApplyContentTextStyle(autoShape.TextFrame);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用内容布局样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用内容文本样式
        /// </summary>
        private void ApplyContentTextStyle(ITextFrame textFrame)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Left; // 内容左对齐
                    paragraph.ParagraphFormat.SpaceAfter = 6;
                    paragraph.ParagraphFormat.SpaceBefore = 0;
                    paragraph.ParagraphFormat.Indent = 0;

                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = 14; // 内容字体大小
                        portion.PortionFormat.LatinFont = new FontData("微软雅黑");
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = Color.Black;
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用内容文本样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用两栏布局设置
        /// </summary>
        private void ApplyTwoColumnLayoutSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 查找两栏布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var twoColumnLayouts = master.LayoutSlides.Where(l =>
                        l.Name.Contains("Two Content") || l.Name.Contains("两栏") ||
                        l.Name.Contains("Comparison") || l.Name.Contains("比较"));

                    foreach (var layout in twoColumnLayouts)
                    {
                        ApplyTwoColumnLayoutStyle(layout);
                    }
                }
                messages.Add("两栏布局已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"应用两栏布局失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用两栏布局样式
        /// </summary>
        private void ApplyTwoColumnLayoutStyle(ILayoutSlide twoColumnLayout)
        {
            try
            {
                // 设置两栏布局的特定样式
                foreach (IShape shape in twoColumnLayout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        // 设置栏位样式
                        ApplyColumnTextStyle(autoShape.TextFrame);
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用两栏布局样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用栏位文本样式
        /// </summary>
        private void ApplyColumnTextStyle(ITextFrame textFrame)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Left;
                    paragraph.ParagraphFormat.SpaceAfter = 4;
                    paragraph.ParagraphFormat.SpaceBefore = 0;

                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = 12; // 栏位字体大小
                        portion.PortionFormat.LatinFont = new FontData("微软雅黑");
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = Color.Black;
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用栏位文本样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用图片布局设置
        /// </summary>
        private void ApplyPictureLayoutSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 查找图片布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var pictureLayouts = master.LayoutSlides.Where(l =>
                        l.Name.Contains("Picture") || l.Name.Contains("图片") ||
                        l.Name.Contains("Content with Caption") || l.Name.Contains("图文"));

                    foreach (var layout in pictureLayouts)
                    {
                        ApplyPictureLayoutStyle(layout);
                    }
                }
                messages.Add("图片布局已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"应用图片布局失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用图片布局样式
        /// </summary>
        private void ApplyPictureLayoutStyle(ILayoutSlide pictureLayout)
        {
            try
            {
                // 设置图片布局的特定样式
                foreach (IShape shape in pictureLayout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        if (autoShape.Placeholder?.Type == PlaceholderType.Picture)
                        {
                            // 设置图片占位符样式
                            ApplyPictureTextStyle(autoShape.TextFrame);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用图片布局样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用图片文本样式
        /// </summary>
        private void ApplyPictureTextStyle(ITextFrame textFrame)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Center; // 图片说明居中
                    paragraph.ParagraphFormat.SpaceAfter = 3;

                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = 10; // 图片说明字体大小
                        portion.PortionFormat.LatinFont = new FontData("微软雅黑");
                        portion.PortionFormat.FontItalic = NullableBool.True; // 斜体
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = Color.Gray;
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用图片文本样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用自定义布局设置
        /// </summary>
        private void ApplyCustomLayoutSettings(IPresentation presentation, List<string> messages)
        {
            try
            {
                // 查找自定义布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var customLayouts = master.LayoutSlides.Where(l =>
                        l.Name.Contains("Custom") || l.Name.Contains("自定义") ||
                        l.Name.Contains("Blank") || l.Name.Contains("空白"));

                    foreach (var layout in customLayouts)
                    {
                        ApplyCustomLayoutStyle(layout);
                    }
                }
                messages.Add("自定义布局已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"应用自定义布局失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用自定义布局样式
        /// </summary>
        private void ApplyCustomLayoutStyle(ILayoutSlide customLayout)
        {
            try
            {
                // 设置自定义布局的基础样式
                if (customLayout.Background != null)
                {
                    customLayout.Background.Type = BackgroundType.OwnBackground;
                    var fillFormat = customLayout.Background.FillFormat;
                    fillFormat.FillType = FillType.Solid;
                    fillFormat.SolidFillColor.Color = Color.White;
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用自定义布局样式失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用标题幻灯片布局设置（使用配置） - 根据TitleSlideLayoutSettings配置设置标题幻灯片布局样式
        /// </summary>
        private void ApplyTitleSlideLayoutSettingsWithConfig(IPresentation presentation, PPTPiliangChuli.Models.TitleSlideLayoutSettings titleSettings, List<string> messages)
        {
            try
            {
                // 查找标题幻灯片布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var titleLayout = master.LayoutSlides.FirstOrDefault(l => l.Name.Contains("Title") || l.Name.Contains("标题"));
                    if (titleLayout != null)
                    {
                        // 应用标题布局配置
                        ApplyTitleLayoutStyleWithConfig(titleLayout, titleSettings);
                        messages.Add("标题幻灯片布局（配置模式）已设置");
                    }
                }
            }
            catch (Exception ex)
            {
                messages.Add($"应用标题幻灯片布局（配置模式）失败: {ex.Message}");
                LogService.Instance.LogProcessError($"应用标题幻灯片布局（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用标题布局样式（使用配置）
        /// </summary>
        private void ApplyTitleLayoutStyleWithConfig(ILayoutSlide titleLayout, PPTPiliangChuli.Models.TitleSlideLayoutSettings titleSettings)
        {
            try
            {
                // 设置背景颜色
                if (titleLayout.Background != null)
                {
                    titleLayout.Background.Type = BackgroundType.OwnBackground;
                    var fillFormat = titleLayout.Background.FillFormat;
                    fillFormat.FillType = FillType.Solid;
                    fillFormat.SolidFillColor.Color = titleSettings.GetBackgroundColor();
                }

                // 设置标题占位符样式
                foreach (IShape shape in titleLayout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        if (autoShape.Placeholder?.Type == PlaceholderType.Title)
                        {
                            // 设置标题样式
                            ApplyTitleTextStyleWithConfig(autoShape.TextFrame, titleSettings);
                        }
                        else if (autoShape.Placeholder?.Type == PlaceholderType.Subtitle && titleSettings.ShowSubtitle)
                        {
                            // 设置副标题样式
                            ApplySubtitleTextStyleWithConfig(autoShape.TextFrame, titleSettings);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用标题布局样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用标题文本样式（使用配置）
        /// </summary>
        private void ApplyTitleTextStyleWithConfig(ITextFrame textFrame, PPTPiliangChuli.Models.TitleSlideLayoutSettings titleSettings)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    // 设置对齐方式
                    paragraph.ParagraphFormat.Alignment = titleSettings.TitleAlignment switch
                    {
                        "左对齐" => TextAlignment.Left,
                        "居中对齐" => TextAlignment.Center,
                        "右对齐" => TextAlignment.Right,
                        "两端对齐" => TextAlignment.Justify,
                        "分散对齐" => TextAlignment.Distributed,
                        _ => TextAlignment.Center
                    };

                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = titleSettings.TitleFontSize;
                        portion.PortionFormat.LatinFont = new FontData(titleSettings.TitleFont);
                        portion.PortionFormat.FontBold = titleSettings.TitleBold ? NullableBool.True : NullableBool.False;
                        portion.PortionFormat.FontItalic = titleSettings.TitleItalic ? NullableBool.True : NullableBool.False;
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = titleSettings.GetTitleColor();
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用标题文本样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用副标题文本样式（使用配置）
        /// </summary>
        private void ApplySubtitleTextStyleWithConfig(ITextFrame textFrame, PPTPiliangChuli.Models.TitleSlideLayoutSettings titleSettings)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Center; // 副标题居中
                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = titleSettings.SubtitleFontSize;
                        portion.PortionFormat.LatinFont = new FontData(titleSettings.SubtitleFont);
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = titleSettings.GetSubtitleColor();
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用副标题文本样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用内容布局设置（使用配置） - 根据ContentLayoutSettings配置设置内容布局样式
        /// </summary>
        private void ApplyContentLayoutSettingsWithConfig(IPresentation presentation, PPTPiliangChuli.Models.ContentLayoutSettings contentSettings, List<string> messages)
        {
            try
            {
                // 查找内容布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var contentLayouts = master.LayoutSlides.Where(l =>
                        l.Name.Contains("Content") || l.Name.Contains("内容") ||
                        l.Name.Contains("Title and Content") || l.Name.Contains("标题和内容"));

                    foreach (var layout in contentLayouts)
                    {
                        ApplyContentLayoutStyleWithConfig(layout, contentSettings);
                    }
                }
                messages.Add("内容布局（配置模式）已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"应用内容布局（配置模式）失败: {ex.Message}");
                LogService.Instance.LogProcessError($"应用内容布局（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用内容布局样式（使用配置）
        /// </summary>
        private void ApplyContentLayoutStyleWithConfig(ILayoutSlide contentLayout, PPTPiliangChuli.Models.ContentLayoutSettings contentSettings)
        {
            try
            {
                foreach (IShape shape in contentLayout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        if (autoShape.Placeholder?.Type == PlaceholderType.Object ||
                            autoShape.Placeholder?.Type == PlaceholderType.Body)
                        {
                            // 设置内容区域样式
                            ApplyContentTextStyleWithConfig(autoShape.TextFrame, contentSettings);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用内容布局样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用内容文本样式（使用配置）
        /// </summary>
        private void ApplyContentTextStyleWithConfig(ITextFrame textFrame, PPTPiliangChuli.Models.ContentLayoutSettings contentSettings)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    // 设置对齐方式
                    paragraph.ParagraphFormat.Alignment = contentSettings.ContentAlignment switch
                    {
                        "左对齐" => TextAlignment.Left,
                        "居中对齐" => TextAlignment.Center,
                        "右对齐" => TextAlignment.Right,
                        "顶部对齐" => TextAlignment.Left,
                        "中部对齐" => TextAlignment.Left,
                        "底部对齐" => TextAlignment.Left,
                        _ => TextAlignment.Left
                    };

                    // 设置段落间距
                    paragraph.ParagraphFormat.SpaceAfter = contentSettings.ParagraphSpacing;
                    paragraph.ParagraphFormat.SpaceBefore = 0;
                    paragraph.ParagraphFormat.SpaceWithin = (float)contentSettings.LineSpacing;

                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = contentSettings.ContentFontSize;
                        portion.PortionFormat.LatinFont = new FontData(contentSettings.ContentFont);
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = contentSettings.GetContentColor();
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用内容文本样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用两栏布局设置（使用配置） - 根据TwoColumnLayoutSettings配置设置两栏布局样式
        /// </summary>
        private void ApplyTwoColumnLayoutSettingsWithConfig(IPresentation presentation, PPTPiliangChuli.Models.TwoColumnLayoutSettings twoColumnSettings, List<string> messages)
        {
            try
            {
                // 查找两栏布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var twoColumnLayouts = master.LayoutSlides.Where(l =>
                        l.Name.Contains("Two Content") || l.Name.Contains("两栏") ||
                        l.Name.Contains("Comparison") || l.Name.Contains("比较"));

                    foreach (var layout in twoColumnLayouts)
                    {
                        ApplyTwoColumnLayoutStyleWithConfig(layout, twoColumnSettings);
                    }
                }
                messages.Add("两栏布局（配置模式）已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"应用两栏布局（配置模式）失败: {ex.Message}");
                LogService.Instance.LogProcessError($"应用两栏布局（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用两栏布局样式（使用配置）
        /// </summary>
        private void ApplyTwoColumnLayoutStyleWithConfig(ILayoutSlide twoColumnLayout, PPTPiliangChuli.Models.TwoColumnLayoutSettings twoColumnSettings)
        {
            try
            {
                // 设置两栏布局的特定样式
                foreach (IShape shape in twoColumnLayout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        // 设置栏位样式
                        ApplyColumnTextStyleWithConfig(autoShape.TextFrame, twoColumnSettings);
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用两栏布局样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用栏位文本样式（使用配置）
        /// </summary>
        private void ApplyColumnTextStyleWithConfig(ITextFrame textFrame, PPTPiliangChuli.Models.TwoColumnLayoutSettings twoColumnSettings)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Left; // 栏位内容左对齐
                    paragraph.ParagraphFormat.SpaceAfter = 6;
                    paragraph.ParagraphFormat.SpaceBefore = 0;

                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = twoColumnSettings.ColumnFontSize;
                        portion.PortionFormat.LatinFont = new FontData(twoColumnSettings.ColumnFont);
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = twoColumnSettings.GetColumnColor();
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用栏位文本样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用图片布局设置（使用配置） - 根据PictureLayoutSettings配置设置图片布局样式
        /// </summary>
        private void ApplyPictureLayoutSettingsWithConfig(IPresentation presentation, PPTPiliangChuli.Models.PictureLayoutSettings pictureSettings, List<string> messages)
        {
            try
            {
                // 查找图片布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var pictureLayouts = master.LayoutSlides.Where(l =>
                        l.Name.Contains("Picture") || l.Name.Contains("图片") ||
                        l.Name.Contains("Content with Caption") || l.Name.Contains("图文"));

                    foreach (var layout in pictureLayouts)
                    {
                        ApplyPictureLayoutStyleWithConfig(layout, pictureSettings);
                    }
                }
                messages.Add("图片布局（配置模式）已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"应用图片布局（配置模式）失败: {ex.Message}");
                LogService.Instance.LogProcessError($"应用图片布局（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用图片布局样式（使用配置）
        /// </summary>
        private void ApplyPictureLayoutStyleWithConfig(ILayoutSlide pictureLayout, PPTPiliangChuli.Models.PictureLayoutSettings pictureSettings)
        {
            try
            {
                // 设置图片布局的特定样式
                foreach (IShape shape in pictureLayout.Shapes)
                {
                    if (shape is IPictureFrame pictureFrame)
                    {
                        // 设置图片框样式
                        ApplyPictureFrameStyleWithConfig(pictureFrame, pictureSettings);
                    }
                    else if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        // 设置图片说明文本样式
                        ApplyPictureCaptionStyleWithConfig(autoShape.TextFrame, pictureSettings);
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用图片布局样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用图片框样式（使用配置）
        /// </summary>
        private void ApplyPictureFrameStyleWithConfig(IPictureFrame pictureFrame, PPTPiliangChuli.Models.PictureLayoutSettings pictureSettings)
        {
            try
            {
                // 设置图片边框
                if (pictureSettings.ShowBorder)
                {
                    pictureFrame.LineFormat.Style = LineStyle.Single;
                    pictureFrame.LineFormat.Width = pictureSettings.BorderWidth;
                    pictureFrame.LineFormat.FillFormat.FillType = FillType.Solid;
                    pictureFrame.LineFormat.FillFormat.SolidFillColor.Color = pictureSettings.GetBorderColor();
                }
                else
                {
                    pictureFrame.LineFormat.Style = LineStyle.NotDefined;
                }

                // 设置图片阴影
                if (pictureSettings.ShowShadow)
                {
                    pictureFrame.EffectFormat.EnableOuterShadowEffect();
                    var shadow = pictureFrame.EffectFormat.OuterShadowEffect;
                    shadow.ShadowColor.Color = pictureSettings.GetShadowColor();
                    shadow.Distance = 5;
                    shadow.Direction = 45;
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用图片框样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用图片说明样式（使用配置）
        /// </summary>
        private void ApplyPictureCaptionStyleWithConfig(ITextFrame textFrame, PPTPiliangChuli.Models.PictureLayoutSettings pictureSettings)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Center; // 图片说明居中
                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = pictureSettings.CaptionFontSize;
                        portion.PortionFormat.LatinFont = new FontData(pictureSettings.CaptionFont);
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = pictureSettings.GetCaptionColor();
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用图片说明样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用自定义布局设置（使用配置） - 根据CustomLayoutSettings配置设置自定义布局样式
        /// </summary>
        private void ApplyCustomLayoutSettingsWithConfig(IPresentation presentation, PPTPiliangChuli.Models.CustomLayoutSettings customSettings, List<string> messages)
        {
            try
            {
                // 查找自定义布局
                foreach (IMasterSlide master in presentation.Masters)
                {
                    var customLayouts = master.LayoutSlides.Where(l =>
                        l.Name.Contains("Custom") || l.Name.Contains("自定义") ||
                        l.Name.Contains("Blank") || l.Name.Contains("空白"));

                    foreach (var layout in customLayouts)
                    {
                        ApplyCustomLayoutStyleWithConfig(layout, customSettings);
                    }
                }
                messages.Add("自定义布局（配置模式）已设置");
            }
            catch (Exception ex)
            {
                messages.Add($"应用自定义布局（配置模式）失败: {ex.Message}");
                LogService.Instance.LogProcessError($"应用自定义布局（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用自定义布局样式（使用配置）
        /// </summary>
        private void ApplyCustomLayoutStyleWithConfig(ILayoutSlide customLayout, PPTPiliangChuli.Models.CustomLayoutSettings customSettings)
        {
            try
            {
                // 设置自定义布局的背景
                if (customLayout.Background != null)
                {
                    customLayout.Background.Type = BackgroundType.OwnBackground;
                    var fillFormat = customLayout.Background.FillFormat;
                    fillFormat.FillType = FillType.Solid;
                    fillFormat.SolidFillColor.Color = customSettings.GetBackgroundColor();
                }

                // 设置默认文本样式
                foreach (IShape shape in customLayout.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        ApplyCustomTextStyleWithConfig(autoShape.TextFrame, customSettings);
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用自定义布局样式（配置模式）失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用自定义文本样式（使用配置）
        /// </summary>
        private void ApplyCustomTextStyleWithConfig(ITextFrame textFrame, PPTPiliangChuli.Models.CustomLayoutSettings customSettings)
        {
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    paragraph.ParagraphFormat.Alignment = TextAlignment.Left; // 自定义布局默认左对齐
                    foreach (IPortion portion in paragraph.Portions)
                    {
                        portion.PortionFormat.FontHeight = customSettings.DefaultFontSize;
                        portion.PortionFormat.LatinFont = new FontData(customSettings.DefaultFont);
                        portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                        portion.PortionFormat.FillFormat.SolidFillColor.Color = customSettings.GetDefaultColor();
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.LogProcessError($"应用自定义文本样式（配置模式）失败: {ex.Message}", ex);
            }
        }
    }
}
