using System.Drawing.Text;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 字体名称替换规则编辑窗体
    /// </summary>
    public partial class FontNameReplacementRuleForm : Form
    {
        private FontNameReplacementRule _rule;
        private bool _isEditMode;

        // 控件
        private TextBox txtRuleName = null!;
        private ComboBox cmbSourceFont = null!;
        private ComboBox cmbTargetFont = null!;
        private CheckBox chkExactMatch = null!;
        private CheckBox chkIncludeSubFonts = null!;
        private CheckBox chkIsEnabled = null!;
        private Button btnOK = null!;
        private Button btnCancel = null!;

        public FontNameReplacementRule Rule => _rule;

        public FontNameReplacementRuleForm(FontNameReplacementRule? rule = null)
        {
            InitializeComponent();
            _isEditMode = rule != null;
            _rule = rule ?? new FontNameReplacementRule();

            InitializeForm();
            LoadAvailableFonts();
            LoadRuleData();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = _isEditMode ? "编辑字体名称替换规则" : "添加字体名称替换规则";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            int yPos = 20;
            int leftMargin = 20;
            int labelWidth = 100;
            int controlWidth = 280;

            // 规则名称
            var lblRuleName = new Label
            {
                Text = "规则名称:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblRuleName);

            txtRuleName = new TextBox
            {
                Name = "txtRuleName",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25)
            };
            this.Controls.Add(txtRuleName);
            yPos += 35;

            // 源字体
            var lblSourceFont = new Label
            {
                Text = "源字体:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblSourceFont);

            cmbSourceFont = new ComboBox
            {
                Name = "cmbSourceFont",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDown
            };
            this.Controls.Add(cmbSourceFont);
            yPos += 35;

            // 目标字体
            var lblTargetFont = new Label
            {
                Text = "目标字体:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblTargetFont);

            cmbTargetFont = new ComboBox
            {
                Name = "cmbTargetFont",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDown
            };
            this.Controls.Add(cmbTargetFont);
            yPos += 45;

            // 选项
            var grpOptions = new GroupBox
            {
                Text = "选项",
                Location = new Point(leftMargin, yPos),
                Size = new Size(controlWidth + labelWidth + 10, 80)
            };
            this.Controls.Add(grpOptions);

            chkExactMatch = new CheckBox
            {
                Text = "精确匹配字体名称",
                Location = new Point(15, 25),
                Size = new Size(180, 25),
                Checked = true
            };
            grpOptions.Controls.Add(chkExactMatch);

            chkIncludeSubFonts = new CheckBox
            {
                Text = "包含子字体",
                Location = new Point(200, 25),
                Size = new Size(120, 25)
            };
            grpOptions.Controls.Add(chkIncludeSubFonts);

            yPos += 90;

            // 是否启用
            chkIsEnabled = new CheckBox
            {
                Text = "启用此规则",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(150, 25),
                Checked = true
            };
            this.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(this.Width - 180, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            this.Controls.Add(btnOK);

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(this.Width - 95, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(btnCancel);
        }

        /// <summary>
        /// 加载可用字体
        /// </summary>
        private void LoadAvailableFonts()
        {
            try
            {
                using var installedFonts = new InstalledFontCollection();
                var fontNames = installedFonts.Families
                    .Select(f => f.Name)
                    .OrderBy(name => name)
                    .ToArray();

                cmbSourceFont.Items.AddRange(fontNames);
                cmbTargetFont.Items.AddRange(fontNames);

                // 设置默认字体
                if (fontNames.Contains("Microsoft YaHei"))
                {
                    cmbSourceFont.Text = "Microsoft YaHei";
                    cmbTargetFont.Text = "Microsoft YaHei";
                }
                else if (fontNames.Length > 0)
                {
                    cmbSourceFont.Text = fontNames[0];
                    cmbTargetFont.Text = fontNames[0];
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载系统字体失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            btnOK.Click += BtnOK_Click;
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRuleData();
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtRuleName.Text))
            {
                MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRuleName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbSourceFont.Text))
            {
                MessageBox.Show("请选择或输入源字体", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbSourceFont.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbTargetFont.Text))
            {
                MessageBox.Show("请选择或输入目标字体", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbTargetFont.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            txtRuleName.Text = _rule.RuleName;
            cmbSourceFont.Text = _rule.SourceFontName;
            cmbTargetFont.Text = _rule.TargetFontName;
            chkExactMatch.Checked = _rule.ExactMatch;
            chkIncludeSubFonts.Checked = _rule.IncludeSubFonts;
            chkIsEnabled.Checked = _rule.IsEnabled;
        }

        /// <summary>
        /// 保存规则数据
        /// </summary>
        private void SaveRuleData()
        {
            _rule.RuleName = txtRuleName.Text.Trim();
            _rule.SourceFontName = cmbSourceFont.Text.Trim();
            _rule.TargetFontName = cmbTargetFont.Text.Trim();
            _rule.ExactMatch = chkExactMatch.Checked;
            _rule.IncludeSubFonts = chkIncludeSubFonts.Checked;
            _rule.IsEnabled = chkIsEnabled.Checked;
            _rule.CreatedTime = _isEditMode ? _rule.CreatedTime : DateTime.Now;
        }
    }
}
