using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Aspose.Slides;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 文档属性处理服务 - 负责处理PPT文档属性的各种操作
    /// 包括删除属性、设置基本信息、统计属性、时间属性和自定义属性
    /// </summary>
    public class DocumentPropertiesService
    {
        /// <summary>
        /// 应用文档属性设置
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="settings">文档属性设置</param>
        /// <returns>处理结果</returns>
        public async Task<ProcessingResult> ApplyDocumentPropertiesAsync(IPresentation presentation, DocumentPropertiesSettings settings)
        {
            try
            {
                var result = new ProcessingResult { IsSuccess = true };
                var messages = new List<string>();

                await Task.Run(() =>
                {
                    // 处理删除设置（优先执行删除操作）
                    ApplyDeletionSettings(presentation, settings.DeletionSettings, messages);

                    // 处理基本信息属性
                    if (settings.BasicProperties.IsEnabled)
                    {
                        ApplyBasicProperties(presentation, settings.BasicProperties, messages);
                    }

                    // 处理统计属性
                    if (settings.StatisticsProperties.IsEnabled)
                    {
                        ApplyStatisticsProperties(presentation, settings.StatisticsProperties, messages);
                    }

                    // 处理时间属性
                    if (settings.TimeProperties.IsEnabled)
                    {
                        ApplyTimeProperties(presentation, settings.TimeProperties, messages);
                    }

                    // 处理自定义属性
                    if (settings.CustomProperties.IsEnabled)
                    {
                        ApplyCustomProperties(presentation, settings.CustomProperties, messages);
                    }
                });

                result.Message = string.Join("; ", messages);
                return result;
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"文档属性处理失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 应用删除设置 - 根据用户配置删除指定的文档属性
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="settings">删除设置</param>
        /// <param name="messages">处理消息列表</param>
        private void ApplyDeletionSettings(IPresentation presentation, DocumentPropertiesDeletionSettings settings, List<string> messages)
        {
            var props = presentation.DocumentProperties;

            // 删除所有内置属性
            if (settings.DeleteAllBuiltInProperties)
            {
                props.ClearBuiltInProperties();
                messages.Add("已删除所有内置属性");
            }

            // 删除所有自定义属性
            if (settings.DeleteAllCustomProperties)
            {
                props.ClearCustomProperties();
                messages.Add("已删除所有自定义属性");
            }

            // 分类删除内置属性
            if (settings.DeleteBasicProperties)
            {
                // 删除基本信息属性
                props.Title = "";
                props.Author = "";
                props.Subject = "";
                props.Keywords = "";
                props.Comments = "";
                props.Category = "";
                props.Company = "";
                props.Manager = "";
                messages.Add("已删除基本信息属性");
            }

            if (settings.DeleteStatisticsProperties)
            {
                // 注意：统计属性通常是只读的，这里只是清空可写的部分
                messages.Add("已清空统计属性（部分属性为只读）");
            }

            if (settings.DeleteTimeProperties)
            {
                // 删除时间属性
                props.CreatedTime = DateTime.MinValue;
                props.LastSavedTime = DateTime.MinValue;
                props.LastPrinted = DateTime.MinValue;
                props.LastSavedBy = "";
                props.TotalEditingTime = TimeSpan.Zero;
                messages.Add("已删除时间属性");
            }

            // 删除指定的自定义属性
            if (settings.CustomPropertiesToDelete?.Count > 0)
            {
                int deletedCount = 0;
                foreach (var propertyName in settings.CustomPropertiesToDelete)
                {
                    if (!string.IsNullOrEmpty(propertyName) && props.ContainsCustomProperty(propertyName))
                    {
                        props.RemoveCustomProperty(propertyName);
                        deletedCount++;
                    }
                }
                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个指定的自定义属性");
                }
            }
        }

        /// <summary>
        /// 应用基本信息属性
        /// </summary>
        private void ApplyBasicProperties(IPresentation presentation, BasicPropertiesSettings settings, List<string> messages)
        {
            var props = presentation.DocumentProperties;

            // 只有在勾选对应开关时才设置属性
            if (settings.SetTitle)
            {
                props.Title = settings.Title ?? "";
                messages.Add($"标题已设置: {settings.Title}");
            }

            if (settings.SetSubject)
            {
                props.Subject = settings.Subject ?? "";
                messages.Add($"主题已设置: {settings.Subject}");
            }

            if (settings.SetAuthor)
            {
                props.Author = settings.Author ?? "";
                messages.Add($"作者已设置: {settings.Author}");
            }

            if (settings.SetManager)
            {
                props.Manager = settings.Manager ?? "";
                messages.Add($"管理者已设置: {settings.Manager}");
            }

            if (settings.SetCompany)
            {
                props.Company = settings.Company ?? "";
                messages.Add($"公司已设置: {settings.Company}");
            }

            if (settings.SetCategory)
            {
                props.Category = settings.Category ?? "";
                messages.Add($"类别已设置: {settings.Category}");
            }

            if (settings.SetKeywords)
            {
                props.Keywords = settings.Keywords ?? "";
                messages.Add($"关键词已设置: {settings.Keywords}");
            }

            if (settings.SetComments)
            {
                props.Comments = settings.Comments ?? "";
                messages.Add($"备注已设置: {settings.Comments}");
            }
        }

        /// <summary>
        /// 应用统计属性 - 读取演示文稿的统计信息（使用Aspose.Slides API提供的只读属性）
        /// </summary>
        private void ApplyStatisticsProperties(IPresentation presentation, StatisticsPropertiesSettings settings, List<string> messages)
        {
            var props = presentation.DocumentProperties;
            var updatedStats = new List<string>();

            if (settings.UpdateSlideCount)
            {
                // 使用Aspose.Slides API提供的只读属性
                updatedStats.Add($"幻灯片数量: {props.Slides}");
            }

            if (settings.UpdateHiddenSlideCount)
            {
                // 使用Aspose.Slides API提供的只读属性
                updatedStats.Add($"隐藏幻灯片数量: {props.HiddenSlides}");
            }

            if (settings.UpdateNotesCount)
            {
                // 使用Aspose.Slides API提供的只读属性
                updatedStats.Add($"备注页数量: {props.Notes}");
            }

            if (settings.UpdateParagraphCount)
            {
                // 使用Aspose.Slides API提供的只读属性
                updatedStats.Add($"段落数量: {props.Paragraphs}");
            }

            if (settings.UpdateWordCount)
            {
                // 使用Aspose.Slides API提供的只读属性
                updatedStats.Add($"字数统计: {props.Words}");
            }

            if (settings.UpdateMultimediaClipCount)
            {
                // 使用Aspose.Slides API提供的只读属性
                updatedStats.Add($"多媒体剪辑数量: {props.MultimediaClips}");
            }

            if (updatedStats.Count > 0)
            {
                messages.Add($"统计属性已读取: {string.Join(", ", updatedStats)}");
            }
        }

        /// <summary>
        /// 应用时间属性
        /// </summary>
        private void ApplyTimeProperties(IPresentation presentation, TimePropertiesSettings settings, List<string> messages)
        {
            var props = presentation.DocumentProperties;

            if (settings.SetCreatedTime)
            {
                props.CreatedTime = settings.CreatedTime;
                messages.Add($"创建时间已设置: {settings.CreatedTime}");
            }

            if (settings.SetLastSavedTime)
            {
                // 注意：根据Aspose.Slides API文档，LastSavedTime在Presentation.DocumentProperties中是只读的
                // 它会在文件保存时自动更新，无法手动设置
                messages.Add($"注意：最后保存时间为只读属性，将在文件保存时自动更新为当前时间");
            }

            if (settings.SetLastPrintedTime)
            {
                props.LastPrinted = settings.LastPrintedTime;
                messages.Add($"最后打印时间已设置: {settings.LastPrintedTime}");
            }

            if (settings.SetLastSavedBy && !string.IsNullOrEmpty(settings.LastSavedBy))
            {
                props.LastSavedBy = settings.LastSavedBy;
                messages.Add($"最后保存者已设置: {settings.LastSavedBy}");
            }

            if (settings.SetTotalEditingTime)
            {
                props.TotalEditingTime = TimeSpan.FromMinutes(settings.TotalEditingTimeMinutes);
                messages.Add($"总编辑时间已设置: {settings.TotalEditingTimeMinutes} 分钟");
            }
        }

        /// <summary>
        /// 应用自定义属性 - 添加或更新自定义文档属性
        /// </summary>
        private void ApplyCustomProperties(IPresentation presentation, CustomPropertiesSettings settings, List<string> messages)
        {
            var customProps = presentation.DocumentProperties;

            // 添加新的自定义属性
            if (settings.CustomProperties?.Count > 0)
            {
                int addedCount = 0;
                foreach (var customProp in settings.CustomProperties)
                {
                    // 只处理启用的自定义属性
                    if (customProp.IsEnabled && !string.IsNullOrEmpty(customProp.Name))
                    {
                        try
                        {
                            // 根据值类型设置自定义属性
                            switch (customProp.PropertyType.ToLower())
                            {
                                case "text":
                                case "string":
                                    customProps[customProp.Name] = customProp.Value ?? "";
                                    break;
                                case "number":
                                case "int":
                                    if (int.TryParse(customProp.Value, out int intValue))
                                        customProps[customProp.Name] = intValue;
                                    else
                                        customProps[customProp.Name] = customProp.Value ?? ""; // 如果转换失败，保存为字符串
                                    break;
                                case "date":
                                case "datetime":
                                    if (DateTime.TryParse(customProp.Value, out DateTime dateValue))
                                        customProps[customProp.Name] = dateValue;
                                    else
                                        customProps[customProp.Name] = customProp.Value ?? ""; // 如果转换失败，保存为字符串
                                    break;
                                case "bool":
                                case "boolean":
                                    if (bool.TryParse(customProp.Value, out bool boolValue))
                                        customProps[customProp.Name] = boolValue;
                                    else
                                        customProps[customProp.Name] = customProp.Value ?? ""; // 如果转换失败，保存为字符串
                                    break;
                                default:
                                    customProps[customProp.Name] = customProp.Value ?? "";
                                    break;
                            }
                            addedCount++;
                            messages.Add($"自定义属性已设置: {customProp.Name} = {customProp.Value} ({customProp.PropertyType})");
                        }
                        catch (Exception ex)
                        {
                            messages.Add($"设置自定义属性失败 {customProp.Name}: {ex.Message}");
                        }
                    }
                }

                if (addedCount > 0)
                {
                    messages.Add($"共设置了 {addedCount} 个自定义属性");
                }
            }
        }
    }
}
