using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 位置区域选择助手窗体
    /// 用于在PPT截图上可视化选择删除区域，自动计算百分比坐标
    /// </summary>
    public partial class PositionHelperForm : Form
    {
        #region 私有字段

        private PictureBox _pictureBox = null!;
        private Panel _imagePanel = null!;
        private Button _btnLoadImage = null!;
        private Button _btnConfirm = null!;
        private Button _btnCancel = null!;
        private Label _lblInstructions = null!;
        private GroupBox _grpCoordinates = null!;
        private Label _lblXPercent = null!;
        private Label _lblYPercent = null!;
        private Label _lblWidthPercent = null!;
        private Label _lblHeightPercent = null!;

        private Image? _loadedImage;
        private Rectangle _selectionRect;
        private bool _isSelecting;
        private Point _startPoint;
        private Point _endPoint;

        #endregion

        #region 公共属性

        /// <summary>
        /// 选择区域的X坐标百分比
        /// </summary>
        public double SelectedXPercent { get; private set; }

        /// <summary>
        /// 选择区域的Y坐标百分比
        /// </summary>
        public double SelectedYPercent { get; private set; }

        /// <summary>
        /// 选择区域的宽度百分比
        /// </summary>
        public double SelectedWidthPercent { get; private set; }

        /// <summary>
        /// 选择区域的高度百分比
        /// </summary>
        public double SelectedHeightPercent { get; private set; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化位置选择助手窗体
        /// </summary>
        public PositionHelperForm()
        {
            InitializeComponent();
            InitializeControls();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化窗体组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PositionHelperForm));
            SuspendLayout();
            // 
            // PositionHelperForm
            // 
            ClientSize = new Size(1384, 861);
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MinimumSize = new Size(1000, 700);
            Name = "PositionHelperForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "区域选择助手";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 说明标签
            _lblInstructions = new Label
            {
                Text = "1. 点击\"加载图片\"选择PPT页面截图  2. 在图片上拖拽选择要删除的区域  3. 点击\"确定\"应用坐标",
                Location = new Point(20, 20),
                Size = new Size(1340, 25),  // 适应更大的窗口宽度
                Font = new Font("微软雅黑", 10F),
                ForeColor = Color.DarkBlue
            };

            // 加载图片按钮
            _btnLoadImage = new Button
            {
                Text = "加载图片",
                Location = new Point(20, 55),
                Size = new Size(120, 35),  // 稍微增大按钮
                Font = new Font("微软雅黑", 10F)
            };
            _btnLoadImage.Click += BtnLoadImage_Click;

            // 添加功能提示标签
            var lblTip = new Label
            {
                Text = "💡 提示：可以最大化窗口或拖拽边框调整大小以获得更好的预览效果",
                Location = new Point(160, 60),
                Size = new Size(600, 40),
                Font = new Font("微软雅黑", 9F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 图片显示面板 - 增大尺寸以适应更大的窗口
            _imagePanel = new Panel
            {
                Location = new Point(20, 100),
                Size = new Size(1000, 700),  // 显著增大图片显示区域
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true,
                BackColor = Color.LightGray,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom  // 支持窗口调整大小
            };

            // 图片框
            _pictureBox = new PictureBox
            {
                SizeMode = PictureBoxSizeMode.AutoSize,
                Location = new Point(0, 0),
                BackColor = Color.White
            };
            _pictureBox.MouseDown += PictureBox_MouseDown;
            _pictureBox.MouseMove += PictureBox_MouseMove;
            _pictureBox.MouseUp += PictureBox_MouseUp;
            _pictureBox.Paint += PictureBox_Paint;

            _imagePanel.Controls.Add(_pictureBox);

            // 坐标信息组框 - 调整位置到右侧
            _grpCoordinates = new GroupBox
            {
                Text = "选择区域坐标（百分比）",
                Location = new Point(1040, 100),  // 移到右侧
                Size = new Size(320, 250),        // 增大尺寸
                Font = new Font("微软雅黑", 10F),
                Anchor = AnchorStyles.Top | AnchorStyles.Right  // 锚定到右侧
            };

            // 坐标标签 - 增大字体和间距，百分比符号移到数值后面
            _lblXPercent = CreateCoordinateLabel("左边距: 0.0%", 20, 50);
            _lblYPercent = CreateCoordinateLabel("上边距: 0.0%", 20, 90);
            _lblWidthPercent = CreateCoordinateLabel("宽度: 0.0%", 20, 130);
            _lblHeightPercent = CreateCoordinateLabel("高度: 0.0%", 20, 170);

            _grpCoordinates.Controls.AddRange(new Control[] {
                _lblXPercent, _lblYPercent, _lblWidthPercent, _lblHeightPercent
            });

            // 确定按钮 - 调整位置
            _btnConfirm = new Button
            {
                Text = "确定",
                Location = new Point(1060, 770),  // 移到底部右侧
                Size = new Size(120, 40),         // 增大按钮
                Font = new Font("微软雅黑", 10F),
                Enabled = false,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right  // 锚定到右下角
            };
            _btnConfirm.Click += BtnConfirm_Click;

            // 取消按钮 - 调整位置
            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(1200, 770),  // 移到底部右侧
                Size = new Size(120, 40),         // 增大按钮
                Font = new Font("微软雅黑", 10F),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right  // 锚定到右下角
            };
            _btnCancel.Click += BtnCancel_Click;

            // 添加控件到窗体
            this.Controls.AddRange(new Control[] {
                _lblInstructions, _btnLoadImage, lblTip, _imagePanel, _grpCoordinates,
                _btnConfirm, _btnCancel
            });
        }

        /// <summary>
        /// 创建坐标显示标签
        /// </summary>
        private Label CreateCoordinateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(280, 30),  // 增大标签尺寸
                Font = new Font("微软雅黑", 11F, FontStyle.Bold),  // 增大字体并加粗
                ForeColor = Color.DarkGreen,
                TextAlign = ContentAlignment.MiddleLeft  // 左对齐垂直居中
            };
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 加载图片按钮点击事件
        /// </summary>
        private void BtnLoadImage_Click(object? sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Title = "选择PPT页面截图",
                Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff|所有文件|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // 释放之前的图片
                    _loadedImage?.Dispose();

                    // 加载新图片
                    _loadedImage = Image.FromFile(openFileDialog.FileName);
                    _pictureBox.Image = _loadedImage;

                    // 重置选择区域
                    _selectionRect = Rectangle.Empty;
                    UpdateCoordinateLabels();

                    // 启用确定按钮
                    _btnConfirm.Enabled = true;

                    // 刷新显示
                    _pictureBox.Invalidate();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"加载图片失败：{ex.Message}", "错误", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnConfirm_Click(object? sender, EventArgs e)
        {
            if (_loadedImage == null)
            {
                MessageBox.Show("请先加载图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (_selectionRect.IsEmpty)
            {
                MessageBox.Show("请在图片上拖拽选择区域", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region 鼠标事件处理

        /// <summary>
        /// 鼠标按下事件 - 开始选择区域
        /// </summary>
        private void PictureBox_MouseDown(object? sender, MouseEventArgs e)
        {
            if (_loadedImage == null || e.Button != MouseButtons.Left)
                return;

            // 限制起始点在图片范围内
            int maxX = _loadedImage.Width - 1;
            int maxY = _loadedImage.Height - 1;
            _startPoint = new Point(
                Math.Max(0, Math.Min(maxX, e.Location.X)),
                Math.Max(0, Math.Min(maxY, e.Location.Y))
            );

            _isSelecting = true;
            _endPoint = _startPoint;
            _selectionRect = new Rectangle(_startPoint.X, _startPoint.Y, 0, 0);

            _pictureBox.Cursor = Cursors.Cross;
        }

        /// <summary>
        /// 鼠标移动事件 - 更新选择区域
        /// </summary>
        private void PictureBox_MouseMove(object? sender, MouseEventArgs e)
        {
            if (!_isSelecting || _loadedImage == null)
                return;

            // 限制鼠标位置在图片范围内
            int maxX = _loadedImage.Width - 1;
            int maxY = _loadedImage.Height - 1;
            _endPoint = new Point(
                Math.Max(0, Math.Min(maxX, e.Location.X)),
                Math.Max(0, Math.Min(maxY, e.Location.Y))
            );

            // 计算选择矩形
            int x = Math.Min(_startPoint.X, _endPoint.X);
            int y = Math.Min(_startPoint.Y, _endPoint.Y);
            int width = Math.Abs(_endPoint.X - _startPoint.X);
            int height = Math.Abs(_endPoint.Y - _startPoint.Y);

            // 确保矩形不超出图片边界
            x = Math.Max(0, Math.Min(maxX, x));
            y = Math.Max(0, Math.Min(maxY, y));
            width = Math.Min(maxX - x, width);
            height = Math.Min(maxY - y, height);

            _selectionRect = new Rectangle(x, y, width, height);

            // 更新坐标显示
            UpdateCoordinateLabels();

            // 重绘图片框
            _pictureBox.Invalidate();
        }

        /// <summary>
        /// 鼠标释放事件 - 完成选择区域
        /// </summary>
        private void PictureBox_MouseUp(object? sender, MouseEventArgs e)
        {
            if (!_isSelecting || e.Button != MouseButtons.Left)
                return;

            _isSelecting = false;
            _pictureBox.Cursor = Cursors.Default;

            // 最终更新坐标
            UpdateCoordinateLabels();
        }

        /// <summary>
        /// 图片框绘制事件 - 绘制选择矩形
        /// </summary>
        private void PictureBox_Paint(object? sender, PaintEventArgs e)
        {
            if (_selectionRect.IsEmpty)
                return;

            // 绘制选择矩形
            using var pen = new Pen(Color.Red, 2);
            pen.DashStyle = DashStyle.Dash;
            e.Graphics.DrawRectangle(pen, _selectionRect);

            // 绘制半透明填充
            using var brush = new SolidBrush(Color.FromArgb(50, Color.Red));
            e.Graphics.FillRectangle(brush, _selectionRect);
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 更新坐标标签显示
        /// </summary>
        private void UpdateCoordinateLabels()
        {
            if (_loadedImage == null || _selectionRect.IsEmpty)
            {
                _lblXPercent.Text = "左边距: 0.0%";
                _lblYPercent.Text = "上边距: 0.0%";
                _lblWidthPercent.Text = "宽度: 0.0%";
                _lblHeightPercent.Text = "高度: 0.0%";

                SelectedXPercent = 0;
                SelectedYPercent = 0;
                SelectedWidthPercent = 0;
                SelectedHeightPercent = 0;
                return;
            }

            // 计算百分比坐标
            double imageWidth = _loadedImage.Width;
            double imageHeight = _loadedImage.Height;

            // 计算百分比并添加边界检查
            SelectedXPercent = Math.Max(0, Math.Min(100, Math.Round((_selectionRect.X / imageWidth) * 100, 1)));
            SelectedYPercent = Math.Max(0, Math.Min(100, Math.Round((_selectionRect.Y / imageHeight) * 100, 1)));
            SelectedWidthPercent = Math.Max(0.1, Math.Min(100, Math.Round((_selectionRect.Width / imageWidth) * 100, 1)));
            SelectedHeightPercent = Math.Max(0.1, Math.Min(100, Math.Round((_selectionRect.Height / imageHeight) * 100, 1)));

            // 更新标签文本 - 百分比符号移到数值后面
            _lblXPercent.Text = $"左边距: {SelectedXPercent}%";
            _lblYPercent.Text = $"上边距: {SelectedYPercent}%";
            _lblWidthPercent.Text = $"宽度: {SelectedWidthPercent}%";
            _lblHeightPercent.Text = $"高度: {SelectedHeightPercent}%";
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _loadedImage?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
