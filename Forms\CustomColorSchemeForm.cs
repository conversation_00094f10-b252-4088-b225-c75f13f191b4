using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 自定义颜色方案设置窗体
    /// </summary>
    public partial class CustomColorSchemeForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 自定义颜色方案设置
        /// </summary>
        public CustomColorSchemeSettings Settings { get; private set; } = new CustomColorSchemeSettings();

        #region 控件字段
        private TextBox? _txtSchemeName;
        private Button? _btnColor1, _btnColor2, _btnColor3, _btnColor4, _btnColor5, _btnColor6, _btnColor7, _btnColor8;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;
        private Panel? _pnlPreview;

        // 颜色存储
        private Color _color1 = Color.FromArgb(68, 114, 196);   // 深蓝色
        private Color _color2 = Color.FromArgb(237, 125, 49);   // 橙色
        private Color _color3 = Color.FromArgb(165, 165, 165);  // 灰色
        private Color _color4 = Color.FromArgb(255, 192, 0);    // 黄色
        private Color _color5 = Color.FromArgb(91, 155, 213);   // 浅蓝色
        private Color _color6 = Color.FromArgb(112, 173, 71);   // 绿色
        private Color _color7 = Color.FromArgb(158, 72, 14);    // 深橙色
        private Color _color8 = Color.FromArgb(99, 99, 99);     // 深灰色
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public CustomColorSchemeForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        /// <summary>
        /// 构造函数（编辑模式）
        /// </summary>
        /// <param name="schemeName">要编辑的方案名称</param>
        public CustomColorSchemeForm(string schemeName) : this()
        {
            LoadSchemeForEdit(schemeName);
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CustomColorSchemeForm));
            SuspendLayout();
            // 
            // CustomColorSchemeForm
            // 
            ClientSize = new Size(584, 461);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "CustomColorSchemeForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "自定义颜色方案";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateBasicInfoGroup();
            CreateColorGroup();
            CreatePreviewGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建基本信息组
        /// </summary>
        private void CreateBasicInfoGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "方案信息",
                Location = new Point(20, 20),
                Size = new Size(550, 60),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            var lblName = new Label
            {
                Text = "方案名称:",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtSchemeName = new TextBox
            {
                Location = new Point(110, 23),
                Size = new Size(200, 23),
                Font = new Font("Microsoft YaHei UI", 9F),
                Text = "自定义颜色方案"
            };

            groupBox.Controls.AddRange(new Control[] { lblName, _txtSchemeName });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建颜色设置组
        /// </summary>
        private void CreateColorGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "颜色设置",
                Location = new Point(20, 90),
                Size = new Size(550, 200),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            // 创建8个颜色按钮
            var colorLabels = new string[]
            {
                "主要颜色1", "主要颜色2", "强调颜色1", "强调颜色2",
                "强调颜色3", "强调颜色4", "强调颜色5", "强调颜色6"
            };

            var colors = new Color[] { _color1, _color2, _color3, _color4, _color5, _color6, _color7, _color8 };
            var buttons = new Button?[] { null, null, null, null, null, null, null, null };

            for (int i = 0; i < 8; i++)
            {
                int row = i / 4;
                int col = i % 4;

                var lblColor = new Label
                {
                    Text = colorLabels[i] + ":",
                    Location = new Point(20 + col * 130, 30 + row * 80),
                    Size = new Size(80, 20),
                    TextAlign = ContentAlignment.MiddleLeft,
                    Font = new Font("Microsoft YaHei UI", 8F)
                };

                var btnColor = new Button
                {
                    Location = new Point(20 + col * 130, 55 + row * 80),
                    Size = new Size(100, 30),
                    BackColor = colors[i],
                    FlatStyle = FlatStyle.Flat,
                    Tag = i
                };
                btnColor.Click += BtnColor_Click;

                buttons[i] = btnColor;
                groupBox.Controls.AddRange(new Control[] { lblColor, btnColor });
            }

            // 保存按钮引用
            _btnColor1 = buttons[0];
            _btnColor2 = buttons[1];
            _btnColor3 = buttons[2];
            _btnColor4 = buttons[3];
            _btnColor5 = buttons[4];
            _btnColor6 = buttons[5];
            _btnColor7 = buttons[6];
            _btnColor8 = buttons[7];

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建预览组
        /// </summary>
        private void CreatePreviewGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "预览",
                Location = new Point(20, 300),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _pnlPreview = new Panel
            {
                Location = new Point(20, 25),
                Size = new Size(510, 60),
                BorderStyle = BorderStyle.FixedSingle
            };

            // 添加预览色块
            for (int i = 0; i < 8; i++)
            {
                var colorBlock = new Panel
                {
                    Location = new Point(i * 60 + 10, 10),
                    Size = new Size(50, 40),
                    BorderStyle = BorderStyle.FixedSingle,
                    BackColor = GetColorByIndex(i)
                };
                _pnlPreview.Controls.Add(colorBlock);
            }

            groupBox.Controls.Add(_pnlPreview);
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button { Text = "确定(&O)", Location = new Point(300, 420), Size = new Size(80, 30) };
            _btnCancel = new Button { Text = "取消(&C)", Location = new Point(390, 420), Size = new Size(80, 30) };
            _btnReset = new Button { Text = "重置(&R)", Location = new Point(480, 420), Size = new Size(80, 30) };
            _btnPreview = new Button { Text = "预览(&P)", Location = new Point(210, 420), Size = new Size(80, 30) };

            _btnOK.Click += BtnOK_Click;
            _btnCancel.Click += BtnCancel_Click;
            _btnReset.Click += BtnReset_Click;
            _btnPreview.Click += BtnPreview_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset, _btnPreview });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            UpdatePreview();
        }

        /// <summary>
        /// 加载方案进行编辑
        /// </summary>
        /// <param name="schemeName">方案名称</param>
        private void LoadSchemeForEdit(string schemeName)
        {
            if (_txtSchemeName != null)
                _txtSchemeName.Text = schemeName;

            // 这里可以从配置文件或数据库加载方案信息
            // 目前使用默认值
            UpdatePreview();
        }

        /// <summary>
        /// 根据索引获取颜色
        /// </summary>
        private Color GetColorByIndex(int index)
        {
            return index switch
            {
                0 => _color1,
                1 => _color2,
                2 => _color3,
                3 => _color4,
                4 => _color5,
                5 => _color6,
                6 => _color7,
                7 => _color8,
                _ => Color.Black
            };
        }

        /// <summary>
        /// 设置指定索引的颜色
        /// </summary>
        private void SetColorByIndex(int index, Color color)
        {
            switch (index)
            {
                case 0: _color1 = color; break;
                case 1: _color2 = color; break;
                case 2: _color3 = color; break;
                case 3: _color4 = color; break;
                case 4: _color5 = color; break;
                case 5: _color6 = color; break;
                case 6: _color7 = color; break;
                case 7: _color8 = color; break;
            }
        }

        /// <summary>
        /// 更新预览
        /// </summary>
        private void UpdatePreview()
        {
            if (_pnlPreview == null) return;

            for (int i = 0; i < _pnlPreview.Controls.Count; i++)
            {
                if (_pnlPreview.Controls[i] is Panel colorBlock)
                {
                    colorBlock.BackColor = GetColorByIndex(i);
                }
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 颜色按钮点击事件
        /// </summary>
        private void BtnColor_Click(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is int index)
            {
                using var colorDialog = new ColorDialog
                {
                    Color = GetColorByIndex(index),
                    FullOpen = true
                };

                if (colorDialog.ShowDialog(this) == DialogResult.OK)
                {
                    SetColorByIndex(index, colorDialog.Color);
                    button.BackColor = colorDialog.Color;
                    UpdatePreview();
                }
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(_txtSchemeName?.Text))
                {
                    MessageBox.Show("请输入方案名称。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _txtSchemeName?.Focus();
                    return;
                }

                // 收集设置
                Settings = new CustomColorSchemeSettings
                {
                    SchemeName = _txtSchemeName.Text.Trim(),
                    Color1 = _color1,
                    Color2 = _color2,
                    Color3 = _color3,
                    Color4 = _color4,
                    Color5 = _color5,
                    Color6 = _color6,
                    Color7 = _color7,
                    Color8 = _color8
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            // 重置为默认颜色
            _color1 = Color.FromArgb(68, 114, 196);
            _color2 = Color.FromArgb(237, 125, 49);
            _color3 = Color.FromArgb(165, 165, 165);
            _color4 = Color.FromArgb(255, 192, 0);
            _color5 = Color.FromArgb(91, 155, 213);
            _color6 = Color.FromArgb(112, 173, 71);
            _color7 = Color.FromArgb(158, 72, 14);
            _color8 = Color.FromArgb(99, 99, 99);

            // 更新按钮颜色
            if (_btnColor1 != null) _btnColor1.BackColor = _color1;
            if (_btnColor2 != null) _btnColor2.BackColor = _color2;
            if (_btnColor3 != null) _btnColor3.BackColor = _color3;
            if (_btnColor4 != null) _btnColor4.BackColor = _color4;
            if (_btnColor5 != null) _btnColor5.BackColor = _color5;
            if (_btnColor6 != null) _btnColor6.BackColor = _color6;
            if (_btnColor7 != null) _btnColor7.BackColor = _color7;
            if (_btnColor8 != null) _btnColor8.BackColor = _color8;

            UpdatePreview();
        }

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能将显示应用当前颜色方案后的效果。\n\n" +
                           "此功能将在后续版本中实现。", "预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion
    }

    /// <summary>
    /// 自定义颜色方案设置类
    /// </summary>
    public class CustomColorSchemeSettings
    {
        public string SchemeName { get; set; } = "自定义颜色方案";
        public Color Color1 { get; set; } = Color.FromArgb(68, 114, 196);
        public Color Color2 { get; set; } = Color.FromArgb(237, 125, 49);
        public Color Color3 { get; set; } = Color.FromArgb(165, 165, 165);
        public Color Color4 { get; set; } = Color.FromArgb(255, 192, 0);
        public Color Color5 { get; set; } = Color.FromArgb(91, 155, 213);
        public Color Color6 { get; set; } = Color.FromArgb(112, 173, 71);
        public Color Color7 { get; set; } = Color.FromArgb(158, 72, 14);
        public Color Color8 { get; set; } = Color.FromArgb(99, 99, 99);
    }
}
