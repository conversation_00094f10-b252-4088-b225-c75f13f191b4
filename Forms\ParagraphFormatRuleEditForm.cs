using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 居中文字的ComboBox控件
    /// </summary>
    public class CenteredComboBox : ComboBox
    {
        public CenteredComboBox()
        {
            this.DrawMode = DrawMode.OwnerDrawFixed;
            this.DropDownStyle = ComboBoxStyle.DropDownList;
        }

        protected override void OnDrawItem(DrawItemEventArgs e)
        {
            if (e.Index < 0) return;

            e.DrawBackground();

            string text = this.Items[e.Index].ToString() ?? "";
            using (var brush = new SolidBrush(e.ForeColor))
            {
                var stringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                // 修复编译警告：确保字体不为null
                var font = e.Font ?? this.Font ?? SystemFonts.DefaultFont;
                e.Graphics.DrawString(text, font, brush, e.Bounds, stringFormat);
            }

            e.DrawFocusRectangle();
        }
    }
}

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 段落格式匹配规则编辑窗体 - 用于编辑单个段落格式匹配规则，包括匹配条件、段落格式和字体格式设置
    /// </summary>
    public partial class ParagraphFormatRuleEditForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 当前规则
        /// </summary>
        private ParagraphFormatMatchingRule _currentRule;

        /// <summary>
        /// 主标签页控件
        /// </summary>
        private TabControl _tabControl = null!;

        /// <summary>
        /// 规则名称文本框
        /// </summary>
        private TextBox _txtRuleName = null!;

        /// <summary>
        /// 确定按钮
        /// </summary>
        private Button _btnOK = null!;

        /// <summary>
        /// 取消按钮
        /// </summary>
        private Button _btnCancel = null!;

        // 匹配条件控件
        private CheckBox _chkStartsWith = null!;
        private TextBox _txtStartsWith = null!;
        private CheckBox _chkContains = null!;
        private ListBox _listContainsKeywords = null!;
        private TextBox _txtNewKeyword = null!;
        private Button _btnAddKeyword = null!;
        private Button _btnRemoveKeyword = null!;
        private CheckBox _chkEndsWith = null!;
        private TextBox _txtEndsWith = null!;
        private CheckBox _chkRegex = null!;
        private TextBox _txtRegexPattern = null!;
        private CheckBox _chkCharacterCount = null!;
        private NumericUpDown _numMinCharCount = null!;
        private NumericUpDown _numMaxCharCount = null!;
        private CheckBox _chkCaseSensitive = null!;
        private CheckBox _chkWholeWord = null!;

        // 替换范围控件
        private CheckBox _chkIncludeNormalSlides = null!;
        private CheckBox _chkIncludeMasterSlides = null!;
        private CheckBox _chkIncludeLayoutSlides = null!;

        // 段落格式控件
        private CheckBox _chkEnableParagraphFormat = null!;
        private CenteredComboBox _cmbAlignment = null!;
        private CheckBox _chkEnableIndentation = null!;
        private NumericUpDown _numLeftIndent = null!;
        private CenteredComboBox _cmbSpecialIndent = null!;
        private NumericUpDown _numSpecialIndentValue = null!;
        private CheckBox _chkEnableSpacing = null!;
        private NumericUpDown _numSpaceBefore = null!;
        private NumericUpDown _numSpaceAfter = null!;
        private CenteredComboBox _cmbLineSpacingType = null!;
        private NumericUpDown _numLineSpacingValue = null!;
        private CheckBox _chkEnableChineseControl = null!;
        private CheckBox _chkChineseCharacterControl = null!;
        private CheckBox _chkAllowLatinWordBreak = null!;
        private CheckBox _chkAllowPunctuationOverhang = null!;
        private CenteredComboBox _cmbTextAlignment = null!;

        // 字体格式控件
        private CheckBox _chkEnableFontFormat = null!;
        private CheckBox _chkSetChineseFont = null!;
        private CenteredComboBox _cmbChineseFontName = null!;
        private CheckBox _chkSetLatinFont = null!;
        private CenteredComboBox _cmbLatinFontName = null!;
        private CenteredComboBox _cmbFontStyle = null!;
        private CheckBox _chkSetFontSize = null!;
        private NumericUpDown _numFontSize = null!;
        private CheckBox _chkSetFontColor = null!;
        private Button _btnFontColor = null!;
        private CheckBox _chkSetUnderline = null!;
        private CenteredComboBox _cmbUnderlineType = null!;
        private Button _btnUnderlineColor = null!;
        private CheckBox _chkSetTextEffects = null!;
        private CheckBox _chkStrikethrough = null!;
        private CheckBox _chkDoubleStrikethrough = null!;
        private CheckBox _chkSuperscript = null!;
        private CheckBox _chkSubscript = null!;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="rule">要编辑的规则</param>
        public ParagraphFormatRuleEditForm(ParagraphFormatMatchingRule rule)
        {
            _currentRule = rule ?? throw new ArgumentNullException(nameof(rule));
            InitializeComponent();
            InitializeControls();
            SetupEventHandlers();
            LoadRuleData();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取编辑后的规则
        /// </summary>
        public ParagraphFormatMatchingRule GetRule()
        {
            SaveRuleData();
            return _currentRule;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 设置窗体属性 - 增加窗体大小以容纳更多内容
            this.Text = "编辑段落格式匹配规则";
            this.Size = new Size(950, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            // 创建主面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(15)
            };

            // 设置行高 - 增加各区域高度
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50)); // 规则名称区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 标签页区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 70)); // 按钮区域 - 增加高度

            // 创建规则名称区域
            CreateRuleNameArea(mainPanel);

            // 创建标签页区域
            CreateTabArea(mainPanel);

            // 创建按钮区域
            CreateButtonArea(mainPanel);

            this.Controls.Add(mainPanel);
        }

        /// <summary>
        /// 创建规则名称区域
        /// </summary>
        private void CreateRuleNameArea(TableLayoutPanel parent)
        {
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            var label = new Label
            {
                Text = "规则名称:",
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _txtRuleName = new TextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(5, 10, 5, 10),
                TextAlign = HorizontalAlignment.Center  // 文本框文字居中
            };

            panel.Controls.Add(label, 0, 0);
            panel.Controls.Add(_txtRuleName, 1, 0);
            parent.Controls.Add(panel, 0, 0);
        }

        /// <summary>
        /// 创建标签页区域
        /// </summary>
        private void CreateTabArea(TableLayoutPanel parent)
        {
            _tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Padding = new Point(10, 10)
            };

            // 创建匹配条件标签页
            CreateMatchingConditionsTab();

            // 创建段落格式标签页
            CreateParagraphFormatTab();

            // 创建字体格式标签页
            CreateFontFormatTab();

            parent.Controls.Add(_tabControl, 0, 1);
        }

        /// <summary>
        /// 创建匹配条件标签页
        /// </summary>
        private void CreateMatchingConditionsTab()
        {
            var tabPage = new TabPage("匹配条件")
            {
                Padding = new Padding(15)
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(5)
            };

            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 9, // 增加一行用于替换范围区域
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 设置行高 - 使用AutoSize确保内容完全显示
            for (int i = 0; i < 9; i++)
            {
                mainPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 替换范围区域 - 放在最上方
            CreateReplacementScopeControls(mainPanel, 0);

            // 段落开头匹配
            CreateStartsWithControls(mainPanel, 1);

            // 段落包含关键词匹配
            CreateContainsControls(mainPanel, 2);

            // 段落结尾匹配
            CreateEndsWithControls(mainPanel, 3);

            // 正则表达式匹配
            CreateRegexControls(mainPanel, 4);

            // 字符数限制
            CreateCharacterCountControls(mainPanel, 5);

            // 匹配选项
            CreateMatchingOptionsControls(mainPanel, 6);

            scrollPanel.Controls.Add(mainPanel);
            tabPage.Controls.Add(scrollPanel);
            _tabControl.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建段落开头匹配控件
        /// </summary>
        private void CreateStartsWithControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "段落开头匹配",
                Dock = DockStyle.Fill,
                Height = 140,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));

            _chkStartsWith = new CheckBox
            {
                Text = "启用段落开头匹配",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _txtStartsWith = new TextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "输入段落开头文本",
                Margin = new Padding(5, 2, 5, 2),
                TextAlign = HorizontalAlignment.Center  // 文本框文字居中
            };

            panel.Controls.Add(_chkStartsWith, 0, 0);
            panel.SetColumnSpan(_chkStartsWith, 2);
            panel.Controls.Add(new Label { Text = "开头文本:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 1);
            panel.Controls.Add(_txtStartsWith, 1, 1);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建段落包含关键词匹配控件
        /// </summary>
        private void CreateContainsControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "段落包含关键词匹配",
                Dock = DockStyle.Fill,
                Height = 200,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 15));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));

            _chkContains = new CheckBox
            {
                Text = "启用段落包含关键词匹配",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _txtNewKeyword = new TextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "输入关键词",
                Margin = new Padding(3),
                TextAlign = HorizontalAlignment.Center  // 文本框文字居中
            };

            _btnAddKeyword = new Button
            {
                Text = "添加",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            _btnRemoveKeyword = new Button
            {
                Text = "删除",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            _listContainsKeywords = new ListBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            panel.Controls.Add(_chkContains, 0, 0);
            panel.SetColumnSpan(_chkContains, 3);
            panel.Controls.Add(_txtNewKeyword, 0, 1);
            panel.Controls.Add(_btnAddKeyword, 1, 1);
            panel.Controls.Add(_btnRemoveKeyword, 2, 1);
            panel.Controls.Add(_listContainsKeywords, 0, 2);
            panel.SetColumnSpan(_listContainsKeywords, 3);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建段落结尾匹配控件
        /// </summary>
        private void CreateEndsWithControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "段落结尾匹配",
                Dock = DockStyle.Fill,
                Height = 140,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));

            _chkEndsWith = new CheckBox
            {
                Text = "启用段落结尾匹配",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _txtEndsWith = new TextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "输入段落结尾文本",
                Margin = new Padding(5, 2, 5, 2),
                TextAlign = HorizontalAlignment.Center  // 文本框文字居中
            };

            panel.Controls.Add(_chkEndsWith, 0, 0);
            panel.SetColumnSpan(_chkEndsWith, 2);
            panel.Controls.Add(new Label { Text = "结尾文本:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 1);
            panel.Controls.Add(_txtEndsWith, 1, 1);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建正则表达式匹配控件
        /// </summary>
        private void CreateRegexControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "正则表达式匹配",
                Dock = DockStyle.Fill,
                Height = 140,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));

            _chkRegex = new CheckBox
            {
                Text = "启用正则表达式匹配",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _txtRegexPattern = new TextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "输入正则表达式模式",
                Margin = new Padding(5, 2, 5, 2),
                TextAlign = HorizontalAlignment.Center  // 文本框文字居中
            };

            panel.Controls.Add(_chkRegex, 0, 0);
            panel.SetColumnSpan(_chkRegex, 2);
            panel.Controls.Add(new Label { Text = "正则模式:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 1);
            panel.Controls.Add(_txtRegexPattern, 1, 1);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建字符数限制控件
        /// </summary>
        private void CreateCharacterCountControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "段落字符数限制",
                Dock = DockStyle.Fill,
                Height = 140,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 2,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            _chkCharacterCount = new CheckBox
            {
                Text = "启用段落字符数限制",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _numMinCharCount = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 1,
                Maximum = 100000,
                Value = 1,
                TextAlign = HorizontalAlignment.Center,
                Margin = new Padding(2)
            };

            _numMaxCharCount = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 1,
                Maximum = 100000,
                Value = 1000,
                TextAlign = HorizontalAlignment.Center,
                Margin = new Padding(2)
            };

            panel.Controls.Add(_chkCharacterCount, 0, 0);
            panel.SetColumnSpan(_chkCharacterCount, 4);
            panel.Controls.Add(new Label { Text = "最小字符数:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 1);
            panel.Controls.Add(_numMinCharCount, 1, 1);
            panel.Controls.Add(new Label { Text = "最大字符数:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 2, 1);
            panel.Controls.Add(_numMaxCharCount, 3, 1);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建匹配选项控件
        /// </summary>
        private void CreateMatchingOptionsControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "匹配选项",
                Dock = DockStyle.Fill,
                Height = 110,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));

            _chkCaseSensitive = new CheckBox
            {
                Text = "区分大小写",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkWholeWord = new CheckBox
            {
                Text = "全词匹配",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            panel.Controls.Add(_chkCaseSensitive, 0, 0);
            panel.Controls.Add(_chkWholeWord, 1, 0);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建替换范围控件
        /// </summary>
        private void CreateReplacementScopeControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "替换范围",
                Dock = DockStyle.Fill,
                Height = 120,
                Padding = new Padding(15),
                Margin = new Padding(5),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold),
                ForeColor = Color.DarkBlue
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33f));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33f));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.34f));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            _chkIncludeNormalSlides = new CheckBox
            {
                Text = "普通幻灯片页",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 10F),
                Checked = true, // 默认全选
                TextAlign = ContentAlignment.MiddleLeft
            };

            _chkIncludeMasterSlides = new CheckBox
            {
                Text = "母版页",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 10F),
                Checked = true, // 默认全选
                TextAlign = ContentAlignment.MiddleLeft
            };

            _chkIncludeLayoutSlides = new CheckBox
            {
                Text = "母版版式页",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 10F),
                Checked = true, // 默认全选
                TextAlign = ContentAlignment.MiddleLeft
            };

            panel.Controls.Add(_chkIncludeNormalSlides, 0, 0);
            panel.Controls.Add(_chkIncludeMasterSlides, 1, 0);
            panel.Controls.Add(_chkIncludeLayoutSlides, 2, 0);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建段落格式标签页
        /// </summary>
        private void CreateParagraphFormatTab()
        {
            var tabPage = new TabPage("段落格式")
            {
                Padding = new Padding(15)
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(5)
            };

            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 6,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 设置行高 - 使用AutoSize确保内容完全显示
            for (int i = 0; i < 6; i++)
            {
                mainPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 启用段落格式设置
            CreateParagraphFormatEnableControls(mainPanel, 0);

            // 对齐方式
            CreateAlignmentControls(mainPanel, 1);

            // 缩进设置
            CreateIndentationControls(mainPanel, 2);

            // 间距设置
            CreateSpacingControls(mainPanel, 3);

            // 中文控制选项
            CreateChineseControlControls(mainPanel, 4);

            // 文本对齐方式
            CreateTextAlignmentControls(mainPanel, 5);

            scrollPanel.Controls.Add(mainPanel);
            tabPage.Controls.Add(scrollPanel);
            _tabControl.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建段落格式启用控件
        /// </summary>
        private void CreateParagraphFormatEnableControls(TableLayoutPanel parent, int row)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 50,
                Padding = new Padding(10)
            };

            _chkEnableParagraphFormat = new CheckBox
            {
                Text = "启用段落格式设置",
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                Location = new Point(15, 15)
            };

            panel.Controls.Add(_chkEnableParagraphFormat);
            parent.Controls.Add(panel, 0, row);
        }

        /// <summary>
        /// 创建对齐方式控件
        /// </summary>
        private void CreateAlignmentControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "对齐方式",
                Dock = DockStyle.Fill,
                Height = 110,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            _cmbAlignment = new CenteredComboBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(5, 8, 5, 8)
            };

            _cmbAlignment.Items.AddRange(new string[] { "左对齐", "居中", "右对齐", "两端对齐", "分散对齐" });

            panel.Controls.Add(new Label { Text = "对齐方式:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 0);
            panel.Controls.Add(_cmbAlignment, 1, 0);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建缩进设置控件
        /// </summary>
        private void CreateIndentationControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "缩进设置",
                Dock = DockStyle.Fill,
                Height = 180,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 3,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 45));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 45));

            _chkEnableIndentation = new CheckBox
            {
                Text = "启用缩进设置",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _numLeftIndent = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 0,
                Maximum = 1000,
                DecimalPlaces = 1,
                TextAlign = HorizontalAlignment.Center,
                Margin = new Padding(2)
            };

            _cmbSpecialIndent = new CenteredComboBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(2)
            };
            _cmbSpecialIndent.Items.AddRange(new string[] { "无", "首行缩进", "悬挂缩进" });

            _numSpecialIndentValue = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                TextAlign = HorizontalAlignment.Center,
                Margin = new Padding(2)
            };

            panel.Controls.Add(_chkEnableIndentation, 0, 0);
            panel.SetColumnSpan(_chkEnableIndentation, 4);
            panel.Controls.Add(new Label { Text = "左缩进(磅):", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 1);
            panel.Controls.Add(_numLeftIndent, 1, 1);
            panel.Controls.Add(new Label { Text = "特殊缩进:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 2);
            panel.Controls.Add(_cmbSpecialIndent, 1, 2);
            panel.Controls.Add(new Label { Text = "缩进值:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 2, 2);
            panel.Controls.Add(_numSpecialIndentValue, 3, 2);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建间距设置控件
        /// </summary>
        private void CreateSpacingControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "间距设置",
                Dock = DockStyle.Fill,
                Height = 180,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 6,
                RowCount = 3,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 45));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 45));

            _chkEnableSpacing = new CheckBox
            {
                Text = "启用间距设置",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _numSpaceBefore = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 0,
                Maximum = 1000,
                DecimalPlaces = 1,
                TextAlign = HorizontalAlignment.Center,
                Margin = new Padding(2)
            };

            _numSpaceAfter = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 0,
                Maximum = 1000,
                DecimalPlaces = 1,
                TextAlign = HorizontalAlignment.Center,
                Margin = new Padding(2)
            };

            _cmbLineSpacingType = new CenteredComboBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(2)
            };
            _cmbLineSpacingType.Items.AddRange(new string[] { "单倍行距", "1.5倍行距", "2倍行距", "多倍行距", "固定值" });

            _numLineSpacingValue = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 0.1m,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = 1.0m,
                TextAlign = HorizontalAlignment.Center,
                Margin = new Padding(2)
            };

            panel.Controls.Add(_chkEnableSpacing, 0, 0);
            panel.SetColumnSpan(_chkEnableSpacing, 6);
            panel.Controls.Add(new Label { Text = "段前(磅):", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 1);
            panel.Controls.Add(_numSpaceBefore, 1, 1);
            panel.Controls.Add(new Label { Text = "段后(磅):", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 2, 1);
            panel.Controls.Add(_numSpaceAfter, 3, 1);
            panel.Controls.Add(new Label { Text = "行距类型:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 2);
            panel.Controls.Add(_cmbLineSpacingType, 1, 2);
            panel.Controls.Add(new Label { Text = "行距值:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 2, 2);
            panel.Controls.Add(_numLineSpacingValue, 3, 2);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建中文控制选项控件
        /// </summary>
        private void CreateChineseControlControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "中文控制选项",
                Dock = DockStyle.Fill,
                Height = 180,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 5,
                Padding = new Padding(10)
            };

            for (int i = 0; i < 5; i++)
            {
                panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));
            }

            _chkEnableChineseControl = new CheckBox
            {
                Text = "启用中文控制选项",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkChineseCharacterControl = new CheckBox
            {
                Text = "按中文习惯控制中文首尾字符",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkAllowLatinWordBreak = new CheckBox
            {
                Text = "允许西文在单词中间换行",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkAllowPunctuationOverhang = new CheckBox
            {
                Text = "允许标点移除边界",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            panel.Controls.Add(_chkEnableChineseControl, 0, 0);
            panel.Controls.Add(_chkChineseCharacterControl, 0, 1);
            panel.Controls.Add(_chkAllowLatinWordBreak, 0, 2);
            panel.Controls.Add(_chkAllowPunctuationOverhang, 0, 3);
            // 第5行留空，用于间距

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建文本对齐方式控件
        /// </summary>
        private void CreateTextAlignmentControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "文本对齐方式",
                Dock = DockStyle.Fill,
                Height = 100,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            _cmbTextAlignment = new CenteredComboBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(5, 8, 5, 8)
            };

            _cmbTextAlignment.Items.AddRange(new string[] { "自动", "居中", "基线", "底部" });

            panel.Controls.Add(new Label { Text = "文本对齐:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 0);
            panel.Controls.Add(_cmbTextAlignment, 1, 0);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建字体格式标签页
        /// </summary>
        private void CreateFontFormatTab()
        {
            var tabPage = new TabPage("字体格式")
            {
                Padding = new Padding(15)
            };

            var scrollPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(5)
            };

            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 6,
                AutoSize = true,
                Padding = new Padding(10)
            };

            // 设置行高 - 使用AutoSize确保内容完全显示
            for (int i = 0; i < 6; i++)
            {
                mainPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 启用字体格式设置
            CreateFontFormatEnableControls(mainPanel, 0);

            // 字体设置
            CreateFontControls(mainPanel, 1);

            // 字体样式和大小
            CreateFontStyleSizeControls(mainPanel, 2);

            // 字体颜色
            CreateFontColorControls(mainPanel, 3);

            // 下划线设置
            CreateUnderlineControls(mainPanel, 4);

            // 文字效果
            CreateTextEffectsControls(mainPanel, 5);

            scrollPanel.Controls.Add(mainPanel);
            tabPage.Controls.Add(scrollPanel);
            _tabControl.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建字体格式启用控件
        /// </summary>
        private void CreateFontFormatEnableControls(TableLayoutPanel parent, int row)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 50,
                Padding = new Padding(10)
            };

            _chkEnableFontFormat = new CheckBox
            {
                Text = "启用字体格式设置",
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                Location = new Point(15, 15)
            };

            panel.Controls.Add(_chkEnableFontFormat);
            parent.Controls.Add(panel, 0, row);
        }

        /// <summary>
        /// 创建字体控件
        /// </summary>
        private void CreateFontControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "字体设置",
                Dock = DockStyle.Fill,
                Height = 110,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 1,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            _chkSetChineseFont = new CheckBox
            {
                Text = "设置中文字体",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _cmbChineseFontName = new CenteredComboBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(2)
            };
            _cmbChineseFontName.Items.AddRange(new string[] { "宋体", "黑体", "楷体", "仿宋", "微软雅黑", "华文宋体", "华文黑体" });

            _chkSetLatinFont = new CheckBox
            {
                Text = "设置西文字体",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _cmbLatinFontName = new CenteredComboBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(2)
            };
            _cmbLatinFontName.Items.AddRange(new string[] { "Arial", "Times New Roman", "Calibri", "Verdana", "Tahoma", "Georgia", "Comic Sans MS" });

            panel.Controls.Add(_chkSetChineseFont, 0, 0);
            panel.Controls.Add(_cmbChineseFontName, 1, 0);
            panel.Controls.Add(_chkSetLatinFont, 2, 0);
            panel.Controls.Add(_cmbLatinFontName, 3, 0);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建字体样式和大小控件
        /// </summary>
        private void CreateFontStyleSizeControls(TableLayoutPanel parent, int row)
        {
            var groupBox = new GroupBox
            {
                Text = "字体样式和大小",
                Dock = DockStyle.Fill,
                Height = 110,
                Padding = new Padding(15),
                Margin = new Padding(5)
            };

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 1,
                Padding = new Padding(10)
            };

            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            _cmbFontStyle = new CenteredComboBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(2)
            };
            _cmbFontStyle.Items.AddRange(new string[] { "常规", "倾斜", "加粗", "倾斜加粗" });

            _chkSetFontSize = new CheckBox
            {
                Text = "设置字体大小",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _numFontSize = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 6,
                Maximum = 200,
                Value = 12,
                TextAlign = HorizontalAlignment.Center,
                Margin = new Padding(2)
            };

            panel.Controls.Add(new Label { Text = "字体样式:", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 0, 0);
            panel.Controls.Add(_cmbFontStyle, 1, 0);
            panel.Controls.Add(_chkSetFontSize, 2, 0);
            panel.Controls.Add(_numFontSize, 3, 0);

            groupBox.Controls.Add(panel);
            parent.Controls.Add(groupBox, 0, row);
        }

        /// <summary>
        /// 创建按钮区域
        /// </summary>
        private void CreateButtonArea(TableLayoutPanel parent)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(15, 10, 15, 15) // 增加边距，优化视觉效果
            };

            var buttonLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 6,
                RowCount = 1,
                Height = 50 // 固定按钮区域高度
            };

            // 优化列宽设置 - 更合理的按钮布局
            buttonLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 左侧空白区域
            buttonLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 确定按钮
            buttonLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 15));  // 按钮间距
            buttonLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 取消按钮

            _btnOK = new Button
            {
                Text = "确定(&O)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 10F), // 增大字体
                UseVisualStyleBackColor = true,
                FlatStyle = FlatStyle.Standard,
                Height = 35, // 固定按钮高度
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                DialogResult = DialogResult.OK
            };

            _btnCancel = new Button
            {
                Text = "取消(&C)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 10F), // 增大字体
                UseVisualStyleBackColor = true,
                FlatStyle = FlatStyle.Standard,
                Height = 35, // 固定按钮高度
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                DialogResult = DialogResult.Cancel
            };

            // 添加按钮到布局，使用间距列分隔
            buttonLayout.Controls.Add(_btnOK, 1, 0);
            buttonLayout.Controls.Add(_btnCancel, 3, 0);

            panel.Controls.Add(buttonLayout);
            parent.Controls.Add(panel, 0, 2);

            // 设置默认按钮
            this.AcceptButton = _btnOK;
            this.CancelButton = _btnCancel;
        }

        #endregion
    }
}
