/// <summary>
/// PPT格式转换设置窗体 - 数据处理方法部分
/// 负责配置数据的加载、保存和控件值的设置获取
/// 包含PDF、图片、Web、其他格式等各类转换设置的数据绑定逻辑
/// 功能包括：配置文件与UI控件的双向数据绑定、主开关状态管理、控件值类型转换
/// 确保配置加载顺序正确，主开关与子功能状态逻辑一致
/// </summary>

using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// PPT格式转换设置窗体 - 数据处理方法部分
    /// 负责配置数据的加载、保存和控件值的设置获取
    /// 包含PDF、图片、Web、其他格式等各类转换设置的数据绑定逻辑
    /// </summary>
    public partial class PPTFormatConversionForm
    {
        #region 数据加载方法

        /// <summary>
        /// 加载图片设置
        /// </summary>
        private void LoadImageSettings()
        {
            var panel = _tabPanels["Image"];
            var settings = _currentSettings.ImageSettings;

            // 修复图片转换主开关逻辑：任何一个图片格式启用时，主开关就应该启用
            bool anyImageFormatEnabled = settings.EnablePNGConversion || settings.EnableJPEGConversion ||
                                       settings.EnableBMPConversion || settings.EnableTIFFConversion || settings.EnableSVGConversion;

            // 先设置子功能状态
            SetControlValue(panel, "chkImagePNG", settings.EnablePNGConversion);
            SetControlValue(panel, "chkImageJPEG", settings.EnableJPEGConversion);
            SetControlValue(panel, "chkImageBMP", settings.EnableBMPConversion);
            SetControlValue(panel, "chkImageTIFF", settings.EnableTIFFConversion);
            SetControlValue(panel, "chkImageSVG", settings.EnableSVGConversion);

            // 再设置主开关状态，确保逻辑一致
            SetControlValue(panel, "chkImageMaster", anyImageFormatEnabled);

            // 设置其他参数
            SetControlValue(panel, "numImageWidth", settings.ImageWidth);
            SetControlValue(panel, "numImageHeight", settings.ImageHeight);
            SetControlValue(panel, "chkImageAspectRatio", settings.MaintainAspectRatio);
            SetControlValue(panel, "numImageDPI", settings.DPI);
            SetControlValue(panel, "numImageJPEGQuality", settings.JPEGQuality);
            SetControlValue(panel, "chkImageAllSlides", settings.ConvertAllSlides);
            SetControlValue(panel, "chkImageSlideRange", settings.UseSlideRange);
            SetControlValue(panel, "numImageStartSlide", settings.StartSlide);
            SetControlValue(panel, "numImageEndSlide", settings.EndSlide);
            SetControlValue(panel, "txtImageNaming", settings.NamingPattern);
        }

        /// <summary>
        /// 加载Web设置
        /// </summary>
        private void LoadWebSettings()
        {
            var panel = _tabPanels["Web"];
            var settings = _currentSettings.WebSettings;

            // 先设置子功能状态
            SetControlValue(panel, "chkWebHTML", settings.EnableHTMLConversion);
            SetControlValue(panel, "chkHTMLEmbedImages", settings.EmbedImages);
            SetControlValue(panel, "chkHTMLEmbedCSS", settings.EmbedCSS);
            SetControlValue(panel, "chkHTMLEmbedJS", settings.EmbedJavaScript);
            SetControlValue(panel, "cmbHTMLVersion", settings.HTMLVersion);
            SetControlValue(panel, "chkHTMLResponsive", settings.ResponsiveDesign);
            SetControlValue(panel, "chkWebXAML", settings.EnableXAMLConversion);
            SetControlValue(panel, "chkXAMLAnimations", settings.IncludeAnimations);
            SetControlValue(panel, "chkXAMLSilverlight", settings.OptimizeForSilverlight);

            // 再设置主开关状态，确保逻辑一致
            SetControlValue(panel, "chkWebMaster", settings.EnableHTMLConversion || settings.EnableXAMLConversion);
        }

        /// <summary>
        /// 加载其他格式设置
        /// </summary>
        private void LoadOtherSettings()
        {
            var panel = _tabPanels["Other"];
            var settings = _currentSettings.OtherSettings;

            // 先设置子功能状态
            SetControlValue(panel, "chkOtherThumbnail", settings.EnableThumbnailGeneration);
            SetControlValue(panel, "numThumbnailWidth", settings.ThumbnailWidth);
            SetControlValue(panel, "numThumbnailHeight", settings.ThumbnailHeight);
            SetControlValue(panel, "cmbThumbnailFormat", settings.ThumbnailFormat);
            SetControlValue(panel, "chkThumbnailAllSlides", settings.GenerateForAllSlides);

            // 再设置主开关状态，确保逻辑一致
            SetControlValue(panel, "chkOtherMaster", settings.EnableThumbnailGeneration);
        }

        #endregion

        #region 数据保存方法

        /// <summary>
        /// 保存图片设置
        /// </summary>
        private void SaveImageSettings()
        {
            var panel = _tabPanels["Image"];
            var settings = _currentSettings.ImageSettings;

            settings.EnablePNGConversion = GetControlValue<bool>(panel, "chkImagePNG");
            settings.EnableJPEGConversion = GetControlValue<bool>(panel, "chkImageJPEG");
            settings.EnableBMPConversion = GetControlValue<bool>(panel, "chkImageBMP");
            settings.EnableTIFFConversion = GetControlValue<bool>(panel, "chkImageTIFF");
            settings.EnableSVGConversion = GetControlValue<bool>(panel, "chkImageSVG");
            settings.ImageWidth = GetControlValue<int>(panel, "numImageWidth");
            settings.ImageHeight = GetControlValue<int>(panel, "numImageHeight");
            settings.MaintainAspectRatio = GetControlValue<bool>(panel, "chkImageAspectRatio");
            settings.DPI = GetControlValue<int>(panel, "numImageDPI");
            settings.JPEGQuality = GetControlValue<int>(panel, "numImageJPEGQuality");
            settings.ConvertAllSlides = GetControlValue<bool>(panel, "chkImageAllSlides");
            settings.UseSlideRange = GetControlValue<bool>(panel, "chkImageSlideRange");
            settings.StartSlide = GetControlValue<int>(panel, "numImageStartSlide");
            settings.EndSlide = GetControlValue<int>(panel, "numImageEndSlide");
            settings.NamingPattern = GetControlValue<string>(panel, "txtImageNaming") ?? "幻灯片{0}";
        }

        /// <summary>
        /// 保存Web设置
        /// </summary>
        private void SaveWebSettings()
        {
            var panel = _tabPanels["Web"];
            var settings = _currentSettings.WebSettings;

            settings.EnableHTMLConversion = GetControlValue<bool>(panel, "chkWebHTML");
            settings.EmbedImages = GetControlValue<bool>(panel, "chkHTMLEmbedImages");
            settings.EmbedCSS = GetControlValue<bool>(panel, "chkHTMLEmbedCSS");
            settings.EmbedJavaScript = GetControlValue<bool>(panel, "chkHTMLEmbedJS");
            settings.HTMLVersion = GetControlValue<string>(panel, "cmbHTMLVersion") ?? "HTML5";
            settings.ResponsiveDesign = GetControlValue<bool>(panel, "chkHTMLResponsive");
            settings.EnableXAMLConversion = GetControlValue<bool>(panel, "chkWebXAML");
            settings.IncludeAnimations = GetControlValue<bool>(panel, "chkXAMLAnimations");
            settings.OptimizeForSilverlight = GetControlValue<bool>(panel, "chkXAMLSilverlight");
        }

        /// <summary>
        /// 保存其他格式设置
        /// </summary>
        private void SaveOtherSettings()
        {
            var panel = _tabPanels["Other"];
            var settings = _currentSettings.OtherSettings;

            settings.EnableThumbnailGeneration = GetControlValue<bool>(panel, "chkOtherThumbnail");
            settings.ThumbnailWidth = GetControlValue<int>(panel, "numThumbnailWidth");
            settings.ThumbnailHeight = GetControlValue<int>(panel, "numThumbnailHeight");
            settings.ThumbnailFormat = GetControlValue<string>(panel, "cmbThumbnailFormat") ?? "PNG";
            settings.GenerateForAllSlides = GetControlValue<bool>(panel, "chkThumbnailAllSlides");
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 设置控件值
        /// </summary>
        private void SetControlValue(Control parent, string controlName, object value)
        {
            try
            {
                var control = FindControlByName(parent, controlName);
                if (control == null) return;

                switch (control)
                {
                    case CheckBox checkBox:
                        checkBox.Checked = Convert.ToBoolean(value);
                        break;
                    case NumericUpDown numericUpDown:
                        numericUpDown.Value = Convert.ToDecimal(value);
                        break;
                    case TextBox textBox:
                        textBox.Text = value?.ToString() ?? "";
                        break;
                    case ComboBox comboBox:
                        var text = value?.ToString();
                        if (!string.IsNullOrEmpty(text) && comboBox.Items.Contains(text))
                            comboBox.SelectedItem = text;
                        break;
                }
            }
            catch (Exception ex)
            {
                // 忽略设置控件值时的错误，避免影响整体加载
                System.Diagnostics.Debug.WriteLine($"设置控件 {controlName} 值时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取控件值
        /// </summary>
        private T GetControlValue<T>(Control parent, string controlName)
        {
            try
            {
                var control = FindControlByName(parent, controlName);
                if (control == null) return default(T)!;

                object? value = null;
                switch (control)
                {
                    case CheckBox checkBox:
                        value = checkBox.Checked;
                        break;
                    case NumericUpDown numericUpDown:
                        value = numericUpDown.Value;
                        break;
                    case TextBox textBox:
                        value = textBox.Text ?? "";
                        break;
                    case ComboBox comboBox:
                        value = comboBox.SelectedItem?.ToString() ?? "";
                        break;
                }

                if (value == null) return default(T)!;

                if (typeof(T) == typeof(int))
                    return (T)(object)Convert.ToInt32(value);
                else if (typeof(T) == typeof(bool))
                    return (T)(object)Convert.ToBoolean(value);
                else if (typeof(T) == typeof(string))
                    return (T)(object)(value?.ToString() ?? "");
                else
                    return (T)value;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取控件 {controlName} 值时发生错误: {ex.Message}");
                return default(T)!;
            }
        }

        /// <summary>
        /// 根据名称查找控件
        /// </summary>
        private Control? FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }

            return null;
        }

        #endregion
    }
}
