using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 自定义字体方案设置窗体
    /// </summary>
    public partial class CustomFontSchemeForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 自定义字体方案设置
        /// </summary>
        public CustomFontSchemeSettings Settings { get; private set; } = new CustomFontSchemeSettings();

        #region 控件字段
        private TextBox? _txtSchemeName;
        private ComboBox? _cmbTitleFontChinese, _cmbTitleFontEnglish, _cmbBodyFontChinese, _cmbBodyFontEnglish;
        private NumericUpDown? _nudTitleFontSize, _nudBodyFontSize;
        private CheckBox? _chkTitleBold, _chkTitleItalic, _chkBodyBold, _chkBodyItalic;
        private Button? _btnTitleFontColor, _btnBodyFontColor;
        private Label? _lblPreview;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;

        // 颜色存储
        private Color _titleFontColor = Color.Black;
        private Color _bodyFontColor = Color.Black;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public CustomFontSchemeForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        /// <summary>
        /// 构造函数（编辑模式）
        /// </summary>
        /// <param name="schemeName">要编辑的方案名称</param>
        public CustomFontSchemeForm(string schemeName) : this()
        {
            LoadSchemeForEdit(schemeName);
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CustomFontSchemeForm));
            SuspendLayout();
            // 
            // CustomFontSchemeForm
            // 
            ClientSize = new Size(584, 511);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "CustomFontSchemeForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "自定义字体方案";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateBasicInfoGroup();
            CreateTitleFontGroup();
            CreateBodyFontGroup();
            CreatePreviewGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建基本信息组
        /// </summary>
        private void CreateBasicInfoGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "方案信息",
                Location = new Point(20, 20),
                Size = new Size(550, 60),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            var lblName = new Label
            {
                Text = "方案名称:",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtSchemeName = new TextBox
            {
                Location = new Point(110, 23),
                Size = new Size(200, 23),
                Font = new Font("Microsoft YaHei UI", 9F),
                Text = "自定义字体方案"
            };

            groupBox.Controls.AddRange(new Control[] { lblName, _txtSchemeName });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建标题字体设置组
        /// </summary>
        private void CreateTitleFontGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "标题字体设置",
                Location = new Point(20, 90),
                Size = new Size(550, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            // 中文字体
            var lblChineseFont = new Label
            {
                Text = "中文字体:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitleFontChinese = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 西文字体
            var lblEnglishFont = new Label
            {
                Text = "西文字体:",
                Location = new Point(240, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitleFontEnglish = new ComboBox
            {
                Location = new Point(320, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 字体大小
            var lblSize = new Label
            {
                Text = "字体大小:",
                Location = new Point(460, 25),
                Size = new Size(60, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudTitleFontSize = new NumericUpDown
            {
                Location = new Point(460, 50),
                Size = new Size(60, 23),
                Minimum = 8,
                Maximum = 72,
                Value = 18,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 字体样式
            _chkTitleBold = new CheckBox
            {
                Text = "粗体",
                Location = new Point(20, 55),
                Size = new Size(60, 20),
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkTitleItalic = new CheckBox
            {
                Text = "斜体",
                Location = new Point(90, 55),
                Size = new Size(60, 20),
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 字体颜色
            var lblColor = new Label
            {
                Text = "字体颜色:",
                Location = new Point(170, 55),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnTitleFontColor = new Button
            {
                Location = new Point(250, 53),
                Size = new Size(60, 23),
                BackColor = _titleFontColor,
                FlatStyle = FlatStyle.Flat,
                Text = "选择"
            };
            _btnTitleFontColor.Click += BtnTitleFontColor_Click;

            groupBox.Controls.AddRange(new Control[] {
                lblChineseFont, _cmbTitleFontChinese, lblEnglishFont, _cmbTitleFontEnglish,
                lblSize, _nudTitleFontSize, _chkTitleBold, _chkTitleItalic,
                lblColor, _btnTitleFontColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建正文字体设置组
        /// </summary>
        private void CreateBodyFontGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "正文字体设置",
                Location = new Point(20, 220),
                Size = new Size(550, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            // 中文字体
            var lblChineseFont = new Label
            {
                Text = "中文字体:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbBodyFontChinese = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 西文字体
            var lblEnglishFont = new Label
            {
                Text = "西文字体:",
                Location = new Point(240, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbBodyFontEnglish = new ComboBox
            {
                Location = new Point(320, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 字体大小
            var lblSize = new Label
            {
                Text = "字体大小:",
                Location = new Point(460, 25),
                Size = new Size(60, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudBodyFontSize = new NumericUpDown
            {
                Location = new Point(460, 50),
                Size = new Size(60, 23),
                Minimum = 8,
                Maximum = 72,
                Value = 12,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 字体样式
            _chkBodyBold = new CheckBox
            {
                Text = "粗体",
                Location = new Point(20, 55),
                Size = new Size(60, 20),
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _chkBodyItalic = new CheckBox
            {
                Text = "斜体",
                Location = new Point(90, 55),
                Size = new Size(60, 20),
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 字体颜色
            var lblColor = new Label
            {
                Text = "字体颜色:",
                Location = new Point(170, 55),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnBodyFontColor = new Button
            {
                Location = new Point(250, 53),
                Size = new Size(60, 23),
                BackColor = _bodyFontColor,
                FlatStyle = FlatStyle.Flat,
                Text = "选择"
            };
            _btnBodyFontColor.Click += BtnBodyFontColor_Click;

            groupBox.Controls.AddRange(new Control[] {
                lblChineseFont, _cmbBodyFontChinese, lblEnglishFont, _cmbBodyFontEnglish,
                lblSize, _nudBodyFontSize, _chkBodyBold, _chkBodyItalic,
                lblColor, _btnBodyFontColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建预览组
        /// </summary>
        private void CreatePreviewGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "预览",
                Location = new Point(20, 350),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _lblPreview = new Label
            {
                Text = "标题字体预览 - Title Font Preview\n正文字体预览 - Body Font Preview",
                Location = new Point(20, 25),
                Size = new Size(510, 60),
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.White
            };

            groupBox.Controls.Add(_lblPreview);
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button { Text = "确定(&O)", Location = new Point(300, 470), Size = new Size(80, 30) };
            _btnCancel = new Button { Text = "取消(&C)", Location = new Point(390, 470), Size = new Size(80, 30) };
            _btnReset = new Button { Text = "重置(&R)", Location = new Point(480, 470), Size = new Size(80, 30) };
            _btnPreview = new Button { Text = "预览(&P)", Location = new Point(210, 470), Size = new Size(80, 30) };

            _btnOK.Click += BtnOK_Click;
            _btnCancel.Click += BtnCancel_Click;
            _btnReset.Click += BtnReset_Click;
            _btnPreview.Click += BtnPreview_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset, _btnPreview });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 加载中文字体列表
            var chineseFonts = new string[]
            {
                "Microsoft YaHei UI", "Microsoft YaHei", "SimSun", "SimHei", "KaiTi",
                "FangSong", "LiSu", "YouYuan", "STXihei", "STKaiti"
            };

            // 加载西文字体列表
            var englishFonts = new string[]
            {
                "Arial", "Times New Roman", "Calibri", "Verdana", "Tahoma",
                "Georgia", "Trebuchet MS", "Comic Sans MS", "Impact", "Courier New"
            };

            _cmbTitleFontChinese?.Items.AddRange(chineseFonts);
            _cmbTitleFontEnglish?.Items.AddRange(englishFonts);
            _cmbBodyFontChinese?.Items.AddRange(chineseFonts);
            _cmbBodyFontEnglish?.Items.AddRange(englishFonts);

            // 设置默认选择
            if (_cmbTitleFontChinese != null) _cmbTitleFontChinese.SelectedItem = "Microsoft YaHei UI";
            if (_cmbTitleFontEnglish != null) _cmbTitleFontEnglish.SelectedItem = "Arial";
            if (_cmbBodyFontChinese != null) _cmbBodyFontChinese.SelectedItem = "Microsoft YaHei UI";
            if (_cmbBodyFontEnglish != null) _cmbBodyFontEnglish.SelectedItem = "Arial";

            // 绑定事件
            if (_cmbTitleFontChinese != null) _cmbTitleFontChinese.SelectedIndexChanged += UpdatePreview;
            if (_cmbTitleFontEnglish != null) _cmbTitleFontEnglish.SelectedIndexChanged += UpdatePreview;
            if (_cmbBodyFontChinese != null) _cmbBodyFontChinese.SelectedIndexChanged += UpdatePreview;
            if (_cmbBodyFontEnglish != null) _cmbBodyFontEnglish.SelectedIndexChanged += UpdatePreview;
            if (_nudTitleFontSize != null) _nudTitleFontSize.ValueChanged += UpdatePreview;
            if (_nudBodyFontSize != null) _nudBodyFontSize.ValueChanged += UpdatePreview;
            if (_chkTitleBold != null) _chkTitleBold.CheckedChanged += UpdatePreview;
            if (_chkTitleItalic != null) _chkTitleItalic.CheckedChanged += UpdatePreview;
            if (_chkBodyBold != null) _chkBodyBold.CheckedChanged += UpdatePreview;
            if (_chkBodyItalic != null) _chkBodyItalic.CheckedChanged += UpdatePreview;

            UpdatePreview(null, EventArgs.Empty);
        }

        /// <summary>
        /// 加载方案进行编辑
        /// </summary>
        /// <param name="schemeName">方案名称</param>
        private void LoadSchemeForEdit(string schemeName)
        {
            if (_txtSchemeName != null)
                _txtSchemeName.Text = schemeName;

            // 这里可以从配置文件或数据库加载方案信息
            // 目前使用默认值
            UpdatePreview(null, EventArgs.Empty);
        }

        /// <summary>
        /// 更新预览
        /// </summary>
        private void UpdatePreview(object? sender, EventArgs e)
        {
            if (_lblPreview == null) return;

            try
            {
                // 创建标题字体
                var titleFontName = _cmbTitleFontChinese?.SelectedItem?.ToString() ?? "Microsoft YaHei UI";
                var titleFontSize = (float)(_nudTitleFontSize?.Value ?? 18);
                var titleFontStyle = FontStyle.Regular;
                if (_chkTitleBold?.Checked == true) titleFontStyle |= FontStyle.Bold;
                if (_chkTitleItalic?.Checked == true) titleFontStyle |= FontStyle.Italic;

                // 创建正文字体
                var bodyFontName = _cmbBodyFontChinese?.SelectedItem?.ToString() ?? "Microsoft YaHei UI";
                var bodyFontSize = (float)(_nudBodyFontSize?.Value ?? 12);
                var bodyFontStyle = FontStyle.Regular;
                if (_chkBodyBold?.Checked == true) bodyFontStyle |= FontStyle.Bold;
                if (_chkBodyItalic?.Checked == true) bodyFontStyle |= FontStyle.Italic;

                // 更新预览文本
                _lblPreview.Text = $"标题字体预览 ({titleFontName}, {titleFontSize}pt)\nBody Font Preview ({bodyFontName}, {bodyFontSize}pt)";

                // 应用字体（使用较小的字体以适应预览区域）
                var previewTitleFont = new Font(titleFontName, Math.Min(titleFontSize * 0.6f, 14), titleFontStyle);
                _lblPreview.Font = previewTitleFont;
                _lblPreview.ForeColor = _titleFontColor;
            }
            catch (Exception)
            {
                // 如果字体创建失败，使用默认字体
                _lblPreview.Font = new Font("Microsoft YaHei UI", 10F);
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 标题字体颜色按钮点击事件
        /// </summary>
        private void BtnTitleFontColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = _titleFontColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _titleFontColor = colorDialog.Color;
                if (_btnTitleFontColor != null)
                    _btnTitleFontColor.BackColor = _titleFontColor;
                UpdatePreview(null, EventArgs.Empty);
            }
        }

        /// <summary>
        /// 正文字体颜色按钮点击事件
        /// </summary>
        private void BtnBodyFontColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = _bodyFontColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _bodyFontColor = colorDialog.Color;
                if (_btnBodyFontColor != null)
                    _btnBodyFontColor.BackColor = _bodyFontColor;
                UpdatePreview(null, EventArgs.Empty);
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(_txtSchemeName?.Text))
                {
                    MessageBox.Show("请输入方案名称。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _txtSchemeName?.Focus();
                    return;
                }

                // 收集设置
                Settings = new CustomFontSchemeSettings
                {
                    SchemeName = _txtSchemeName.Text.Trim(),
                    TitleFontChinese = _cmbTitleFontChinese?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    TitleFontEnglish = _cmbTitleFontEnglish?.SelectedItem?.ToString() ?? "Arial",
                    TitleFontSize = (int)(_nudTitleFontSize?.Value ?? 18),
                    TitleBold = _chkTitleBold?.Checked ?? false,
                    TitleItalic = _chkTitleItalic?.Checked ?? false,
                    TitleFontColor = _titleFontColor,
                    BodyFontChinese = _cmbBodyFontChinese?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    BodyFontEnglish = _cmbBodyFontEnglish?.SelectedItem?.ToString() ?? "Arial",
                    BodyFontSize = (int)(_nudBodyFontSize?.Value ?? 12),
                    BodyBold = _chkBodyBold?.Checked ?? false,
                    BodyItalic = _chkBodyItalic?.Checked ?? false,
                    BodyFontColor = _bodyFontColor
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            // 重置为默认值
            if (_cmbTitleFontChinese != null) _cmbTitleFontChinese.SelectedItem = "Microsoft YaHei UI";
            if (_cmbTitleFontEnglish != null) _cmbTitleFontEnglish.SelectedItem = "Arial";
            if (_cmbBodyFontChinese != null) _cmbBodyFontChinese.SelectedItem = "Microsoft YaHei UI";
            if (_cmbBodyFontEnglish != null) _cmbBodyFontEnglish.SelectedItem = "Arial";
            if (_nudTitleFontSize != null) _nudTitleFontSize.Value = 18;
            if (_nudBodyFontSize != null) _nudBodyFontSize.Value = 12;
            if (_chkTitleBold != null) _chkTitleBold.Checked = false;
            if (_chkTitleItalic != null) _chkTitleItalic.Checked = false;
            if (_chkBodyBold != null) _chkBodyBold.Checked = false;
            if (_chkBodyItalic != null) _chkBodyItalic.Checked = false;

            _titleFontColor = Color.Black;
            _bodyFontColor = Color.Black;
            if (_btnTitleFontColor != null) _btnTitleFontColor.BackColor = _titleFontColor;
            if (_btnBodyFontColor != null) _btnBodyFontColor.BackColor = _bodyFontColor;

            UpdatePreview(null, EventArgs.Empty);
        }

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能将显示应用当前字体方案后的效果。\n\n" +
                           "此功能将在后续版本中实现。", "预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion
    }

    /// <summary>
    /// 自定义字体方案设置类
    /// </summary>
    public class CustomFontSchemeSettings
    {
        public string SchemeName { get; set; } = "自定义字体方案";
        public string TitleFontChinese { get; set; } = "Microsoft YaHei UI";
        public string TitleFontEnglish { get; set; } = "Arial";
        public int TitleFontSize { get; set; } = 18;
        public bool TitleBold { get; set; } = false;
        public bool TitleItalic { get; set; } = false;
        public Color TitleFontColor { get; set; } = Color.Black;
        public string BodyFontChinese { get; set; } = "Microsoft YaHei UI";
        public string BodyFontEnglish { get; set; } = "Arial";
        public int BodyFontSize { get; set; } = 12;
        public bool BodyBold { get; set; } = false;
        public bool BodyItalic { get; set; } = false;
        public Color BodyFontColor { get; set; } = Color.Black;
    }
}
