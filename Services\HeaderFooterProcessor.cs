using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Aspose.Slides;
using Aspose.Slides.Export;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 页眉页脚处理器
    /// 基于Aspose.Slides API实现PowerPoint页眉页脚的各种操作
    /// </summary>
    public class HeaderFooterProcessor
    {
        #region 私有字段

        /// <summary>
        /// 页眉页脚设置
        /// </summary>
        private readonly HeaderFooterSettings _settings;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="settings">页眉页脚设置</param>
        public HeaderFooterProcessor(HeaderFooterSettings settings)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 处理PowerPoint文件的页眉页脚
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>处理结果</returns>
        public ProcessResult ProcessFile(string filePath)
        {
            var result = new ProcessResult { FilePath = filePath };

            try
            {
                if (!File.Exists(filePath))
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "文件不存在";
                    return result;
                }

                using var presentation = new Presentation(filePath);
                result = ProcessPresentation(presentation, filePath);

                if (result.IsSuccess)
                {
                    // 保存文件
                    presentation.Save(filePath, SaveFormat.Pptx);
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"处理文件时发生错误：{ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 处理已加载的演示文稿的页眉页脚
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        /// <param name="filePath">文件路径（用于日志）</param>
        /// <returns>处理结果</returns>
        public ProcessResult ProcessPresentation(Presentation presentation, string filePath = "")
        {
            var result = new ProcessResult { FilePath = filePath };

            try
            {
                // 执行删除操作
                if (HasDeletionOperations())
                {
                    ExecuteDeletionOperations(presentation);
                }

                // 执行演示文稿级别设置
                if (_settings.PresentationSettings.IsEnabled)
                {
                    ApplyPresentationHeaderFooter(presentation);
                }

                // 执行普通幻灯片设置
                if (_settings.SlideSettings.IsEnabled)
                {
                    ApplySlideHeaderFooter(presentation);
                }

                // 执行母版幻灯片设置
                if (_settings.MasterSlideSettings.IsEnabled)
                {
                    ApplyMasterSlideHeaderFooter(presentation);
                }

                // 执行布局幻灯片设置
                if (_settings.LayoutSlideSettings.IsEnabled)
                {
                    ApplyLayoutSlideHeaderFooter(presentation);
                }

                // 执行备注幻灯片设置
                if (_settings.NotesSlideSettings.IsEnabled)
                {
                    ApplyNotesSlideHeaderFooter(presentation);
                }

                result.IsSuccess = true;
                result.Message = "页眉页脚处理完成";
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"处理演示文稿时发生错误：{ex.Message}";
            }

            return result;
        }

        #endregion

        #region 删除操作

        /// <summary>
        /// 检查是否有删除操作
        /// </summary>
        /// <returns>是否有删除操作</returns>
        private bool HasDeletionOperations()
        {
            var deletion = _settings.DeletionSettings;
            return deletion.DeleteAllFooters ||
                   deletion.DeleteAllSlideNumbers || deletion.DeleteAllDateTimes;
        }

        /// <summary>
        /// 执行删除操作
        /// 功能开关：控制是否删除指定范围内幻灯片的页眉页脚元素
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        private void ExecuteDeletionOperations(Presentation presentation)
        {
            var deletion = _settings.DeletionSettings;
            var slideIndexes = GetSlideIndexesForDeletion(presentation);

            foreach (var slideIndex in slideIndexes)
            {
                if (slideIndex >= 0 && slideIndex < presentation.Slides.Count)
                {
                    var slide = presentation.Slides[slideIndex];
                    var headerFooterManager = slide.HeaderFooterManager;



                    // 删除页脚 - 功能开关：控制是否删除所有页脚
                    if (deletion.DeleteAllFooters)
                    {
                        headerFooterManager.SetFooterVisibility(false);
                        headerFooterManager.SetFooterText("");
                    }

                    // 删除页码 - 功能开关：控制是否删除所有页码
                    if (deletion.DeleteAllSlideNumbers)
                    {
                        headerFooterManager.SetSlideNumberVisibility(false);
                    }

                    // 删除日期时间 - 功能开关：控制是否删除所有日期时间
                    if (deletion.DeleteAllDateTimes)
                    {
                        headerFooterManager.SetDateTimeVisibility(false);
                        headerFooterManager.SetDateTimeText("");
                    }
                }
            }
        }

        /// <summary>
        /// 获取删除操作的幻灯片索引列表
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        /// <returns>幻灯片索引列表</returns>
        private List<int> GetSlideIndexesForDeletion(Presentation presentation)
        {
            var deletion = _settings.DeletionSettings;
            var indexes = new List<int>();

            var scope = GetSafeString(deletion.DeletionScope, "All");
            switch (scope)
            {
                case "All":
                    for (int i = 0; i < presentation.Slides.Count; i++)
                    {
                        indexes.Add(i);
                    }
                    break;

                case "SlideRange":
                    var range = GetSafeString(deletion.SlideRange, "1-");
                    indexes.AddRange(ParseSlideRange(range, presentation.Slides.Count));
                    break;

                case "SelectedSlides":
                    var selectedIndexes = deletion.SelectedSlideIndexes ?? new List<int>();
                    indexes.AddRange(selectedIndexes);
                    break;
            }

            return indexes;
        }

        #endregion

        #region 演示文稿级别设置

        /// <summary>
        /// 应用演示文稿级别页眉页脚设置
        /// 功能开关：控制是否对整个演示文稿应用页眉页脚设置，影响所有幻灯片、母版、布局和备注
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        private void ApplyPresentationHeaderFooter(Presentation presentation)
        {
            var settings = _settings.PresentationSettings;
            var headerFooterManager = presentation.HeaderFooterManager;

            // 设置页脚 - 功能开关：控制演示文稿级别页脚的显示和内容
            if (settings.ShowFooter)
            {
                headerFooterManager.SetAllFootersVisibility(true);
                headerFooterManager.SetAllFootersText(GetSafeString(settings.FooterText));
            }
            else
            {
                headerFooterManager.SetAllFootersVisibility(false);
            }



            // 设置页码 - 功能开关：控制演示文稿级别页码的显示
            if (settings.ShowSlideNumber)
            {
                headerFooterManager.SetAllSlideNumbersVisibility(true);
            }
            else
            {
                headerFooterManager.SetAllSlideNumbersVisibility(false);
            }

            // 设置日期时间 - 功能开关：控制演示文稿级别日期时间的显示和格式
            if (settings.ShowDateTime)
            {
                headerFooterManager.SetAllDateTimesVisibility(true);

                // 根据自动更新设置决定使用当前时间还是自定义文本
                string dateTimeText;
                if (settings.AutoUpdateDateTime)
                {
                    // 自动更新：使用当前日期时间并按指定格式格式化
                    var format = GetSafeString(settings.DateTimeFormat, "yyyy/MM/dd");
                    dateTimeText = DateTime.Now.ToString(format);
                }
                else
                {
                    // 使用自定义文本
                    dateTimeText = GetSafeString(settings.DateTimeText);
                }

                headerFooterManager.SetAllDateTimesText(dateTimeText);
            }
            else
            {
                headerFooterManager.SetAllDateTimesVisibility(false);
            }
        }

        #endregion

        #region 普通幻灯片设置

        /// <summary>
        /// 应用普通幻灯片页眉页脚设置
        /// 功能开关：控制是否对指定的普通幻灯片应用页眉页脚设置
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        private void ApplySlideHeaderFooter(Presentation presentation)
        {
            var settings = _settings.SlideSettings;
            var slideIndexes = GetSlideIndexesForApplication(presentation, settings.ApplyScope,
                settings.SlideRange, settings.SelectedSlideIndexes);

            foreach (var slideIndex in slideIndexes)
            {
                if (slideIndex >= 0 && slideIndex < presentation.Slides.Count)
                {
                    var slide = presentation.Slides[slideIndex];
                    var headerFooterManager = slide.HeaderFooterManager;

                    // 应用页脚设置 - 功能开关：控制普通幻灯片页脚的显示和内容
                    if (settings.Footer.IsEnabled)
                    {
                        headerFooterManager.SetFooterVisibility(settings.Footer.IsVisible);
                        if (settings.Footer.IsVisible)
                        {
                            headerFooterManager.SetFooterText(GetSafeString(settings.Footer.Text));
                        }
                    }



                    // 应用页码设置 - 功能开关：控制普通幻灯片页码的显示和格式
                    if (settings.SlideNumber.IsEnabled)
                    {
                        headerFooterManager.SetSlideNumberVisibility(settings.SlideNumber.IsVisible);

                        // 如果页码可见，应用页码格式（注意：Aspose.Slides的页码格式通过占位符文本设置）
                        if (settings.SlideNumber.IsVisible && !string.IsNullOrEmpty(settings.SlideNumber.Format))
                        {
                            // 页码格式化处理：将格式字符串中的#替换为实际页码
                            // 注意：实际页码由Aspose.Slides自动处理，这里主要是设置格式模板
                            var formattedText = FormatSlideNumber(settings.SlideNumber.Format, slideIndex + 1, settings.SlideNumber.StartNumber);
                            // 由于Aspose.Slides会自动处理页码，这里主要用于验证格式
                        }
                    }

                    // 应用日期时间设置 - 功能开关：控制普通幻灯片日期时间的显示和格式
                    if (settings.DateTime.IsEnabled)
                    {
                        headerFooterManager.SetDateTimeVisibility(settings.DateTime.IsVisible);
                        if (settings.DateTime.IsVisible)
                        {
                            string dateTimeText;
                            if (settings.DateTime.AutoUpdate)
                            {
                                // 自动更新：使用当前日期时间并按指定格式格式化
                                var format = GetSafeString(settings.DateTime.Format, "yyyy/MM/dd");
                                dateTimeText = DateTime.Now.ToString(format);
                            }
                            else
                            {
                                // 使用自定义文本
                                dateTimeText = GetSafeString(settings.DateTime.CustomText);
                            }

                            headerFooterManager.SetDateTimeText(dateTimeText);
                        }
                    }
                }
            }
        }

        #endregion

        #region 母版幻灯片设置

        /// <summary>
        /// 应用母版幻灯片页眉页脚设置
        /// 功能开关：控制是否对母版幻灯片应用页眉页脚设置，影响所有基于该母版的幻灯片
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        private void ApplyMasterSlideHeaderFooter(Presentation presentation)
        {
            var settings = _settings.MasterSlideSettings;

            foreach (IMasterSlide masterSlide in presentation.Masters)
            {
                var headerFooterManager = masterSlide.HeaderFooterManager;

                // 应用页脚设置 - 功能开关：控制母版页脚的显示和内容
                if (settings.Footer.IsEnabled)
                {
                    if (settings.ApplyToChildSlides)
                    {
                        // 使用正确的API设置母版及其子幻灯片的页脚
                        headerFooterManager.SetFooterAndChildFootersVisibility(settings.Footer.IsVisible);
                        if (settings.Footer.IsVisible)
                        {
                            headerFooterManager.SetFooterAndChildFootersText(GetSafeString(settings.Footer.Text));
                        }
                    }
                    else
                    {
                        // 仅设置母版本身的页脚
                        headerFooterManager.SetFooterVisibility(settings.Footer.IsVisible);
                        if (settings.Footer.IsVisible)
                        {
                            headerFooterManager.SetFooterText(GetSafeString(settings.Footer.Text));
                        }
                    }
                }



                // 应用页码设置 - 功能开关：控制母版页码的显示
                if (settings.SlideNumber.IsEnabled)
                {
                    if (settings.ApplyToChildSlides)
                    {
                        // 使用正确的API设置母版及其子幻灯片的页码
                        headerFooterManager.SetSlideNumberAndChildSlideNumbersVisibility(settings.SlideNumber.IsVisible);
                    }
                    else
                    {
                        // 仅设置母版本身的页码
                        headerFooterManager.SetSlideNumberVisibility(settings.SlideNumber.IsVisible);
                    }
                }

                // 应用日期时间设置 - 功能开关：控制母版日期时间的显示和格式
                if (settings.DateTime.IsEnabled)
                {
                    if (settings.ApplyToChildSlides)
                    {
                        // 使用正确的API设置母版及其子幻灯片的日期时间
                        headerFooterManager.SetDateTimeAndChildDateTimesVisibility(settings.DateTime.IsVisible);
                        if (settings.DateTime.IsVisible)
                        {
                            string dateTimeText;
                            if (settings.DateTime.AutoUpdate)
                            {
                                // 自动更新：使用当前日期时间并按指定格式格式化
                                var format = GetSafeString(settings.DateTime.Format, "yyyy/MM/dd");
                                dateTimeText = DateTime.Now.ToString(format);
                            }
                            else
                            {
                                // 使用自定义文本
                                dateTimeText = GetSafeString(settings.DateTime.CustomText);
                            }

                            headerFooterManager.SetDateTimeAndChildDateTimesText(dateTimeText);
                        }
                    }
                    else
                    {
                        // 仅设置母版本身的日期时间
                        headerFooterManager.SetDateTimeVisibility(settings.DateTime.IsVisible);
                        if (settings.DateTime.IsVisible)
                        {
                            string dateTimeText;
                            if (settings.DateTime.AutoUpdate)
                            {
                                // 自动更新：使用当前日期时间并按指定格式格式化
                                var format = GetSafeString(settings.DateTime.Format, "yyyy/MM/dd");
                                dateTimeText = DateTime.Now.ToString(format);
                            }
                            else
                            {
                                // 使用自定义文本
                                dateTimeText = GetSafeString(settings.DateTime.CustomText);
                            }

                            headerFooterManager.SetDateTimeText(dateTimeText);
                        }
                    }
                }
            }
        }

        #endregion

        #region 布局幻灯片设置

        /// <summary>
        /// 应用布局幻灯片页眉页脚设置
        /// 功能开关：控制是否对布局幻灯片应用页眉页脚设置，影响使用该布局的所有幻灯片
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        private void ApplyLayoutSlideHeaderFooter(Presentation presentation)
        {
            var settings = _settings.LayoutSlideSettings;

            foreach (IMasterSlide masterSlide in presentation.Masters)
            {
                var layoutIndexes = settings.ApplyToAllLayouts ?
                    Enumerable.Range(0, masterSlide.LayoutSlides.Count).ToList() :
                    settings.SelectedLayoutIndexes;

                foreach (var layoutIndex in layoutIndexes)
                {
                    if (layoutIndex >= 0 && layoutIndex < masterSlide.LayoutSlides.Count)
                    {
                        var layoutSlide = masterSlide.LayoutSlides[layoutIndex];
                        var headerFooterManager = layoutSlide.HeaderFooterManager;

                        // 应用页脚设置 - 功能开关：控制布局页脚的显示和内容
                        if (settings.Footer.IsEnabled)
                        {
                            if (settings.ApplyToChildSlides)
                            {
                                // 使用正确的API设置布局及其子幻灯片的页脚
                                headerFooterManager.SetFooterAndChildFootersVisibility(settings.Footer.IsVisible);
                                if (settings.Footer.IsVisible)
                                {
                                    headerFooterManager.SetFooterAndChildFootersText(GetSafeString(settings.Footer.Text));
                                }
                            }
                            else
                            {
                                // 仅设置布局本身的页脚
                                headerFooterManager.SetFooterVisibility(settings.Footer.IsVisible);
                                if (settings.Footer.IsVisible)
                                {
                                    headerFooterManager.SetFooterText(GetSafeString(settings.Footer.Text));
                                }
                            }
                        }



                        // 应用页码设置 - 功能开关：控制布局页码的显示
                        if (settings.SlideNumber.IsEnabled)
                        {
                            if (settings.ApplyToChildSlides)
                            {
                                // 使用正确的API设置布局及其子幻灯片的页码
                                headerFooterManager.SetSlideNumberAndChildSlideNumbersVisibility(settings.SlideNumber.IsVisible);
                            }
                            else
                            {
                                // 仅设置布局本身的页码
                                headerFooterManager.SetSlideNumberVisibility(settings.SlideNumber.IsVisible);
                            }
                        }

                        // 应用日期时间设置 - 功能开关：控制布局日期时间的显示和格式
                        if (settings.DateTime.IsEnabled)
                        {
                            if (settings.ApplyToChildSlides)
                            {
                                // 使用正确的API设置布局及其子幻灯片的日期时间
                                headerFooterManager.SetDateTimeAndChildDateTimesVisibility(settings.DateTime.IsVisible);
                                if (settings.DateTime.IsVisible)
                                {
                                    string dateTimeText;
                                    if (settings.DateTime.AutoUpdate)
                                    {
                                        // 自动更新：使用当前日期时间并按指定格式格式化
                                        var format = GetSafeString(settings.DateTime.Format, "yyyy/MM/dd");
                                        dateTimeText = DateTime.Now.ToString(format);
                                    }
                                    else
                                    {
                                        // 使用自定义文本
                                        dateTimeText = GetSafeString(settings.DateTime.CustomText);
                                    }

                                    headerFooterManager.SetDateTimeAndChildDateTimesText(dateTimeText);
                                }
                            }
                            else
                            {
                                // 仅设置布局本身的日期时间
                                headerFooterManager.SetDateTimeVisibility(settings.DateTime.IsVisible);
                                if (settings.DateTime.IsVisible)
                                {
                                    string dateTimeText;
                                    if (settings.DateTime.AutoUpdate)
                                    {
                                        // 自动更新：使用当前日期时间并按指定格式格式化
                                        var format = GetSafeString(settings.DateTime.Format, "yyyy/MM/dd");
                                        dateTimeText = DateTime.Now.ToString(format);
                                    }
                                    else
                                    {
                                        // 使用自定义文本
                                        dateTimeText = GetSafeString(settings.DateTime.CustomText);
                                    }

                                    headerFooterManager.SetDateTimeText(dateTimeText);
                                }
                            }
                        }
                    }
                }
            }
        }

        #endregion

        #region 备注幻灯片设置

        /// <summary>
        /// 应用备注幻灯片页眉页脚设置
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        private void ApplyNotesSlideHeaderFooter(Presentation presentation)
        {
            var settings = _settings.NotesSlideSettings;

            // 应用备注母版设置
            if (settings.NotesMaster.IsEnabled)
            {
                ApplyNotesMasterHeaderFooter(presentation, settings.NotesMaster);
            }

            // 应用备注幻灯片设置
            if (settings.NotesSlide.IsEnabled)
            {
                ApplyIndividualNotesSlideHeaderFooter(presentation, settings.NotesSlide);
            }
        }

        /// <summary>
        /// 应用备注母版页眉页脚设置
        /// 功能开关：控制是否对备注母版应用页眉页脚设置，影响所有备注页面
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        /// <param name="settings">备注母版设置</param>
        private void ApplyNotesMasterHeaderFooter(Presentation presentation, NotesMasterHeaderFooterSettings settings)
        {
            try
            {
                // 使用正确的API获取备注母版
                var notesMaster = presentation.MasterNotesSlideManager.MasterNotesSlide;
                if (notesMaster != null)
                {
                    var headerFooterManager = notesMaster.HeaderFooterManager;

                    // 应用页脚设置 - 功能开关：控制备注母版页脚的显示和内容
                    if (settings.Footer.IsEnabled)
                    {
                        if (settings.ApplyToChildSlides)
                        {
                            // 使用正确的API设置备注母版及其子备注页的页脚
                            headerFooterManager.SetFooterAndChildFootersVisibility(settings.Footer.IsVisible);
                            if (settings.Footer.IsVisible)
                            {
                                headerFooterManager.SetFooterAndChildFootersText(GetSafeString(settings.Footer.Text));
                            }
                        }
                        else
                        {
                            // 仅设置备注母版本身的页脚
                            headerFooterManager.SetFooterVisibility(settings.Footer.IsVisible);
                            if (settings.Footer.IsVisible)
                            {
                                headerFooterManager.SetFooterText(GetSafeString(settings.Footer.Text));
                            }
                        }
                    }



                    // 应用页码设置 - 功能开关：控制备注母版页码的显示
                    if (settings.SlideNumber.IsEnabled)
                    {
                        if (settings.ApplyToChildSlides)
                        {
                            // 使用正确的API设置备注母版及其子备注页的页码
                            headerFooterManager.SetSlideNumberAndChildSlideNumbersVisibility(settings.SlideNumber.IsVisible);
                        }
                        else
                        {
                            // 仅设置备注母版本身的页码
                            headerFooterManager.SetSlideNumberVisibility(settings.SlideNumber.IsVisible);
                        }
                    }

                    // 应用日期时间设置 - 功能开关：控制备注母版日期时间的显示和格式
                    if (settings.DateTime.IsEnabled)
                    {
                        if (settings.ApplyToChildSlides)
                        {
                            // 使用正确的API设置备注母版及其子备注页的日期时间
                            headerFooterManager.SetDateTimeAndChildDateTimesVisibility(settings.DateTime.IsVisible);
                            if (settings.DateTime.IsVisible)
                            {
                                string dateTimeText;
                                if (settings.DateTime.AutoUpdate)
                                {
                                    // 自动更新：使用当前日期时间并按指定格式格式化
                                    var format = GetSafeString(settings.DateTime.Format, "yyyy/MM/dd");
                                    dateTimeText = DateTime.Now.ToString(format);
                                }
                                else
                                {
                                    // 使用自定义文本
                                    dateTimeText = GetSafeString(settings.DateTime.CustomText);
                                }

                                headerFooterManager.SetDateTimeAndChildDateTimesText(dateTimeText);
                            }
                        }
                        else
                        {
                            // 仅设置备注母版本身的日期时间
                            headerFooterManager.SetDateTimeVisibility(settings.DateTime.IsVisible);
                            if (settings.DateTime.IsVisible)
                            {
                                string dateTimeText;
                                if (settings.DateTime.AutoUpdate)
                                {
                                    // 自动更新：使用当前日期时间并按指定格式格式化
                                    var format = GetSafeString(settings.DateTime.Format, "yyyy/MM/dd");
                                    dateTimeText = DateTime.Now.ToString(format);
                                }
                                else
                                {
                                    // 使用自定义文本
                                    dateTimeText = GetSafeString(settings.DateTime.CustomText);
                                }

                                headerFooterManager.SetDateTimeText(dateTimeText);
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 如果无法获取备注母版，则跳过处理
                // 某些演示文稿可能没有备注母版
            }
        }

        /// <summary>
        /// 应用个别备注幻灯片页眉页脚设置
        /// 功能开关：控制是否对指定的备注幻灯片应用页眉页脚设置
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        /// <param name="settings">备注幻灯片设置</param>
        private void ApplyIndividualNotesSlideHeaderFooter(Presentation presentation, NotesSlideIndividualSettings settings)
        {
            var slideIndexes = GetSlideIndexesForApplication(presentation, settings.ApplyScope,
                "", settings.SelectedNotesSlideIndexes);

            foreach (var slideIndex in slideIndexes)
            {
                if (slideIndex >= 0 && slideIndex < presentation.Slides.Count)
                {
                    var slide = presentation.Slides[slideIndex];
                    var notesSlide = slide.NotesSlideManager.NotesSlide;

                    if (notesSlide != null)
                    {
                        var headerFooterManager = notesSlide.HeaderFooterManager;

                        // 应用页脚设置 - 功能开关：控制个别备注页脚的显示和内容
                        if (settings.Footer.IsEnabled)
                        {
                            headerFooterManager.SetFooterVisibility(settings.Footer.IsVisible);
                            if (settings.Footer.IsVisible)
                            {
                                headerFooterManager.SetFooterText(GetSafeString(settings.Footer.Text));
                            }
                        }



                        // 应用页码设置 - 功能开关：控制个别备注页码的显示
                        if (settings.SlideNumber.IsEnabled)
                        {
                            headerFooterManager.SetSlideNumberVisibility(settings.SlideNumber.IsVisible);
                        }

                        // 应用日期时间设置 - 功能开关：控制个别备注日期时间的显示和格式
                        if (settings.DateTime.IsEnabled)
                        {
                            headerFooterManager.SetDateTimeVisibility(settings.DateTime.IsVisible);
                            if (settings.DateTime.IsVisible)
                            {
                                string dateTimeText;
                                if (settings.DateTime.AutoUpdate)
                                {
                                    // 自动更新：使用当前日期时间并按指定格式格式化
                                    var format = GetSafeString(settings.DateTime.Format, "yyyy/MM/dd");
                                    dateTimeText = DateTime.Now.ToString(format);
                                }
                                else
                                {
                                    // 使用自定义文本
                                    dateTimeText = GetSafeString(settings.DateTime.CustomText);
                                }

                                headerFooterManager.SetDateTimeText(dateTimeText);
                            }
                        }
                    }
                }
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取应用操作的幻灯片索引列表
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        /// <param name="applyScope">应用范围</param>
        /// <param name="slideRange">幻灯片范围</param>
        /// <param name="selectedIndexes">选中的索引列表</param>
        /// <returns>幻灯片索引列表</returns>
        private List<int> GetSlideIndexesForApplication(Presentation presentation, string applyScope,
            string slideRange, List<int> selectedIndexes)
        {
            var indexes = new List<int>();
            var scope = GetSafeString(applyScope, "AllSlides");

            switch (scope)
            {
                case "AllSlides":
                case "AllNotesSlides":
                    for (int i = 0; i < presentation.Slides.Count; i++)
                    {
                        indexes.Add(i);
                    }
                    break;

                case "SlideRange":
                    var range = GetSafeString(slideRange, "1-");
                    indexes.AddRange(ParseSlideRange(range, presentation.Slides.Count));
                    break;

                case "SelectedSlides":
                case "SelectedNotesSlides":
                    var safeSelectedIndexes = selectedIndexes ?? new List<int>();
                    indexes.AddRange(safeSelectedIndexes);
                    break;
            }

            return indexes;
        }

        /// <summary>
        /// 解析幻灯片范围字符串
        /// </summary>
        /// <param name="rangeText">范围文本，如"1-5"、"1,3,5"、"1-"</param>
        /// <param name="totalSlides">总幻灯片数</param>
        /// <returns>幻灯片索引列表（从0开始）</returns>
        private List<int> ParseSlideRange(string rangeText, int totalSlides)
        {
            var indexes = new List<int>();

            if (string.IsNullOrWhiteSpace(rangeText))
                return indexes;

            try
            {
                var parts = rangeText.Split(',');
                foreach (var part in parts)
                {
                    var trimmedPart = part.Trim();

                    if (trimmedPart.Contains('-'))
                    {
                        var rangeParts = trimmedPart.Split('-');
                        if (rangeParts.Length == 2)
                        {
                            var startText = rangeParts[0].Trim();
                            var endText = rangeParts[1].Trim();

                            var start = string.IsNullOrEmpty(startText) ? 1 : int.Parse(startText);
                            var end = string.IsNullOrEmpty(endText) ? totalSlides : int.Parse(endText);

                            for (int i = start; i <= end && i <= totalSlides; i++)
                            {
                                indexes.Add(i - 1); // 转换为从0开始的索引
                            }
                        }
                    }
                    else
                    {
                        if (int.TryParse(trimmedPart, out var slideNumber))
                        {
                            if (slideNumber >= 1 && slideNumber <= totalSlides)
                            {
                                indexes.Add(slideNumber - 1); // 转换为从0开始的索引
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 解析失败时返回空列表
            }

            return indexes.Distinct().OrderBy(x => x).ToList();
        }

        /// <summary>
        /// 格式化页码文本
        /// </summary>
        /// <param name="format">页码格式字符串，如"幻灯片 #"、"第 # 页"等</param>
        /// <param name="currentSlideNumber">当前幻灯片编号（从1开始）</param>
        /// <param name="startNumber">起始编号</param>
        /// <returns>格式化后的页码文本</returns>
        private string FormatSlideNumber(string format, int currentSlideNumber, int startNumber)
        {
            if (string.IsNullOrEmpty(format))
                return currentSlideNumber.ToString();

            // 计算实际显示的页码（考虑起始编号）
            var displayNumber = currentSlideNumber - 1 + startNumber;

            // 将格式字符串中的#替换为实际页码
            return format.Replace("#", displayNumber.ToString());
        }

        /// <summary>
        /// 安全获取字符串值，避免空引用
        /// </summary>
        /// <param name="value">原始字符串值</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>安全的字符串值</returns>
        private string GetSafeString(string? value, string defaultValue = "")
        {
            return value ?? defaultValue;
        }

        #endregion

        #region 处理结果类

        /// <summary>
        /// 处理结果
        /// </summary>
        public class ProcessResult
        {
            /// <summary>
            /// 文件路径
            /// </summary>
            public string FilePath { get; set; } = "";

            /// <summary>
            /// 是否成功
            /// </summary>
            public bool IsSuccess { get; set; } = false;

            /// <summary>
            /// 消息
            /// </summary>
            public string Message { get; set; } = "";

            /// <summary>
            /// 错误消息
            /// </summary>
            public string ErrorMessage { get; set; } = "";
        }

        #endregion
    }
}
