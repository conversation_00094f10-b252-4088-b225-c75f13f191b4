using System;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 文档属性设置窗体 - 用于配置PPT文档属性的各种设置
    /// 包括删除属性、基本信息属性、统计属性、时间属性和自定义属性的管理
    /// </summary>
    public partial class DocumentPropertiesSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 文档属性设置
        /// </summary>
        private DocumentPropertiesSettings _settings;

        #region 控件字段

        // 标签页控件
        private TabControl? _tabControl;

        // 删除功能标签页控件
        private CheckBox? _chkDeleteAllBuiltIn, _chkDeleteAllCustom, _chkDeleteBasic, _chkDeleteStatistics, _chkDeleteTime;
        private ListBox? _lstCustomPropertiesToDelete;
        private Button? _btnAddDeleteProperty, _btnRemoveDeleteProperty;
        private TextBox? _txtDeletePropertyName;

        // 基本信息标签页控件
        private CheckBox? _chkEnableBasic, _chkSetTitle, _chkSetAuthor, _chkSetSubject, _chkSetKeywords, _chkSetComments, _chkSetCategory, _chkSetCompany, _chkSetManager;
        private TextBox? _txtTitle, _txtAuthor, _txtSubject, _txtKeywords, _txtComments, _txtCategory, _txtCompany, _txtManager;

        // 统计属性标签页控件
        private CheckBox? _chkEnableStatistics, _chkUpdateSlideCount, _chkUpdateHiddenSlideCount, _chkUpdateNotesCount, _chkUpdateParagraphCount, _chkUpdateWordCount, _chkUpdateMultimediaClipCount;
        private Label? _lblStatisticsNote;

        // 时间属性标签页控件
        private CheckBox? _chkEnableTime, _chkSetCreatedTime, _chkSetLastSavedTime, _chkSetLastSavedBy, _chkSetLastPrintedTime, _chkSetTotalEditingTime;
        private DateTimePicker? _dtpCreatedTime, _dtpLastSavedTime, _dtpLastPrintedTime;
        private TextBox? _txtLastSavedBy;
        private NumericUpDown? _nudTotalEditingTimeMinutes;

        // 自定义属性标签页控件
        private CheckBox? _chkEnableCustom;
        private ListBox? _lstCustomProperties;
        private TextBox? _txtCustomPropertyName, _txtCustomPropertyValue;
        private ComboBox? _cmbCustomPropertyType;
        private Button? _btnAddCustomProperty, _btnEditCustomProperty, _btnDeleteCustomProperty;

        // 操作按钮
        private Button? _btnOK, _btnCancel;

        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化文档属性设置窗体
        /// </summary>
        /// <param name="settings">文档属性设置</param>
        public DocumentPropertiesSettingsForm(DocumentPropertiesSettings settings)
        {
            _settings = settings ?? new DocumentPropertiesSettings();
            InitializeComponent();
            InitializeControls();
            LoadSettings();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DocumentPropertiesSettingsForm));
            SuspendLayout();
            // 
            // DocumentPropertiesSettingsForm
            // 
            BackColor = Color.FromArgb(240, 240, 240);
            ClientSize = new Size(1284, 961);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "DocumentPropertiesSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "文档属性设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateTabControl();
            CreateDeletionTab();
            CreateBasicPropertiesTab();
            CreateStatisticsTab();
            CreateTimePropertiesTab();
            CreateCustomPropertiesTab();
            CreateActionButtons();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建标签页控件
        /// </summary>
        private void CreateTabControl()
        {
            _tabControl = new TabControl
            {
                Location = new Point(20, 20),  // 增加边距
                Size = new Size(1250, 820),  // 调整标签页控件大小，为底部按钮留出足够空间
                Font = new Font("Microsoft YaHei UI", 9F),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            // 设置标签页宽度均匀分布
            _tabControl.SizeMode = TabSizeMode.Fixed;
            _tabControl.ItemSize = new Size(248, 40);  // 进一步增加标签页大小，确保文字完整显示并均匀分布

            this.Controls.Add(_tabControl);
        }

        /// <summary>
        /// 创建删除功能标签页
        /// </summary>
        private void CreateDeletionTab()
        {
            var tabPage = new TabPage("删除属性")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(20),  // 增加内边距
                AutoScroll = true  // 添加滚动条支持
            };

            int yPos = 30;  // 增加起始位置
            const int rowHeight = 55;  // 进一步增加行高
            const int leftMargin = 30;  // 增加左边距
            const int groupBoxWidth = 1180;  // 进一步增加组件宽度，充分利用空间

            // 删除所有内置属性区域
            var grpDeleteBuiltIn = new GroupBox
            {
                Text = "删除内置属性",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 30),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkDeleteAllBuiltIn = new CheckBox
            {
                Text = "删除所有内置属性（标题、作者、主题、关键词、描述等）",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpDeleteBuiltIn.Controls.Add(_chkDeleteAllBuiltIn);
            tabPage.Controls.Add(grpDeleteBuiltIn);
            yPos += grpDeleteBuiltIn.Height + 25;  // 增加间距

            // 删除所有自定义属性区域
            var grpDeleteCustom = new GroupBox
            {
                Text = "删除自定义属性",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 30),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkDeleteAllCustom = new CheckBox
            {
                Text = "删除所有自定义属性",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpDeleteCustom.Controls.Add(_chkDeleteAllCustom);
            tabPage.Controls.Add(grpDeleteCustom);
            yPos += grpDeleteCustom.Height + 25;  // 增加间距

            // 分类删除区域
            var grpDeleteCategories = new GroupBox
            {
                Text = "分类删除属性",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight * 3 + 60),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkDeleteBasic = new CheckBox
            {
                Text = "删除基本信息属性（标题、作者、主题、关键词、描述、类别、公司、管理者等）",
                Location = new Point(25, 35),  // 调整位置和间距，增加内边距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpDeleteCategories.Controls.Add(_chkDeleteBasic);

            _chkDeleteStatistics = new CheckBox
            {
                Text = "删除统计属性（幻灯片数量、隐藏幻灯片数、备注页数、段落数量、字数统计、多媒体剪辑数等）",
                Location = new Point(25, 80),  // 增加间距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpDeleteCategories.Controls.Add(_chkDeleteStatistics);

            _chkDeleteTime = new CheckBox
            {
                Text = "删除时间属性（创建时间、最后保存时间、最后保存者、最后打印时间、总编辑时间等）",
                Location = new Point(25, 125),  // 增加间距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpDeleteCategories.Controls.Add(_chkDeleteTime);

            tabPage.Controls.Add(grpDeleteCategories);
            yPos += grpDeleteCategories.Height + 25;  // 增加间距

            // 指定删除自定义属性区域
            var grpDeleteSpecific = new GroupBox
            {
                Text = "指定删除的自定义属性",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, 260),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            var lblDeletePropertyName = new Label
            {
                Text = "属性名称：",
                Location = new Point(25, 35),  // 调整位置，增加内边距
                Size = new Size(120, 35),  // 增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            grpDeleteSpecific.Controls.Add(lblDeletePropertyName);

            _txtDeletePropertyName = new TextBox
            {
                Location = new Point(155, 35),  // 调整位置
                Size = new Size(300, 30),  // 增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpDeleteSpecific.Controls.Add(_txtDeletePropertyName);

            _btnAddDeleteProperty = new Button
            {
                Text = "添加",
                Location = new Point(475, 33),  // 调整位置
                Size = new Size(90, 35),  // 增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true
            };
            grpDeleteSpecific.Controls.Add(_btnAddDeleteProperty);

            _btnRemoveDeleteProperty = new Button
            {
                Text = "移除",
                Location = new Point(580, 33),  // 调整位置
                Size = new Size(90, 35),  // 增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true
            };
            grpDeleteSpecific.Controls.Add(_btnRemoveDeleteProperty);

            _lstCustomPropertiesToDelete = new ListBox
            {
                Location = new Point(25, 85),  // 调整位置，增加内边距
                Size = new Size(1130, 160),  // 进一步增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                SelectionMode = SelectionMode.One
            };
            grpDeleteSpecific.Controls.Add(_lstCustomPropertiesToDelete);

            tabPage.Controls.Add(grpDeleteSpecific);

            _tabControl?.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建基本信息属性标签页
        /// </summary>
        private void CreateBasicPropertiesTab()
        {
            var tabPage = new TabPage("基本信息")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(20),  // 增加内边距
                AutoScroll = true  // 添加滚动条支持
            };

            int yPos = 30;  // 增加起始位置
            const int rowHeight = 60;  // 进一步增加行高
            const int leftMargin = 30;  // 增加左边距
            const int groupBoxWidth = 1180;  // 进一步增加组件宽度，充分利用空间

            // 启用基本信息属性设置区域
            var grpEnable = new GroupBox
            {
                Text = "启用设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 30),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkEnableBasic = new CheckBox
            {
                Text = "启用基本信息属性设置",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpEnable.Controls.Add(_chkEnableBasic);
            tabPage.Controls.Add(grpEnable);
            yPos += grpEnable.Height + 25;  // 增加间距

            // 标题设置区域
            var grpTitle = new GroupBox
            {
                Text = "标题设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 45),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetTitle = new CheckBox
            {
                Text = "设置标题",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(180, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpTitle.Controls.Add(_chkSetTitle);

            _txtTitle = new TextBox
            {
                Location = new Point(25, 70),  // 调整位置，增加内边距
                Size = new Size(1130, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入演示文稿标题",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpTitle.Controls.Add(_txtTitle);

            tabPage.Controls.Add(grpTitle);
            yPos += grpTitle.Height + 25;  // 增加间距

            // 作者设置区域
            var grpAuthor = new GroupBox
            {
                Text = "作者设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 45),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetAuthor = new CheckBox
            {
                Text = "设置作者",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(180, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpAuthor.Controls.Add(_chkSetAuthor);

            _txtAuthor = new TextBox
            {
                Location = new Point(25, 70),  // 调整位置，增加内边距
                Size = new Size(1130, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入作者姓名",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpAuthor.Controls.Add(_txtAuthor);

            tabPage.Controls.Add(grpAuthor);
            yPos += grpAuthor.Height + 25;  // 增加间距

            // 主题设置区域
            var grpSubject = new GroupBox
            {
                Text = "主题设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 45),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetSubject = new CheckBox
            {
                Text = "设置主题",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(180, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpSubject.Controls.Add(_chkSetSubject);

            _txtSubject = new TextBox
            {
                Location = new Point(25, 70),  // 调整位置，增加内边距
                Size = new Size(1130, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入演示文稿主题",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpSubject.Controls.Add(_txtSubject);

            tabPage.Controls.Add(grpSubject);
            yPos += grpSubject.Height + 25;  // 增加间距

            // 关键词设置区域
            var grpKeywords = new GroupBox
            {
                Text = "关键词设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 45),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetKeywords = new CheckBox
            {
                Text = "设置关键词",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(180, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpKeywords.Controls.Add(_chkSetKeywords);

            _txtKeywords = new TextBox
            {
                Location = new Point(25, 70),  // 调整位置，增加内边距
                Size = new Size(1130, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入关键词，多个关键词用分号分隔",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpKeywords.Controls.Add(_txtKeywords);

            tabPage.Controls.Add(grpKeywords);
            yPos += grpKeywords.Height + 25;  // 增加间距

            // 描述/注释设置区域
            var grpComments = new GroupBox
            {
                Text = "描述/注释设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 45),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetComments = new CheckBox
            {
                Text = "设置描述/注释",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(200, 35),  // 进一步增加宽度和高度以容纳完整文字
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpComments.Controls.Add(_chkSetComments);

            _txtComments = new TextBox
            {
                Location = new Point(25, 70),  // 调整位置，增加内边距
                Size = new Size(1130, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入演示文稿描述或注释",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpComments.Controls.Add(_txtComments);

            tabPage.Controls.Add(grpComments);
            yPos += grpComments.Height + 25;  // 增加间距

            // 类别设置区域
            var grpCategory = new GroupBox
            {
                Text = "类别设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 45),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetCategory = new CheckBox
            {
                Text = "设置类别",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(180, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpCategory.Controls.Add(_chkSetCategory);

            _txtCategory = new TextBox
            {
                Location = new Point(25, 70),  // 调整位置，增加内边距
                Size = new Size(1130, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入演示文稿类别",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpCategory.Controls.Add(_txtCategory);

            tabPage.Controls.Add(grpCategory);
            yPos += grpCategory.Height + 25;  // 增加间距

            // 公司设置区域
            var grpCompany = new GroupBox
            {
                Text = "公司设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 45),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetCompany = new CheckBox
            {
                Text = "设置公司",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(180, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpCompany.Controls.Add(_chkSetCompany);

            _txtCompany = new TextBox
            {
                Location = new Point(25, 70),  // 调整位置，增加内边距
                Size = new Size(1130, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入公司名称",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpCompany.Controls.Add(_txtCompany);

            tabPage.Controls.Add(grpCompany);
            yPos += grpCompany.Height + 25;  // 增加间距

            // 管理者设置区域
            var grpManager = new GroupBox
            {
                Text = "管理者设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 45),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetManager = new CheckBox
            {
                Text = "设置管理者",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(180, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpManager.Controls.Add(_chkSetManager);

            _txtManager = new TextBox
            {
                Location = new Point(25, 70),  // 调整位置，增加内边距
                Size = new Size(1130, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入管理者姓名",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpManager.Controls.Add(_txtManager);

            tabPage.Controls.Add(grpManager);

            _tabControl?.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建统计属性标签页
        /// </summary>
        private void CreateStatisticsTab()
        {
            var tabPage = new TabPage("统计属性")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(20),  // 增加内边距
                AutoScroll = true  // 添加滚动条支持
            };

            int yPos = 30;  // 增加起始位置
            const int rowHeight = 55;  // 进一步增加行高
            const int leftMargin = 30;  // 增加左边距
            const int groupBoxWidth = 1180;  // 进一步增加组件宽度，充分利用空间

            // 启用统计属性设置区域
            var grpEnable = new GroupBox
            {
                Text = "启用设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 30),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkEnableStatistics = new CheckBox
            {
                Text = "启用统计属性设置",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpEnable.Controls.Add(_chkEnableStatistics);
            tabPage.Controls.Add(grpEnable);
            yPos += grpEnable.Height + 25;  // 增加间距

            // 统计项目设置区域
            var grpStatistics = new GroupBox
            {
                Text = "统计项目设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight * 6 + 80),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkUpdateSlideCount = new CheckBox
            {
                Text = "更新幻灯片数量统计",
                Location = new Point(25, 35),  // 调整位置和间距，增加内边距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpStatistics.Controls.Add(_chkUpdateSlideCount);

            _chkUpdateHiddenSlideCount = new CheckBox
            {
                Text = "更新隐藏幻灯片数统计",
                Location = new Point(25, 80),  // 增加间距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpStatistics.Controls.Add(_chkUpdateHiddenSlideCount);

            _chkUpdateNotesCount = new CheckBox
            {
                Text = "更新备注页数统计",
                Location = new Point(25, 125),  // 增加间距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpStatistics.Controls.Add(_chkUpdateNotesCount);

            _chkUpdateParagraphCount = new CheckBox
            {
                Text = "更新段落数量统计",
                Location = new Point(25, 170),  // 增加间距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpStatistics.Controls.Add(_chkUpdateParagraphCount);

            _chkUpdateWordCount = new CheckBox
            {
                Text = "更新字数统计",
                Location = new Point(25, 215),  // 增加间距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpStatistics.Controls.Add(_chkUpdateWordCount);

            _chkUpdateMultimediaClipCount = new CheckBox
            {
                Text = "更新多媒体剪辑数统计（音频、视频等）",
                Location = new Point(25, 260),  // 增加间距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpStatistics.Controls.Add(_chkUpdateMultimediaClipCount);

            tabPage.Controls.Add(grpStatistics);
            yPos += grpStatistics.Height + 25;  // 增加间距

            // 说明信息区域
            var grpNote = new GroupBox
            {
                Text = "说明",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 50),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _lblStatisticsNote = new Label
            {
                Text = "注意：统计属性将在处理PPT文件时自动计算并更新。\n启用相应选项后，系统会重新统计对应的数据并写入文档属性中。\n这些统计信息有助于了解演示文稿的基本结构和内容概况。",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(1130, 70),  // 进一步增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                ForeColor = Color.FromArgb(100, 100, 100),
                TextAlign = ContentAlignment.TopLeft  // 多行文本左对齐
            };
            grpNote.Controls.Add(_lblStatisticsNote);

            tabPage.Controls.Add(grpNote);

            _tabControl?.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建时间属性标签页
        /// </summary>
        private void CreateTimePropertiesTab()
        {
            var tabPage = new TabPage("时间属性")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(20),  // 增加内边距
                AutoScroll = true  // 添加滚动条支持
            };

            int yPos = 30;  // 增加起始位置
            const int rowHeight = 60;  // 进一步增加行高
            const int leftMargin = 30;  // 增加左边距
            const int groupBoxWidth = 1180;  // 进一步增加组件宽度，充分利用空间

            // 启用时间属性设置区域
            var grpEnable = new GroupBox
            {
                Text = "启用设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 30),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkEnableTime = new CheckBox
            {
                Text = "启用时间属性设置",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpEnable.Controls.Add(_chkEnableTime);
            tabPage.Controls.Add(grpEnable);
            yPos += grpEnable.Height + 25;  // 增加间距

            // 创建时间设置区域
            var grpCreatedTime = new GroupBox
            {
                Text = "创建时间设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetCreatedTime = new CheckBox
            {
                Text = "设置创建时间",
                Location = new Point(20, 25),
                Size = new Size(180, 30),  // 增加宽度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpCreatedTime.Controls.Add(_chkSetCreatedTime);

            _dtpCreatedTime = new DateTimePicker
            {
                Location = new Point(20, 60),
                Size = new Size(960, 25),  // 增加宽度
                Font = new Font("Microsoft YaHei UI", 9F),
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy-MM-dd HH:mm:ss"
            };
            grpCreatedTime.Controls.Add(_dtpCreatedTime);

            tabPage.Controls.Add(grpCreatedTime);
            yPos += grpCreatedTime.Height + 20;

            // 最后保存时间设置区域
            var grpLastSavedTime = new GroupBox
            {
                Text = "最后保存时间设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetLastSavedTime = new CheckBox
            {
                Text = "设置最后保存时间",
                Location = new Point(20, 25),
                Size = new Size(200, 30),  // 增加宽度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpLastSavedTime.Controls.Add(_chkSetLastSavedTime);

            _dtpLastSavedTime = new DateTimePicker
            {
                Location = new Point(20, 60),
                Size = new Size(960, 25),
                Font = new Font("Microsoft YaHei UI", 9F),
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy-MM-dd HH:mm:ss"
            };
            grpLastSavedTime.Controls.Add(_dtpLastSavedTime);

            tabPage.Controls.Add(grpLastSavedTime);
            yPos += grpLastSavedTime.Height + 20;

            // 最后保存者设置区域
            var grpLastSavedBy = new GroupBox
            {
                Text = "最后保存者设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetLastSavedBy = new CheckBox
            {
                Text = "设置最后保存者",
                Location = new Point(20, 25),
                Size = new Size(180, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpLastSavedBy.Controls.Add(_chkSetLastSavedBy);

            _txtLastSavedBy = new TextBox
            {
                Location = new Point(20, 60),
                Size = new Size(960, 25),
                Font = new Font("Microsoft YaHei UI", 9F),
                PlaceholderText = "请输入最后保存者姓名",
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpLastSavedBy.Controls.Add(_txtLastSavedBy);

            tabPage.Controls.Add(grpLastSavedBy);
            yPos += grpLastSavedBy.Height + 20;

            // 最后打印时间设置区域
            var grpLastPrintedTime = new GroupBox
            {
                Text = "最后打印时间设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetLastPrintedTime = new CheckBox
            {
                Text = "设置最后打印时间",
                Location = new Point(20, 25),
                Size = new Size(200, 30),  // 增加宽度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpLastPrintedTime.Controls.Add(_chkSetLastPrintedTime);

            _dtpLastPrintedTime = new DateTimePicker
            {
                Location = new Point(20, 60),
                Size = new Size(960, 25),
                Font = new Font("Microsoft YaHei UI", 9F),
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy-MM-dd HH:mm:ss"
            };
            grpLastPrintedTime.Controls.Add(_dtpLastPrintedTime);

            tabPage.Controls.Add(grpLastPrintedTime);
            yPos += grpLastPrintedTime.Height + 20;

            // 总编辑时间设置区域
            var grpTotalEditingTime = new GroupBox
            {
                Text = "总编辑时间设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkSetTotalEditingTime = new CheckBox
            {
                Text = "设置总编辑时间",
                Location = new Point(20, 25),
                Size = new Size(180, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpTotalEditingTime.Controls.Add(_chkSetTotalEditingTime);

            var lblMinutes = new Label
            {
                Text = "总编辑时间（分钟）：",
                Location = new Point(20, 62),  // 调整位置
                Size = new Size(180, 30),  // 增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            grpTotalEditingTime.Controls.Add(lblMinutes);

            _nudTotalEditingTimeMinutes = new NumericUpDown
            {
                Location = new Point(210, 60),  // 调整位置
                Size = new Size(150, 25),  // 增加宽度
                Font = new Font("Microsoft YaHei UI", 9F),
                Minimum = 0,
                Maximum = 999999,
                TextAlign = HorizontalAlignment.Center
            };
            grpTotalEditingTime.Controls.Add(_nudTotalEditingTimeMinutes);

            tabPage.Controls.Add(grpTotalEditingTime);

            _tabControl?.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建自定义属性标签页
        /// </summary>
        private void CreateCustomPropertiesTab()
        {
            var tabPage = new TabPage("自定义属性")
            {
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(20),  // 增加内边距
                AutoScroll = true  // 添加滚动条支持
            };

            int yPos = 30;  // 增加起始位置
            const int rowHeight = 55;  // 进一步增加行高
            const int leftMargin = 30;  // 增加左边距
            const int groupBoxWidth = 1180;  // 进一步增加组件宽度，充分利用空间

            // 启用自定义属性设置区域
            var grpEnable = new GroupBox
            {
                Text = "启用设置",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight + 30),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _chkEnableCustom = new CheckBox
            {
                Text = "启用自定义属性设置",
                Location = new Point(25, 30),  // 调整位置，增加内边距
                Size = new Size(1130, 35),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            grpEnable.Controls.Add(_chkEnableCustom);
            tabPage.Controls.Add(grpEnable);
            yPos += grpEnable.Height + 25;  // 增加间距

            // 自定义属性列表区域
            var grpCustomList = new GroupBox
            {
                Text = "自定义属性列表",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, 280),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            _lstCustomProperties = new ListBox
            {
                Location = new Point(25, 35),  // 调整位置，增加内边距
                Size = new Size(1130, 230),  // 进一步增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                SelectionMode = SelectionMode.One
            };
            grpCustomList.Controls.Add(_lstCustomProperties);

            tabPage.Controls.Add(grpCustomList);
            yPos += grpCustomList.Height + 25;  // 增加间距

            // 自定义属性编辑区域
            var grpCustomEdit = new GroupBox
            {
                Text = "自定义属性编辑",
                Location = new Point(leftMargin, yPos),
                Size = new Size(groupBoxWidth, rowHeight * 4 + 70),  // 进一步增加高度
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold)
            };

            var lblPropertyName = new Label
            {
                Text = "属性名称：",
                Location = new Point(25, 35),  // 调整位置，增加内边距
                Size = new Size(120, 35),  // 增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            grpCustomEdit.Controls.Add(lblPropertyName);

            _txtCustomPropertyName = new TextBox
            {
                Location = new Point(155, 35),  // 调整位置
                Size = new Size(300, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpCustomEdit.Controls.Add(_txtCustomPropertyName);

            var lblPropertyType = new Label
            {
                Text = "属性类型：",
                Location = new Point(480, 35),  // 调整位置
                Size = new Size(120, 35),  // 增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            grpCustomEdit.Controls.Add(lblPropertyType);

            _cmbCustomPropertyType = new ComboBox
            {
                Location = new Point(610, 35),  // 调整位置
                Size = new Size(180, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                DrawMode = DrawMode.OwnerDrawFixed  // 启用自定义绘制以实现文本居中
            };
            _cmbCustomPropertyType.Items.AddRange(new[] { "Text", "Number", "Date", "Boolean" });
            _cmbCustomPropertyType.SelectedIndex = 0;
            // 添加自定义绘制事件以实现下拉框文本居中
            _cmbCustomPropertyType.DrawItem += (sender, e) =>
            {
                if (e.Index >= 0)
                {
                    e.DrawBackground();
                    using (var brush = new SolidBrush(e.ForeColor))
                    {
                        var text = _cmbCustomPropertyType.Items[e.Index].ToString();
                        var font = e.Font ?? _cmbCustomPropertyType.Font ?? SystemFonts.DefaultFont;
                        var textSize = e.Graphics.MeasureString(text, font);
                        var x = e.Bounds.X + (e.Bounds.Width - textSize.Width) / 2;
                        var y = e.Bounds.Y + (e.Bounds.Height - textSize.Height) / 2;
                        e.Graphics.DrawString(text, font, brush, x, y);
                    }
                    e.DrawFocusRectangle();
                }
            };
            grpCustomEdit.Controls.Add(_cmbCustomPropertyType);

            var lblPropertyValue = new Label
            {
                Text = "属性值：",
                Location = new Point(25, 80),  // 调整位置，增加内边距
                Size = new Size(120, 35),  // 增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = ContentAlignment.MiddleLeft
            };
            grpCustomEdit.Controls.Add(lblPropertyValue);

            _txtCustomPropertyValue = new TextBox
            {
                Location = new Point(155, 80),  // 调整位置
                Size = new Size(635, 30),  // 进一步增加宽度和高度
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = HorizontalAlignment.Center  // 文本居中对齐
            };
            grpCustomEdit.Controls.Add(_txtCustomPropertyValue);

            _btnAddCustomProperty = new Button
            {
                Text = "添加",
                Location = new Point(25, 130),  // 调整位置，增加内边距
                Size = new Size(120, 40),  // 进一步增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true
            };
            grpCustomEdit.Controls.Add(_btnAddCustomProperty);

            _btnEditCustomProperty = new Button
            {
                Text = "编辑",
                Location = new Point(165, 130),  // 调整位置
                Size = new Size(120, 40),  // 进一步增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true
            };
            grpCustomEdit.Controls.Add(_btnEditCustomProperty);

            _btnDeleteCustomProperty = new Button
            {
                Text = "删除",
                Location = new Point(305, 130),  // 调整位置
                Size = new Size(120, 40),  // 进一步增加大小
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true
            };
            grpCustomEdit.Controls.Add(_btnDeleteCustomProperty);

            tabPage.Controls.Add(grpCustomEdit);

            _tabControl?.TabPages.Add(tabPage);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            const int buttonWidth = 120;  // 按钮宽度
            const int buttonHeight = 40;  // 按钮高度
            const int buttonSpacing = 20;  // 按钮间距
            const int buttonMargin = 20;  // 按钮与TabControl的间距

            // 计算按钮总宽度 - 只有确定和取消按钮，靠右放置
            int totalButtonWidth = buttonWidth * 2 + buttonSpacing;
            int startX = 1300 - totalButtonWidth - 40;  // 靠右放置，留40像素右边距

            // 将按钮位置设置在TabControl下方
            int yPos = (_tabControl?.Bottom ?? 870) + buttonMargin; // 基于TabControl的底部位置计算，避免空引用

            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(startX, yPos),
                Size = new Size(buttonWidth, buttonHeight),
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.OK,
                TabIndex = 1000  // 设置较高的TabIndex确保按钮在最前面
            };
            Controls.Add(_btnOK);

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(startX + buttonWidth + buttonSpacing, yPos),
                Size = new Size(buttonWidth, buttonHeight),
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.Cancel,
                TabIndex = 1001
            };
            Controls.Add(_btnCancel);


        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 删除属性按钮事件
            if (_btnAddDeleteProperty != null)
                _btnAddDeleteProperty.Click += BtnAddDeleteProperty_Click;
            if (_btnRemoveDeleteProperty != null)
                _btnRemoveDeleteProperty.Click += BtnRemoveDeleteProperty_Click;

            // 自定义属性按钮事件
            if (_btnAddCustomProperty != null)
                _btnAddCustomProperty.Click += BtnAddCustomProperty_Click;
            if (_btnEditCustomProperty != null)
                _btnEditCustomProperty.Click += BtnEditCustomProperty_Click;
            if (_btnDeleteCustomProperty != null)
                _btnDeleteCustomProperty.Click += BtnDeleteCustomProperty_Click;

            // 自定义属性列表选择事件
            if (_lstCustomProperties != null)
                _lstCustomProperties.SelectedIndexChanged += LstCustomProperties_SelectedIndexChanged;

            // 操作按钮事件
            if (_btnOK != null)
                _btnOK.Click += BtnOK_Click;
            if (_btnCancel != null)
                _btnCancel.Click += BtnCancel_Click;

            // 窗体关闭事件已在 OnFormClosing 方法中处理


            // 启用复选框事件
            if (_chkEnableBasic != null)
                _chkEnableBasic.CheckedChanged += ChkEnableBasic_CheckedChanged;
            if (_chkEnableStatistics != null)
                _chkEnableStatistics.CheckedChanged += ChkEnableStatistics_CheckedChanged;
            if (_chkEnableTime != null)
                _chkEnableTime.CheckedChanged += ChkEnableTime_CheckedChanged;
            if (_chkEnableCustom != null)
                _chkEnableCustom.CheckedChanged += ChkEnableCustom_CheckedChanged;

            // 基本信息子复选框事件 - 控制对应输入框的启用状态
            if (_chkSetTitle != null)
                _chkSetTitle.CheckedChanged += (s, e) => UpdateBasicPropertiesControlStates();
            if (_chkSetAuthor != null)
                _chkSetAuthor.CheckedChanged += (s, e) => UpdateBasicPropertiesControlStates();
            if (_chkSetSubject != null)
                _chkSetSubject.CheckedChanged += (s, e) => UpdateBasicPropertiesControlStates();
            if (_chkSetKeywords != null)
                _chkSetKeywords.CheckedChanged += (s, e) => UpdateBasicPropertiesControlStates();
            if (_chkSetComments != null)
                _chkSetComments.CheckedChanged += (s, e) => UpdateBasicPropertiesControlStates();
            if (_chkSetCategory != null)
                _chkSetCategory.CheckedChanged += (s, e) => UpdateBasicPropertiesControlStates();
            if (_chkSetCompany != null)
                _chkSetCompany.CheckedChanged += (s, e) => UpdateBasicPropertiesControlStates();
            if (_chkSetManager != null)
                _chkSetManager.CheckedChanged += (s, e) => UpdateBasicPropertiesControlStates();

            // 时间属性子复选框事件 - 控制对应输入框的启用状态
            if (_chkSetCreatedTime != null)
                _chkSetCreatedTime.CheckedChanged += (s, e) => UpdateTimePropertiesControlStates();
            if (_chkSetLastSavedTime != null)
                _chkSetLastSavedTime.CheckedChanged += (s, e) => UpdateTimePropertiesControlStates();
            if (_chkSetLastSavedBy != null)
                _chkSetLastSavedBy.CheckedChanged += (s, e) => UpdateTimePropertiesControlStates();
            if (_chkSetLastPrintedTime != null)
                _chkSetLastPrintedTime.CheckedChanged += (s, e) => UpdateTimePropertiesControlStates();
            if (_chkSetTotalEditingTime != null)
                _chkSetTotalEditingTime.CheckedChanged += (s, e) => UpdateTimePropertiesControlStates();
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // 加载删除设置
                LoadDeletionSettings();

                // 加载基本信息设置
                LoadBasicPropertiesSettings();

                // 加载统计属性设置
                LoadStatisticsSettings();

                // 加载时间属性设置
                LoadTimePropertiesSettings();

                // 加载自定义属性设置
                LoadCustomPropertiesSettings();

                // 更新控件状态
                UpdateControlStates();

                // 加载完成
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载删除设置
        /// </summary>
        private void LoadDeletionSettings()
        {
            var deletion = _settings.DeletionSettings;
            if (_chkDeleteAllBuiltIn != null)
                _chkDeleteAllBuiltIn.Checked = deletion.DeleteAllBuiltInProperties;
            if (_chkDeleteAllCustom != null)
                _chkDeleteAllCustom.Checked = deletion.DeleteAllCustomProperties;
            if (_chkDeleteBasic != null)
                _chkDeleteBasic.Checked = deletion.DeleteBasicProperties;
            if (_chkDeleteStatistics != null)
                _chkDeleteStatistics.Checked = deletion.DeleteStatisticsProperties;
            if (_chkDeleteTime != null)
                _chkDeleteTime.Checked = deletion.DeleteTimeProperties;

            // 加载要删除的自定义属性列表
            if (_lstCustomPropertiesToDelete != null)
            {
                _lstCustomPropertiesToDelete.Items.Clear();
                foreach (var propertyName in deletion.CustomPropertiesToDelete)
                {
                    _lstCustomPropertiesToDelete.Items.Add(propertyName);
                }
            }
        }

        /// <summary>
        /// 加载基本信息设置
        /// </summary>
        private void LoadBasicPropertiesSettings()
        {
            var basic = _settings.BasicProperties;
            if (_chkEnableBasic != null)
                _chkEnableBasic.Checked = basic.IsEnabled;

            if (_chkSetTitle != null)
                _chkSetTitle.Checked = basic.SetTitle;
            if (_txtTitle != null)
                _txtTitle.Text = basic.Title;

            if (_chkSetAuthor != null)
                _chkSetAuthor.Checked = basic.SetAuthor;
            if (_txtAuthor != null)
                _txtAuthor.Text = basic.Author;

            if (_chkSetSubject != null)
                _chkSetSubject.Checked = basic.SetSubject;
            if (_txtSubject != null)
                _txtSubject.Text = basic.Subject;

            if (_chkSetKeywords != null)
                _chkSetKeywords.Checked = basic.SetKeywords;
            if (_txtKeywords != null)
                _txtKeywords.Text = basic.Keywords;

            if (_chkSetComments != null)
                _chkSetComments.Checked = basic.SetComments;
            if (_txtComments != null)
                _txtComments.Text = basic.Comments;

            if (_chkSetCategory != null)
                _chkSetCategory.Checked = basic.SetCategory;
            if (_txtCategory != null)
                _txtCategory.Text = basic.Category;

            if (_chkSetCompany != null)
                _chkSetCompany.Checked = basic.SetCompany;
            if (_txtCompany != null)
                _txtCompany.Text = basic.Company;

            if (_chkSetManager != null)
                _chkSetManager.Checked = basic.SetManager;
            if (_txtManager != null)
                _txtManager.Text = basic.Manager;
        }

        /// <summary>
        /// 加载统计属性设置
        /// </summary>
        private void LoadStatisticsSettings()
        {
            var statistics = _settings.StatisticsProperties;
            if (_chkEnableStatistics != null)
                _chkEnableStatistics.Checked = statistics.IsEnabled;

            if (_chkUpdateSlideCount != null)
                _chkUpdateSlideCount.Checked = statistics.UpdateSlideCount;
            if (_chkUpdateHiddenSlideCount != null)
                _chkUpdateHiddenSlideCount.Checked = statistics.UpdateHiddenSlideCount;
            if (_chkUpdateNotesCount != null)
                _chkUpdateNotesCount.Checked = statistics.UpdateNotesCount;
            if (_chkUpdateParagraphCount != null)
                _chkUpdateParagraphCount.Checked = statistics.UpdateParagraphCount;
            if (_chkUpdateWordCount != null)
                _chkUpdateWordCount.Checked = statistics.UpdateWordCount;
            if (_chkUpdateMultimediaClipCount != null)
                _chkUpdateMultimediaClipCount.Checked = statistics.UpdateMultimediaClipCount;
        }

        /// <summary>
        /// 加载时间属性设置
        /// </summary>
        private void LoadTimePropertiesSettings()
        {
            var time = _settings.TimeProperties;
            if (_chkEnableTime != null)
                _chkEnableTime.Checked = time.IsEnabled;

            if (_chkSetCreatedTime != null)
                _chkSetCreatedTime.Checked = time.SetCreatedTime;
            if (_dtpCreatedTime != null)
                _dtpCreatedTime.Value = time.CreatedTime;

            if (_chkSetLastSavedTime != null)
                _chkSetLastSavedTime.Checked = time.SetLastSavedTime;
            if (_dtpLastSavedTime != null)
                _dtpLastSavedTime.Value = time.LastSavedTime;

            if (_chkSetLastSavedBy != null)
                _chkSetLastSavedBy.Checked = time.SetLastSavedBy;
            if (_txtLastSavedBy != null)
                _txtLastSavedBy.Text = time.LastSavedBy;

            if (_chkSetLastPrintedTime != null)
                _chkSetLastPrintedTime.Checked = time.SetLastPrintedTime;
            if (_dtpLastPrintedTime != null)
                _dtpLastPrintedTime.Value = time.LastPrintedTime;

            if (_chkSetTotalEditingTime != null)
                _chkSetTotalEditingTime.Checked = time.SetTotalEditingTime;
            if (_nudTotalEditingTimeMinutes != null)
                _nudTotalEditingTimeMinutes.Value = time.TotalEditingTimeMinutes;
        }

        /// <summary>
        /// 加载自定义属性设置
        /// </summary>
        private void LoadCustomPropertiesSettings()
        {
            var custom = _settings.CustomProperties;
            if (_chkEnableCustom != null)
                _chkEnableCustom.Checked = custom.IsEnabled;

            // 加载自定义属性列表
            if (_lstCustomProperties != null)
            {
                _lstCustomProperties.Items.Clear();
                foreach (var property in custom.CustomProperties)
                {
                    var displayText = $"{property.Name} ({property.PropertyType}) = {property.Value}";
                    _lstCustomProperties.Items.Add(displayText);
                }
            }
        }

        /// <summary>
        /// 更新控件状态
        /// </summary>
        private void UpdateControlStates()
        {
            // 更新基本信息控件状态
            UpdateBasicPropertiesControlStates();

            // 更新统计属性控件状态
            UpdateStatisticsControlStates();

            // 更新时间属性控件状态
            UpdateTimePropertiesControlStates();

            // 更新自定义属性控件状态
            UpdateCustomPropertiesControlStates();
        }

        /// <summary>
        /// 更新基本信息控件状态
        /// </summary>
        private void UpdateBasicPropertiesControlStates()
        {
            bool isEnabled = _chkEnableBasic?.Checked ?? false;

            if (_chkSetTitle != null) _chkSetTitle.Enabled = isEnabled;
            if (_txtTitle != null) _txtTitle.Enabled = isEnabled && (_chkSetTitle?.Checked ?? false);

            if (_chkSetAuthor != null) _chkSetAuthor.Enabled = isEnabled;
            if (_txtAuthor != null) _txtAuthor.Enabled = isEnabled && (_chkSetAuthor?.Checked ?? false);

            if (_chkSetSubject != null) _chkSetSubject.Enabled = isEnabled;
            if (_txtSubject != null) _txtSubject.Enabled = isEnabled && (_chkSetSubject?.Checked ?? false);

            if (_chkSetKeywords != null) _chkSetKeywords.Enabled = isEnabled;
            if (_txtKeywords != null) _txtKeywords.Enabled = isEnabled && (_chkSetKeywords?.Checked ?? false);

            if (_chkSetComments != null) _chkSetComments.Enabled = isEnabled;
            if (_txtComments != null) _txtComments.Enabled = isEnabled && (_chkSetComments?.Checked ?? false);

            if (_chkSetCategory != null) _chkSetCategory.Enabled = isEnabled;
            if (_txtCategory != null) _txtCategory.Enabled = isEnabled && (_chkSetCategory?.Checked ?? false);

            if (_chkSetCompany != null) _chkSetCompany.Enabled = isEnabled;
            if (_txtCompany != null) _txtCompany.Enabled = isEnabled && (_chkSetCompany?.Checked ?? false);

            if (_chkSetManager != null) _chkSetManager.Enabled = isEnabled;
            if (_txtManager != null) _txtManager.Enabled = isEnabled && (_chkSetManager?.Checked ?? false);
        }

        /// <summary>
        /// 更新统计属性控件状态
        /// </summary>
        private void UpdateStatisticsControlStates()
        {
            bool isEnabled = _chkEnableStatistics?.Checked ?? false;

            if (_chkUpdateSlideCount != null) _chkUpdateSlideCount.Enabled = isEnabled;
            if (_chkUpdateHiddenSlideCount != null) _chkUpdateHiddenSlideCount.Enabled = isEnabled;
            if (_chkUpdateNotesCount != null) _chkUpdateNotesCount.Enabled = isEnabled;
            if (_chkUpdateParagraphCount != null) _chkUpdateParagraphCount.Enabled = isEnabled;
            if (_chkUpdateWordCount != null) _chkUpdateWordCount.Enabled = isEnabled;
            if (_chkUpdateMultimediaClipCount != null) _chkUpdateMultimediaClipCount.Enabled = isEnabled;
        }

        /// <summary>
        /// 更新时间属性控件状态
        /// </summary>
        private void UpdateTimePropertiesControlStates()
        {
            bool isEnabled = _chkEnableTime?.Checked ?? false;

            if (_chkSetCreatedTime != null) _chkSetCreatedTime.Enabled = isEnabled;
            if (_dtpCreatedTime != null) _dtpCreatedTime.Enabled = isEnabled && (_chkSetCreatedTime?.Checked ?? false);

            if (_chkSetLastSavedTime != null) _chkSetLastSavedTime.Enabled = isEnabled;
            if (_dtpLastSavedTime != null) _dtpLastSavedTime.Enabled = isEnabled && (_chkSetLastSavedTime?.Checked ?? false);

            if (_chkSetLastSavedBy != null) _chkSetLastSavedBy.Enabled = isEnabled;
            if (_txtLastSavedBy != null) _txtLastSavedBy.Enabled = isEnabled && (_chkSetLastSavedBy?.Checked ?? false);

            if (_chkSetLastPrintedTime != null) _chkSetLastPrintedTime.Enabled = isEnabled;
            if (_dtpLastPrintedTime != null) _dtpLastPrintedTime.Enabled = isEnabled && (_chkSetLastPrintedTime?.Checked ?? false);

            if (_chkSetTotalEditingTime != null) _chkSetTotalEditingTime.Enabled = isEnabled;
            if (_nudTotalEditingTimeMinutes != null) _nudTotalEditingTimeMinutes.Enabled = isEnabled && (_chkSetTotalEditingTime?.Checked ?? false);
        }

        /// <summary>
        /// 更新自定义属性控件状态
        /// </summary>
        private void UpdateCustomPropertiesControlStates()
        {
            bool isEnabled = _chkEnableCustom?.Checked ?? false;

            if (_lstCustomProperties != null) _lstCustomProperties.Enabled = isEnabled;
            if (_txtCustomPropertyName != null) _txtCustomPropertyName.Enabled = isEnabled;
            if (_txtCustomPropertyValue != null) _txtCustomPropertyValue.Enabled = isEnabled;
            if (_cmbCustomPropertyType != null) _cmbCustomPropertyType.Enabled = isEnabled;
            if (_btnAddCustomProperty != null) _btnAddCustomProperty.Enabled = isEnabled;
            if (_btnEditCustomProperty != null) _btnEditCustomProperty.Enabled = isEnabled;
            if (_btnDeleteCustomProperty != null) _btnDeleteCustomProperty.Enabled = isEnabled;
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 添加删除属性按钮点击事件
        /// </summary>
        private void BtnAddDeleteProperty_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_txtDeletePropertyName == null || _lstCustomPropertiesToDelete == null) return;

                string propertyName = _txtDeletePropertyName.Text.Trim();
                if (string.IsNullOrEmpty(propertyName))
                {
                    MessageBox.Show("请输入要删除的属性名称", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (_lstCustomPropertiesToDelete.Items.Contains(propertyName))
                {
                    MessageBox.Show("该属性名称已存在", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _lstCustomPropertiesToDelete.Items.Add(propertyName);
                _txtDeletePropertyName.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加删除属性时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 移除删除属性按钮点击事件
        /// </summary>
        private void BtnRemoveDeleteProperty_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_lstCustomPropertiesToDelete == null) return;

                if (_lstCustomPropertiesToDelete.SelectedIndex >= 0)
                {
                    _lstCustomPropertiesToDelete.Items.RemoveAt(_lstCustomPropertiesToDelete.SelectedIndex);
                }
                else
                {
                    MessageBox.Show("请选择要移除的属性", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"移除删除属性时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 添加自定义属性按钮点击事件
        /// </summary>
        private void BtnAddCustomProperty_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_txtCustomPropertyName == null || _txtCustomPropertyValue == null ||
                    _cmbCustomPropertyType == null || _lstCustomProperties == null) return;

                string name = _txtCustomPropertyName.Text.Trim();
                string value = _txtCustomPropertyValue.Text.Trim();
                string type = _cmbCustomPropertyType.SelectedItem?.ToString() ?? "Text";

                if (string.IsNullOrEmpty(name))
                {
                    MessageBox.Show("请输入属性名称", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 检查是否已存在同名属性
                foreach (var property in _settings.CustomProperties.CustomProperties)
                {
                    if (property.Name.Equals(name, StringComparison.OrdinalIgnoreCase))
                    {
                        MessageBox.Show("该属性名称已存在", "提示",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                }

                // 添加新属性
                var newProperty = new CustomPropertyInfo
                {
                    Name = name,
                    Value = value,
                    PropertyType = type,
                    IsEnabled = true,
                    CreatedTime = DateTime.Now
                };

                _settings.CustomProperties.CustomProperties.Add(newProperty);

                // 更新列表显示
                var displayText = $"{newProperty.Name} ({newProperty.PropertyType}) = {newProperty.Value}";
                _lstCustomProperties.Items.Add(displayText);

                // 清空输入框
                _txtCustomPropertyName.Clear();
                _txtCustomPropertyValue.Clear();
                _cmbCustomPropertyType.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加自定义属性时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑自定义属性按钮点击事件
        /// </summary>
        private void BtnEditCustomProperty_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_lstCustomProperties == null || _txtCustomPropertyName == null ||
                    _txtCustomPropertyValue == null || _cmbCustomPropertyType == null) return;

                int selectedIndex = _lstCustomProperties.SelectedIndex;
                if (selectedIndex < 0)
                {
                    MessageBox.Show("请选择要编辑的属性", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string name = _txtCustomPropertyName.Text.Trim();
                string value = _txtCustomPropertyValue.Text.Trim();
                string type = _cmbCustomPropertyType.SelectedItem?.ToString() ?? "Text";

                if (string.IsNullOrEmpty(name))
                {
                    MessageBox.Show("请输入属性名称", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 更新属性
                var property = _settings.CustomProperties.CustomProperties[selectedIndex];
                property.Name = name;
                property.Value = value;
                property.PropertyType = type;

                // 更新列表显示
                var displayText = $"{property.Name} ({property.PropertyType}) = {property.Value}";
                _lstCustomProperties.Items[selectedIndex] = displayText;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑自定义属性时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除自定义属性按钮点击事件
        /// </summary>
        private void BtnDeleteCustomProperty_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_lstCustomProperties == null) return;

                int selectedIndex = _lstCustomProperties.SelectedIndex;
                if (selectedIndex < 0)
                {
                    MessageBox.Show("请选择要删除的属性", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 直接删除，不显示确认框（根据项目要求删除多余的确认提示框）
                _settings.CustomProperties.CustomProperties.RemoveAt(selectedIndex);
                _lstCustomProperties.Items.RemoveAt(selectedIndex);

                // 清空输入框
                if (_txtCustomPropertyName != null) _txtCustomPropertyName.Clear();
                if (_txtCustomPropertyValue != null) _txtCustomPropertyValue.Clear();
                if (_cmbCustomPropertyType != null) _cmbCustomPropertyType.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除自定义属性时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 自定义属性列表选择变更事件
        /// </summary>
        private void LstCustomProperties_SelectedIndexChanged(object? sender, EventArgs e)
        {
            try
            {
                if (_lstCustomProperties == null || _txtCustomPropertyName == null ||
                    _txtCustomPropertyValue == null || _cmbCustomPropertyType == null) return;

                int selectedIndex = _lstCustomProperties.SelectedIndex;
                if (selectedIndex >= 0 && selectedIndex < _settings.CustomProperties.CustomProperties.Count)
                {
                    var property = _settings.CustomProperties.CustomProperties[selectedIndex];
                    _txtCustomPropertyName.Text = property.Name;
                    _txtCustomPropertyValue.Text = property.Value;
                    _cmbCustomPropertyType.SelectedItem = property.PropertyType;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择自定义属性时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 确定按钮点击事件 - 保存配置并关闭窗口
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveSettings();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件 - 不保存配置直接关闭窗口
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            // 直接关闭，不保存配置，删除多余的确认提示框
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 窗体关闭事件 - 如果不是取消操作则自动保存配置
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // 如果不是通过取消按钮关闭的，则自动保存配置
            if (this.DialogResult != DialogResult.Cancel)
            {
                try
                {
                    SaveSettings();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            base.OnFormClosing(e);
        }



        /// <summary>
        /// 启用基本信息复选框变更事件
        /// </summary>
        private void ChkEnableBasic_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateBasicPropertiesControlStates();
        }

        /// <summary>
        /// 启用统计属性复选框变更事件
        /// </summary>
        private void ChkEnableStatistics_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateStatisticsControlStates();
        }

        /// <summary>
        /// 启用时间属性复选框变更事件
        /// </summary>
        private void ChkEnableTime_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateTimePropertiesControlStates();
        }

        /// <summary>
        /// 启用自定义属性复选框变更事件
        /// </summary>
        private void ChkEnableCustom_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateCustomPropertiesControlStates();
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            // 保存删除设置
            SaveDeletionSettings();

            // 保存基本信息设置
            SaveBasicPropertiesSettings();

            // 保存统计属性设置
            SaveStatisticsSettings();

            // 保存时间属性设置
            SaveTimePropertiesSettings();

            // 保存自定义属性设置
            SaveCustomPropertiesSettings();

            // 通过ConfigService保存到配置文件
            var configService = ConfigService.Instance;
            configService.SaveDocumentPropertiesSettings(_settings);
        }

        /// <summary>
        /// 保存删除设置
        /// </summary>
        private void SaveDeletionSettings()
        {
            var deletion = _settings.DeletionSettings;
            deletion.DeleteAllBuiltInProperties = _chkDeleteAllBuiltIn?.Checked ?? false;
            deletion.DeleteAllCustomProperties = _chkDeleteAllCustom?.Checked ?? false;
            deletion.DeleteBasicProperties = _chkDeleteBasic?.Checked ?? false;
            deletion.DeleteStatisticsProperties = _chkDeleteStatistics?.Checked ?? false;
            deletion.DeleteTimeProperties = _chkDeleteTime?.Checked ?? false;

            // 保存要删除的自定义属性列表
            deletion.CustomPropertiesToDelete.Clear();
            if (_lstCustomPropertiesToDelete != null)
            {
                foreach (string item in _lstCustomPropertiesToDelete.Items)
                {
                    deletion.CustomPropertiesToDelete.Add(item);
                }
            }
        }

        /// <summary>
        /// 保存基本信息设置
        /// </summary>
        private void SaveBasicPropertiesSettings()
        {
            var basic = _settings.BasicProperties;
            basic.IsEnabled = _chkEnableBasic?.Checked ?? false;

            basic.SetTitle = _chkSetTitle?.Checked ?? false;
            basic.Title = _txtTitle?.Text ?? "";

            basic.SetAuthor = _chkSetAuthor?.Checked ?? false;
            basic.Author = _txtAuthor?.Text ?? "";

            basic.SetSubject = _chkSetSubject?.Checked ?? false;
            basic.Subject = _txtSubject?.Text ?? "";

            basic.SetKeywords = _chkSetKeywords?.Checked ?? false;
            basic.Keywords = _txtKeywords?.Text ?? "";

            basic.SetComments = _chkSetComments?.Checked ?? false;
            basic.Comments = _txtComments?.Text ?? "";

            basic.SetCategory = _chkSetCategory?.Checked ?? false;
            basic.Category = _txtCategory?.Text ?? "";

            basic.SetCompany = _chkSetCompany?.Checked ?? false;
            basic.Company = _txtCompany?.Text ?? "";

            basic.SetManager = _chkSetManager?.Checked ?? false;
            basic.Manager = _txtManager?.Text ?? "";
        }

        /// <summary>
        /// 保存统计属性设置
        /// </summary>
        private void SaveStatisticsSettings()
        {
            var statistics = _settings.StatisticsProperties;
            statistics.IsEnabled = _chkEnableStatistics?.Checked ?? false;

            statistics.UpdateSlideCount = _chkUpdateSlideCount?.Checked ?? false;
            statistics.UpdateHiddenSlideCount = _chkUpdateHiddenSlideCount?.Checked ?? false;
            statistics.UpdateNotesCount = _chkUpdateNotesCount?.Checked ?? false;
            statistics.UpdateParagraphCount = _chkUpdateParagraphCount?.Checked ?? false;
            statistics.UpdateWordCount = _chkUpdateWordCount?.Checked ?? false;
            statistics.UpdateMultimediaClipCount = _chkUpdateMultimediaClipCount?.Checked ?? false;
        }

        /// <summary>
        /// 保存时间属性设置
        /// </summary>
        private void SaveTimePropertiesSettings()
        {
            var time = _settings.TimeProperties;
            time.IsEnabled = _chkEnableTime?.Checked ?? false;

            time.SetCreatedTime = _chkSetCreatedTime?.Checked ?? false;
            time.CreatedTime = _dtpCreatedTime?.Value ?? DateTime.Now;

            time.SetLastSavedTime = _chkSetLastSavedTime?.Checked ?? false;
            time.LastSavedTime = _dtpLastSavedTime?.Value ?? DateTime.Now;

            time.SetLastSavedBy = _chkSetLastSavedBy?.Checked ?? false;
            time.LastSavedBy = _txtLastSavedBy?.Text ?? "";

            time.SetLastPrintedTime = _chkSetLastPrintedTime?.Checked ?? false;
            time.LastPrintedTime = _dtpLastPrintedTime?.Value ?? DateTime.Now;

            time.SetTotalEditingTime = _chkSetTotalEditingTime?.Checked ?? false;
            time.TotalEditingTimeMinutes = (int)(_nudTotalEditingTimeMinutes?.Value ?? 0);
        }

        /// <summary>
        /// 保存自定义属性设置
        /// </summary>
        private void SaveCustomPropertiesSettings()
        {
            var custom = _settings.CustomProperties;
            custom.IsEnabled = _chkEnableCustom?.Checked ?? false;
            // 自定义属性列表已经在添加/编辑/删除时实时更新，这里不需要额外处理
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取文档属性设置
        /// </summary>
        public DocumentPropertiesSettings Settings => _settings;

        #endregion
    }
}
