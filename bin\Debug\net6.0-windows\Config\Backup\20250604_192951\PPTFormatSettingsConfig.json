{
  // PPT格式设置配置文件 - 控制PPT全局格式设置的详细参数
  // 此文件定义了段落格式、字体格式、主题设置、母版设置、布局设置和样式设置的配置

  // 启用PPT格式设置功能开关 - 是否启用PPT格式设置功能
  "EnablePPTFormatSettings": false,

  // 段落格式设置 - 控制段落对齐、缩进、间距等格式
  "ParagraphFormat": {
    // 启用段落格式设置 - 是否启用段落格式设置
    "EnableParagraphFormat": false,
    
    // 对齐方式设置
    "EnableAlignment": false,
    "Alignment": "Left", // Left, Center, Right, Justify, Distributed
    
    // 缩进设置
    "EnableIndent": false,
    "IndentBefore": 0.0, // 文本之前缩进（厘米）
    "SpecialIndentType": "无", // 无、首行缩进、悬挂缩进
    "SpecialIndentValue": 2.0, // 特殊缩进值（字符）
    
    // 间距设置
    "EnableSpacing": false,
    "SpacingBefore": 0.0, // 段前间距（磅）
    "SpacingAfter": 0.0, // 段后间距（磅）
    "LineSpacingType": "单倍行距", // 单倍行距、1.5倍行距、2倍行距、多倍行距、固定值
    "LineSpacingValue": 1.0, // 行距值
    
    // 段落选项
    "EnableOptions": false,
    "ChineseControl": true, // 按中文习惯控制中文首尾字符
    "WordWrap": false, // 允许西文在单词中间换行
    "PunctuationBoundary": false, // 允许标点移除边界
    
    // 文本对齐方式
    "EnableTextAlign": false,
    "TextAlignmentType": "自动" // 自动、居中、基线、底部
  },

  // 字体格式设置 - 控制字体选择、样式、大小、颜色等
  "FontFormat": {
    // 启用字体格式设置 - 是否启用字体格式设置
    "EnableFontFormat": false,
    
    // 字体选择设置
    "EnableFontSelection": false,
    "ChineseFont": "宋体", // 中文字体
    "EnglishFont": "Arial", // 西文字体
    
    // 字体样式设置
    "EnableFontStyle": false,
    "FontStyle": "常规", // 常规、倾斜、加粗、倾斜加粗
    "FontSize": 12.0, // 字体大小
    "FontColor": "#000000", // 字体颜色（十六进制）
    
    // 字体效果设置
    "EnableFontEffects": false,
    "UnderlineType": "无", // 无、单下划线、双下划线、粗下划线
    "UnderlineColor": "#000000", // 下划线颜色（十六进制）
    "Strikethrough": false, // 删除线
    "DoubleStrikethrough": false, // 双删除线
    "Superscript": false, // 上标
    "Subscript": false // 下标
  },

  // 主题设置 - 控制PPT主题、颜色方案、字体方案等
  "ThemeSettings": {
    // 启用主题设置 - 是否启用主题设置
    "EnableTheme": false,
    
    // 内置主题设置
    "EnableBuiltInTheme": false,
    "SelectedTheme": "Office 主题", // 选中的主题名称
    
    // 颜色方案设置
    "EnableColorScheme": false,
    "SelectedColorScheme": "Office 颜色方案", // 选中的颜色方案
    
    // 字体方案设置
    "EnableFontScheme": false,
    "SelectedFontScheme": "Office 字体方案", // 选中的字体方案
    
    // 主题效果设置
    "EnableThemeEffects": false,
    "EffectIntensity": 50, // 效果强度（0-100）
    
    // 主题背景设置
    "EnableThemeBackground": false,
    "BackgroundType": "纯色背景", // 纯色背景、渐变背景、图片背景
    "BackgroundColor": "#FFFFFF", // 背景颜色（十六进制）
    "Transparency": 0 // 透明度（0-100）
  },

  // 母版设置 - 控制幻灯片母版、标题母版等
  "MasterSettings": {
    // 启用母版设置 - 是否启用母版设置
    "EnableMaster": false,
    
    // 母版文件路径
    "SlideMasterFile": "", // 幻灯片母版文件路径
    "TitleMasterFile": "", // 标题母版文件路径
    "NotesMasterFile": "", // 备注母版文件路径
    "HandoutMasterFile": "", // 讲义母版文件路径
    
    // 母版应用设置
    "EnableMasterApplication": false,
    "PreserveContent": true, // 是否保留原有内容
    "ApplyColorScheme": true, // 是否应用母版颜色方案
    "ApplyFontScheme": true, // 是否应用母版字体方案
    
    // 母版背景设置
    "EnableMasterBackground": false,
    "MasterBackgroundType": "纯色背景", // 纯色背景、渐变背景、图片背景
    "MasterBackgroundColor": "#FFFFFF", // 母版背景颜色（十六进制）
    "MasterBackgroundTransparency": 0, // 母版背景透明度（0-100）
    
    // 母版文本样式设置
    "EnableMasterTextStyle": false,
    "MasterTitleFont": "微软雅黑", // 母版标题字体
    "MasterTitleSize": 24, // 母版标题字体大小
    "MasterTitleColor": "#000000", // 母版标题颜色（十六进制）
    "MasterBodyFont": "微软雅黑", // 母版正文字体
    "MasterBodySize": 14, // 母版正文字体大小
    "MasterBodyColor": "#000000", // 母版正文颜色（十六进制）
    
    // 母版页眉页脚设置
    "EnableMasterHeaderFooter": false,
    "ShowMasterHeader": false, // 显示母版页眉
    "ShowMasterFooter": false, // 显示母版页脚
    "ShowMasterSlideNumber": false, // 显示母版幻灯片编号
    "ShowMasterDateTime": false, // 显示母版日期时间
    "MasterHeaderText": "", // 母版页眉文本
    "MasterFooterText": "", // 母版页脚文本
    "MasterDateTimeFormat": "短" // 母版日期时间格式：短、长、自定义
  },

  // 布局设置 - 控制幻灯片布局样式
  "LayoutSettings": {
    // 启用布局设置 - 是否启用布局设置
    "EnableLayout": false,

    // 标题幻灯片布局设置
    "TitleSlideLayout": {
      "EnableTitleLayout": false,
      "LayoutTemplate": "标题居中",
      "TitlePosition": "中部",
      "TitleAlignment": "居中对齐",
      "TitleFont": "微软雅黑",
      "TitleFontSize": 36,
      "TitleColor": "#1F4E79",
      "TitleBold": true,
      "TitleItalic": false,
      "ShowSubtitle": true,
      "SubtitleFont": "微软雅黑",
      "SubtitleFontSize": 18,
      "SubtitleColor": "#666666",
      "BackgroundColor": "#FFFFFF",
      "AutoResizeText": true
    },

    // 内容布局设置
    "ContentLayout": {
      "EnableContentLayout": false,
      "TitleAreaStyle": "标准标题",
      "TitleHeight": 60,
      "ContentAlignment": "中部对齐",
      "LayoutType": "纯文本",
      "LayoutStyle": "单栏布局",
      "MarginTop": 10,
      "MarginLeft": 10,
      "MarginRight": 10,
      "MarginBottom": 10,
      "ContentFont": "微软雅黑",
      "ContentFontSize": 14,
      "ContentColor": "#000000",
      "LineSpacing": 1.2,
      "ParagraphSpacing": 6
    },

    // 两栏布局设置
    "TwoColumnLayout": {
      "EnableTwoColumnLayout": false,
      "LeftColumnWidth": 50,
      "RightColumnWidth": 50,
      "ColumnSpacing": 20,
      "ShowColumnTitles": false,
      "LeftColumnTitle": "左栏内容",
      "RightColumnTitle": "右栏内容",
      "ColumnFont": "微软雅黑",
      "ColumnFontSize": 12,
      "ColumnColor": "#000000",
      "TitleFont": "微软雅黑",
      "TitleFontSize": 14,
      "TitleColor": "#1F4E79",
      "TitleBold": true
    },

    // 图片布局设置
    "PictureLayout": {
      "EnablePictureLayout": false,
      "PictureWidth": 300,
      "PictureHeight": 200,
      "PicturePosition": "居中",
      "PictureCount": "单张",
      "ArrangementType": "网格排列",
      "PictureSpacing": 10,
      "RowSpacing": 15,
      "ColumnSpacing": 15,
      "ShowBorder": false,
      "BorderStyle": "实线",
      "BorderWidth": 1,
      "BorderColor": "#000000",
      "ShowShadow": false,
      "ShadowColor": "#808080",
      "CaptionFont": "微软雅黑",
      "CaptionFontSize": 10,
      "CaptionColor": "#666666",
      "CaptionPosition": "图片下方"
    },

    // 自定义布局设置
    "CustomLayout": {
      "EnableCustomLayout": false,
      "LayoutName": "自定义布局",
      "BaseTemplate": "空白",
      "BackgroundColor": "#FFFFFF",
      "DefaultFont": "微软雅黑",
      "DefaultFontSize": 14,
      "DefaultColor": "#000000",
      "GridEnabled": true,
      "GridSpacing": 10,
      "SnapToGrid": true,
      "ShowGuides": true
    }
  },

  // 样式设置 - 控制形状、文本、表格、图表等样式
  "StyleSettings": {
    // 启用样式设置 - 是否启用样式设置功能
    "EnableStyle": false,

    // 形状样式设置 - 控制形状填充、边框、阴影等样式效果
    "ShapeStyle": {
      "EnableShapeStyle": false,
      "FillType": "纯色填充", // 无填充、纯色填充、渐变填充、图片填充、纹理填充、图案填充
      "FillColor": "#4472C4", // 填充颜色（十六进制）
      "Transparency": 0, // 透明度（0-100）
      "BorderType": "实线", // 无边框、实线、虚线、点线、双线、粗线、细线、点划线
      "BorderColor": "#000000", // 边框颜色（十六进制）
      "BorderWidth": 1.0, // 边框宽度（磅）
      "ShadowType": "无阴影", // 无阴影、外部阴影、内部阴影、透视阴影、反射阴影
      "EnableGlow": false, // 启用发光效果
      "EnableReflection": false, // 启用反射效果
      "Enable3D": false // 启用3D效果
    },

    // 文本样式设置 - 控制文本字体、颜色、效果等样式
    "TextStyle": {
      "EnableTextStyle": false,
      "FontFamily": "微软雅黑", // 字体名称
      "FontSize": 14, // 字体大小
      "FontColor": "#000000", // 字体颜色（十六进制）
      "Bold": false, // 粗体
      "Italic": false, // 斜体
      "Underline": false, // 下划线
      "Strikethrough": false, // 删除线
      "TextAlignment": "左对齐", // 左对齐、居中对齐、右对齐、两端对齐、分散对齐
      "LineSpacing": "单倍行距", // 单倍行距、1.5倍行距、双倍行距、最小值、固定值、多倍行距
      "CharacterSpacing": 0, // 字符间距
      "EnableShadow": false, // 启用阴影
      "EnableOutline": false, // 启用轮廓
      "EnableEmboss": false, // 启用浮雕
      "EnableEngrave": false // 启用雕刻
    },

    // 表格样式设置 - 控制表格边框、填充、主题等样式
    "TableStyle": {
      "EnableTableStyle": false,
      "TableTheme": "默认主题", // 表格主题
      "HeaderRowStyle": true, // 标题行样式
      "FirstColumnStyle": false, // 首列样式
      "LastColumnStyle": false, // 末列样式
      "BandedRows": true, // 带状行
      "BandedColumns": false, // 带状列
      "BorderStyle": "实线", // 边框样式
      "BorderColor": "#000000", // 边框颜色（十六进制）
      "BorderWidth": 1.0, // 边框宽度（磅）
      "CellFillColor": "#FFFFFF", // 单元格填充颜色（十六进制）
      "AlternateRowColor": "#F2F2F2", // 交替行颜色（十六进制）
      "HeaderFillColor": "#4472C4", // 标题行填充颜色（十六进制）
      "HeaderFontColor": "#FFFFFF", // 标题行字体颜色（十六进制）
      "CellFontColor": "#000000" // 单元格字体颜色（十六进制）
    },

    // 图表样式设置 - 控制图表颜色、字体、主题等样式
    "ChartStyle": {
      "EnableChartStyle": false,
      "ChartTheme": "默认主题", // 图表主题
      "ColorScheme": "Office颜色方案", // 颜色方案
      "SeriesColors": ["#4472C4", "#E7E6E6", "#A5A5A5", "#FFC000", "#5B9BD5", "#70AD47"], // 数据系列颜色
      "TitleFont": "微软雅黑", // 标题字体
      "TitleFontSize": 18, // 标题字体大小
      "TitleFontColor": "#000000", // 标题字体颜色（十六进制）
      "AxisFont": "微软雅黑", // 轴标签字体
      "AxisFontSize": 12, // 轴标签字体大小
      "AxisFontColor": "#000000", // 轴标签字体颜色（十六进制）
      "LegendPosition": "右侧", // 图例位置：无、上方、下方、左侧、右侧
      "ShowDataLabels": false, // 显示数据标签
      "ShowGridLines": true, // 显示网格线
      "GridLineColor": "#D9D9D9" // 网格线颜色（十六进制）
    },

    // SmartArt样式设置 - 控制SmartArt图形的样式和效果
    "SmartArtStyle": {
      "EnableSmartArtStyle": false,
      "SmartArtTheme": "默认主题", // SmartArt主题
      "ColorScheme": "彩色", // 颜色方案：彩色、深色、浅色
      "StyleType": "精致", // 样式类型：简单、精致、3D
      "NodeFillColor": "#4472C4", // 节点填充颜色（十六进制）
      "NodeBorderColor": "#000000", // 节点边框颜色（十六进制）
      "TextColor": "#FFFFFF", // 文本颜色（十六进制）
      "FontFamily": "微软雅黑", // 字体
      "FontSize": 12 // 字体大小
    },

    // 多媒体样式设置 - 控制图片、视频、音频等多媒体元素样式
    "MultimediaStyle": {
      "EnableMultimediaStyle": false,
      "ImageBorderStyle": "无边框", // 图片边框样式
      "ImageBorderColor": "#000000", // 图片边框颜色（十六进制）
      "ImageBorderWidth": 1.0, // 图片边框宽度（磅）
      "ImageShadow": false, // 图片阴影
      "ImageReflection": false, // 图片反射
      "VideoControlsStyle": "默认", // 视频控件样式
      "AudioControlsStyle": "默认", // 音频控件样式
      "MediaEffects": "无效果" // 多媒体效果：无效果、淡入淡出、缩放、旋转
    }
  }
}
