// PPT内容删除处理服务
// 功能：处理PPT内容删除的各种操作，包括文档删除、内容删除、文本删除、图片删除等11个分类的删除功能
// 作者：PPT批量处理工具开发团队
// 创建时间：2024年
// 最后修改：2024年

using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Aspose.Slides;
using Aspose.Slides.Animation;
using Aspose.Slides.Charts;
using Aspose.Slides.SlideShow;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 内容删除服务类
    /// </summary>
    public class ContentDeletionService
    {
        /// <summary>
        /// 应用内容删除设置
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="settings">内容删除设置</param>
        /// <returns>处理结果</returns>
        public async Task<ProcessingResult> ApplyContentDeletionAsync(IPresentation presentation, ContentDeletionSettings settings)
        {
            try
            {
                var result = new ProcessingResult { IsSuccess = true };
                var messages = new List<string>();

                // 删除文档级别内容
                if (settings.DocumentDeletion != null)
                {
                    await ProcessDocumentDeletionAsync(presentation, settings.DocumentDeletion, messages);
                }

                // 删除内容
                if (settings.ContentRemoval != null)
                {
                    await ProcessContentRemovalAsync(presentation, settings.ContentRemoval, messages);
                }

                // 删除文本
                if (settings.TextDeletion != null)
                {
                    await ProcessTextDeletionAsync(presentation, settings.TextDeletion, messages);
                }

                // 删除图片
                if (settings.ImageDeletion != null)
                {
                    await ProcessImageDeletionAsync(presentation, settings.ImageDeletion, messages);
                }

                // 删除表格
                if (settings.TableDeletion != null)
                {
                    await ProcessTableDeletionAsync(presentation, settings.TableDeletion, messages);
                }

                // 删除图表
                if (settings.ChartDeletion != null)
                {
                    await ProcessChartDeletionAsync(presentation, settings.ChartDeletion, messages);
                }

                // 删除音频视频
                if (settings.MediaDeletion != null)
                {
                    await ProcessMediaDeletionAsync(presentation, settings.MediaDeletion, messages);
                }

                // 删除联系方式
                if (settings.ContactDeletion != null)
                {
                    await ProcessContactDeletionAsync(presentation, settings.ContactDeletion, messages);
                }

                // 删除动画
                if (settings.AnimationDeletion != null)
                {
                    await ProcessAnimationDeletionAsync(presentation, settings.AnimationDeletion, messages);
                }

                // 删除备注
                if (settings.NotesDeletion != null)
                {
                    await ProcessNotesDeletionAsync(presentation, settings.NotesDeletion, messages);
                }

                // 删除格式
                if (settings.FormatDeletion != null)
                {
                    await ProcessFormatDeletionAsync(presentation, settings.FormatDeletion, messages);
                }

                result.Message = string.Join("; ", messages);
                return result;
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"内容删除处理失败: {ex.Message}"
                };
            }
        }

        #region 文档删除处理

        /// <summary>
        /// 文档删除判断结果
        /// </summary>
        public class DocumentDeletionResult
        {
            /// <summary>
            /// 是否应该删除文档
            /// </summary>
            public bool ShouldDelete { get; set; } = false;

            /// <summary>
            /// 删除原因列表
            /// </summary>
            public List<string> DeletionReasons { get; set; } = new List<string>();

            /// <summary>
            /// 检查结果详情
            /// </summary>
            public List<string> CheckDetails { get; set; } = new List<string>();
        }

        /// <summary>
        /// 判断是否应该删除整个文档
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="presentation">演示文稿对象（可为null，如果为null则只进行文件级别检查）</param>
        /// <param name="settings">文档删除设置</param>
        /// <returns>删除判断结果</returns>
        public DocumentDeletionResult ShouldDeleteDocument(string filePath, IPresentation? presentation, DocumentDeletionSettings settings)
        {
            var result = new DocumentDeletionResult();

            // 检查总开关
            if (!settings.EnableDocumentDeletion)
            {
                result.CheckDetails.Add("文档删除功能未启用");
                return result;
            }

            var deletionReasons = new List<string>();
            var checkDetails = new List<string>();

            try
            {
                // 1. 文件名长度检查（不含扩展名）
                if (settings.EnableFileNameLengthCheck)
                {
                    var fileName = Path.GetFileNameWithoutExtension(filePath);
                    var fileNameLength = fileName?.Length ?? 0;
                    checkDetails.Add($"文件名长度检查: {fileNameLength} 字符 (范围: {settings.FileNameMinLength}-{settings.FileNameMaxLength})");

                    // 如果文件名长度在指定范围内，则删除文档
                    if (fileNameLength >= settings.FileNameMinLength && fileNameLength <= settings.FileNameMaxLength)
                    {
                        deletionReasons.Add($"文件名长度符合删除条件: {fileNameLength} 字符");
                    }
                }

                // 2. 文件大小检查
                if (settings.EnableFileSizeCheck)
                {
                    var fileInfo = new FileInfo(filePath);
                    var fileSizeInBytes = fileInfo.Length;

                    // 获取最小和最大大小的单位，支持向后兼容
                    var minSizeUnit = !string.IsNullOrEmpty(settings.FileMinSizeUnit) ? settings.FileMinSizeUnit : "KB";
                    var maxSizeUnit = !string.IsNullOrEmpty(settings.FileMaxSizeUnit) ? settings.FileMaxSizeUnit : "KB";

                    // 将设置中的大小转换为字节
                    var minSizeInBytes = ConvertSizeToBytes(settings.FileMinSize, minSizeUnit);
                    var maxSizeInBytes = ConvertSizeToBytes(settings.FileMaxSize, maxSizeUnit);

                    // 格式化显示范围信息，显示原始设置值和单位
                    var minSizeDisplay = $"{settings.FileMinSize} {minSizeUnit}";
                    var maxSizeDisplay = $"{settings.FileMaxSize} {maxSizeUnit}";
                    checkDetails.Add($"文件大小检查: {FormatFileSize(fileSizeInBytes)} (范围: {minSizeDisplay} - {maxSizeDisplay})");

                    // 如果文件大小在指定范围内，则删除文档
                    if (fileSizeInBytes >= minSizeInBytes && fileSizeInBytes <= maxSizeInBytes)
                    {
                        deletionReasons.Add($"文件大小符合删除条件: {FormatFileSize(fileSizeInBytes)} (范围: {minSizeDisplay} - {maxSizeDisplay})");
                    }
                }

                // 3. 文件名非法词检查
                if (settings.EnableFileNameIllegalWordsCheck && settings.FileNameIllegalWords?.Any() == true)
                {
                    var fileName = Path.GetFileNameWithoutExtension(filePath);
                    var foundIllegalWords = new List<string>();

                    foreach (var illegalWord in settings.FileNameIllegalWords)
                    {
                        if (!string.IsNullOrEmpty(illegalWord) && fileName.Contains(illegalWord, StringComparison.OrdinalIgnoreCase))
                        {
                            foundIllegalWords.Add(illegalWord);
                        }
                    }

                    if (foundIllegalWords.Any())
                    {
                        deletionReasons.Add($"文件名包含非法词: {string.Join(", ", foundIllegalWords)}");
                        checkDetails.Add($"文件名非法词检查: 发现非法词 {string.Join(", ", foundIllegalWords)}");
                    }
                    else
                    {
                        checkDetails.Add("文件名非法词检查: 通过");
                    }
                }

                // 4. 如果有演示文稿对象，进行内容相关检查
                if (presentation != null)
                {
                    // 根据删除范围获取需要检查的幻灯片集合
                    var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                    // 内容字符数检查
                    if (settings.EnableContentCharCountCheck)
                    {
                        int totalCharCount = CountCharactersInScope(slideScope);
                        checkDetails.Add($"内容字符数检查: {totalCharCount} 字符 (范围: {settings.ContentMinCharCount}-{settings.ContentMaxCharCount})");

                        // 如果内容字符数在指定范围内，则删除文档
                        if (totalCharCount >= settings.ContentMinCharCount && totalCharCount <= settings.ContentMaxCharCount)
                        {
                            deletionReasons.Add($"内容字符数符合删除条件: {totalCharCount} 字符");
                        }
                    }

                    // 页数检查
                    if (settings.EnablePageCountCheck)
                    {
                        int totalPageCount = CountPagesInScope(slideScope);
                        checkDetails.Add($"页数检查: {totalPageCount} 页 (范围: {settings.MinPageCount}-{settings.MaxPageCount})");

                        // 如果页数在指定范围内，则删除文档
                        if (totalPageCount >= settings.MinPageCount && totalPageCount <= settings.MaxPageCount)
                        {
                            deletionReasons.Add($"页数符合删除条件: {totalPageCount} 页");
                        }
                    }

                    // 内容非法词检查
                    if (settings.EnableContentIllegalWordsCheck && settings.ContentIllegalWords?.Any() == true)
                    {
                        var foundIllegalWords = FindIllegalWordsInScope(slideScope, settings.ContentIllegalWords);
                        if (foundIllegalWords.Any())
                        {
                            deletionReasons.Add($"内容包含非法词: {string.Join(", ", foundIllegalWords)}");
                            checkDetails.Add($"内容非法词检查: 发现非法词 {string.Join(", ", foundIllegalWords)}");
                        }
                        else
                        {
                            checkDetails.Add("内容非法词检查: 通过");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                checkDetails.Add($"检查过程中发生错误: {ex.Message}");
            }

            result.ShouldDelete = deletionReasons.Any();
            result.DeletionReasons = deletionReasons;
            result.CheckDetails = checkDetails;

            return result;
        }

        /// <summary>
        /// 处理文档删除（现在只记录检查结果，实际删除在文件处理层面进行）
        /// </summary>
        private async Task ProcessDocumentDeletionAsync(IPresentation presentation, DocumentDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableDocumentDeletion)
                {
                    return;
                }

                // 这里只记录启用状态，实际的删除判断在 ShouldDeleteDocument 方法中进行
                var enabledChecks = new List<string>();

                if (settings.EnableFileNameLengthCheck)
                {
                    enabledChecks.Add("文件名长度检查");
                }

                if (settings.EnableFileSizeCheck)
                {
                    enabledChecks.Add("文件大小检查");
                }

                if (settings.EnableContentCharCountCheck)
                {
                    enabledChecks.Add("内容字符数检查");
                }

                if (settings.EnablePageCountCheck)
                {
                    enabledChecks.Add("页数检查");
                }

                if (settings.EnableFileNameIllegalWordsCheck)
                {
                    enabledChecks.Add("文件名非法词检查");
                }

                if (settings.EnableContentIllegalWordsCheck)
                {
                    enabledChecks.Add("内容非法词检查");
                }

                if (enabledChecks.Any())
                {
                    messages.Add($"文档删除检查已启用: {string.Join(", ", enabledChecks)}");
                }
            });
        }

        /// <summary>
        /// 将大小设置转换为字节数
        /// </summary>
        private long ConvertSizeToBytes(long size, string unit)
        {
            return unit?.ToUpper() switch
            {
                "B" => size,
                "KB" => size * 1024,
                "MB" => size * 1024 * 1024,
                "GB" => size * 1024 * 1024 * 1024,
                _ => size * 1024 // 默认为KB
            };
        }

        /// <summary>
        /// 格式化文件大小显示
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            if (bytes >= 1024 * 1024 * 1024)
                return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
            if (bytes >= 1024 * 1024)
                return $"{bytes / (1024.0 * 1024.0):F1} MB";
            if (bytes >= 1024)
                return $"{bytes / 1024.0:F1} KB";
            return $"{bytes} B";
        }

        /// <summary>
        /// 统计指定范围内的字符数
        /// </summary>
        private int CountCharactersInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int totalCharCount = 0;

            // 统计普通幻灯片的字符数
            foreach (var slide in slideScope.NormalSlides)
            {
                totalCharCount += CountCharactersInSlide(slide);
            }

            // 统计母版的字符数
            foreach (var master in slideScope.Masters)
            {
                totalCharCount += CountCharactersInSlide(master);
            }

            // 统计版式页的字符数
            foreach (var layout in slideScope.LayoutSlides)
            {
                totalCharCount += CountCharactersInSlide(layout);
            }

            return totalCharCount;
        }

        /// <summary>
        /// 统计单个幻灯片的字符数
        /// </summary>
        private int CountCharactersInSlide(IBaseSlide slide)
        {
            int charCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    charCount += autoShape.TextFrame.Text?.Length ?? 0;
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                charCount += cell.TextFrame.Text?.Length ?? 0;
                            }
                        }
                    }
                }
                else if (shape is IGroupShape groupShape)
                {
                    charCount += CountCharactersInGroupShape(groupShape);
                }
            }

            return charCount;
        }

        /// <summary>
        /// 统计组合形状中的字符数
        /// </summary>
        private int CountCharactersInGroupShape(IGroupShape groupShape)
        {
            int charCount = 0;

            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    charCount += autoShape.TextFrame.Text?.Length ?? 0;
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                charCount += cell.TextFrame.Text?.Length ?? 0;
                            }
                        }
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    charCount += CountCharactersInGroupShape(nestedGroupShape);
                }
            }

            return charCount;
        }

        /// <summary>
        /// 统计指定范围内的页数
        /// </summary>
        private int CountPagesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            return slideScope.NormalSlides.Count + slideScope.Masters.Count + slideScope.LayoutSlides.Count;
        }

        /// <summary>
        /// 在指定范围内查找非法词
        /// </summary>
        private List<string> FindIllegalWordsInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope, List<string> illegalWords)
        {
            var foundWords = new HashSet<string>();

            // 检查普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                var wordsInSlide = FindIllegalWordsInSlide(slide, illegalWords);
                foreach (var word in wordsInSlide)
                {
                    foundWords.Add(word);
                }
            }

            // 检查母版
            foreach (var master in slideScope.Masters)
            {
                var wordsInSlide = FindIllegalWordsInSlide(master, illegalWords);
                foreach (var word in wordsInSlide)
                {
                    foundWords.Add(word);
                }
            }

            // 检查版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                var wordsInSlide = FindIllegalWordsInSlide(layout, illegalWords);
                foreach (var word in wordsInSlide)
                {
                    foundWords.Add(word);
                }
            }

            return foundWords.ToList();
        }

        /// <summary>
        /// 在单个幻灯片中查找非法词
        /// </summary>
        private List<string> FindIllegalWordsInSlide(IBaseSlide slide, List<string> illegalWords)
        {
            var foundWords = new HashSet<string>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    var text = autoShape.TextFrame.Text ?? "";
                    foreach (var illegalWord in illegalWords)
                    {
                        if (text.Contains(illegalWord, StringComparison.OrdinalIgnoreCase))
                        {
                            foundWords.Add(illegalWord);
                        }
                    }
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                var text = cell.TextFrame.Text ?? "";
                                foreach (var illegalWord in illegalWords)
                                {
                                    if (text.Contains(illegalWord, StringComparison.OrdinalIgnoreCase))
                                    {
                                        foundWords.Add(illegalWord);
                                    }
                                }
                            }
                        }
                    }
                }
                else if (shape is IGroupShape groupShape)
                {
                    var wordsInGroup = FindIllegalWordsInGroupShape(groupShape, illegalWords);
                    foreach (var word in wordsInGroup)
                    {
                        foundWords.Add(word);
                    }
                }
            }

            return foundWords.ToList();
        }

        /// <summary>
        /// 在组合形状中查找非法词
        /// </summary>
        private List<string> FindIllegalWordsInGroupShape(IGroupShape groupShape, List<string> illegalWords)
        {
            var foundWords = new HashSet<string>();

            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    var text = autoShape.TextFrame.Text ?? "";
                    foreach (var illegalWord in illegalWords)
                    {
                        if (text.Contains(illegalWord, StringComparison.OrdinalIgnoreCase))
                        {
                            foundWords.Add(illegalWord);
                        }
                    }
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                var text = cell.TextFrame.Text ?? "";
                                foreach (var illegalWord in illegalWords)
                                {
                                    if (text.Contains(illegalWord, StringComparison.OrdinalIgnoreCase))
                                    {
                                        foundWords.Add(illegalWord);
                                    }
                                }
                            }
                        }
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    var wordsInNestedGroup = FindIllegalWordsInGroupShape(nestedGroupShape, illegalWords);
                    foreach (var word in wordsInNestedGroup)
                    {
                        foundWords.Add(word);
                    }
                }
            }

            return foundWords.ToList();
        }

        #endregion

        #region 内容删除处理

        /// <summary>
        /// 处理内容删除
        /// </summary>
        private async Task ProcessContentRemovalAsync(IPresentation presentation, ContentRemovalSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableContentRemoval)
                {
                    return;
                }

                int deletedSlides = 0;

                // 删除空白幻灯片
                if (settings.DeleteBlankSlides)
                {
                    deletedSlides += DeleteBlankSlides(presentation);
                }

                // 删除不包含任何文字内容的页
                if (settings.DeleteSlidesWithoutText)
                {
                    deletedSlides += DeleteSlidesWithoutText(presentation);
                }

                // 删除第一页
                if (settings.DeleteFirstSlide && presentation.Slides.Count > 1)
                {
                    presentation.Slides.RemoveAt(0);
                    deletedSlides++;
                }

                // 删除最后一页
                if (settings.DeleteLastSlide && presentation.Slides.Count > 1)
                {
                    presentation.Slides.RemoveAt(presentation.Slides.Count - 1);
                    deletedSlides++;
                }

                // 删除指定页范围
                if (settings.EnableSlideRangeDeletion)
                {
                    deletedSlides += DeleteSlideRange(presentation, settings.SlideRangeStart, settings.SlideRangeEnd);
                }

                // 删除包含关键词的页
                if (settings.EnableKeywordSlidesDeletion && settings.SlideKeywords?.Any() == true)
                {
                    deletedSlides += DeleteSlidesWithKeywords(presentation, settings.SlideKeywords);
                }

                // 删除包含指定图片的页
                if (settings.EnableSpecificImageSlidesDeletion && settings.SpecificImageNames.Count > 0)
                {
                    deletedSlides += DeleteSpecificImageSlides(presentation, settings.SpecificImageNames);
                }

                // 删除空白段落和行（使用删除范围）
                if (settings.DeleteBlankParagraphs || settings.DeleteBlankLines)
                {
                    // 根据删除范围获取需要处理的幻灯片集合
                    var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);
                    DeleteBlankTextContentInScope(slideScope, settings.DeleteBlankParagraphs, settings.DeleteBlankLines);
                }

                if (deletedSlides > 0)
                {
                    messages.Add($"已删除 {deletedSlides} 张幻灯片");
                }
            });
        }

        /// <summary>
        /// 删除空白幻灯片
        /// </summary>
        private int DeleteBlankSlides(IPresentation presentation)
        {
            int deletedCount = 0;
            var slidesToDelete = new List<ISlide>();

            foreach (ISlide slide in presentation.Slides)
            {
                if (IsSlideBlank(slide))
                {
                    slidesToDelete.Add(slide);
                }
            }

            foreach (var slide in slidesToDelete)
            {
                slide.Remove();
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 判断幻灯片是否为空白
        /// </summary>
        private bool IsSlideBlank(ISlide slide)
        {
            // 检查是否有形状
            if (slide.Shapes.Count == 0)
                return true;

            // 检查所有形状是否都没有内容
            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    if (!string.IsNullOrWhiteSpace(autoShape.TextFrame.Text))
                        return false;
                }
                else if (shape is IPictureFrame || shape is IChart || shape is ITable)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 删除不包含任何文字内容的页 - 删除只含图片或图形但没有文字的幻灯片
        /// </summary>
        private int DeleteSlidesWithoutText(IPresentation presentation)
        {
            int deletedCount = 0;
            var slidesToDelete = new List<ISlide>();

            foreach (ISlide slide in presentation.Slides)
            {
                if (IsSlideWithoutText(slide))
                {
                    slidesToDelete.Add(slide);
                }
            }

            foreach (var slide in slidesToDelete)
            {
                slide.Remove();
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 判断幻灯片是否不包含任何文字内容
        /// </summary>
        private bool IsSlideWithoutText(ISlide slide)
        {
            // 遍历幻灯片中的所有形状
            foreach (IShape shape in slide.Shapes)
            {
                // 检查自动形状（包括文本框、占位符等）
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    // 如果找到任何非空白文本，则该幻灯片包含文字内容
                    if (!string.IsNullOrWhiteSpace(autoShape.TextFrame.Text))
                        return false;
                }
                // 检查表格中的文字内容
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null && !string.IsNullOrWhiteSpace(cell.TextFrame.Text))
                            {
                                return false;
                            }
                        }
                    }
                }
                // 检查组合形状中的文字内容
                else if (shape is IGroupShape groupShape)
                {
                    if (GroupShapeContainsText(groupShape))
                        return false;
                }
            }

            // 如果没有找到任何文字内容，则该幻灯片不包含文字
            return true;
        }

        /// <summary>
        /// 检查组合形状是否包含文字内容
        /// </summary>
        private bool GroupShapeContainsText(IGroupShape groupShape)
        {
            // 使用索引遍历组合形状中的子形状
            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    if (!string.IsNullOrWhiteSpace(autoShape.TextFrame.Text))
                        return true;
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null && !string.IsNullOrWhiteSpace(cell.TextFrame.Text))
                            {
                                return true;
                            }
                        }
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    if (GroupShapeContainsText(nestedGroupShape))
                        return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 删除包含指定图片的页 - 删除包含任意一个指定图片的幻灯片
        /// </summary>
        private int DeleteSpecificImageSlides(IPresentation presentation, List<string> imagePaths)
        {
            int deletedCount = 0;
            var slidesToDelete = new List<ISlide>();

            // 预加载参考图片信息
            var referenceImages = LoadReferenceImages(imagePaths);
            if (referenceImages.Count == 0)
            {
                return 0; // 没有有效的参考图片
            }

            foreach (ISlide slide in presentation.Slides)
            {
                if (SlideContainsSpecificImages(slide, referenceImages))
                {
                    slidesToDelete.Add(slide);
                }
            }

            foreach (var slide in slidesToDelete)
            {
                slide.Remove();
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 图片信息类 - 用于存储参考图片的关键信息
        /// </summary>
        private class ReferenceImageInfo
        {
            public string FilePath { get; set; } = "";
            public int Width { get; set; }
            public int Height { get; set; }
            public byte[] ImageData { get; set; } = Array.Empty<byte>();
            public string ContentType { get; set; } = "";
            public long FileSize { get; set; }

            // 用于快速比较的哈希值
            public string DataHash { get; set; } = "";
        }

        /// <summary>
        /// 加载参考图片信息
        /// </summary>
        private List<ReferenceImageInfo> LoadReferenceImages(List<string> imagePaths)
        {
            var referenceImages = new List<ReferenceImageInfo>();

            foreach (var imagePath in imagePaths)
            {
                try
                {
                    if (File.Exists(imagePath))
                    {
                        var imageData = File.ReadAllBytes(imagePath);
                        var fileInfo = new FileInfo(imagePath);

                        // 使用System.Drawing获取图片尺寸
                        int width = 0, height = 0;
                        string contentType = "";

                        try
                        {
                            using (var image = System.Drawing.Image.FromFile(imagePath))
                            {
                                width = image.Width;
                                height = image.Height;

                                // 根据图片格式设置ContentType
                                contentType = image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Jpeg) ? "image/jpeg" :
                                            image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Png) ? "image/png" :
                                            image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Gif) ? "image/gif" :
                                            image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Bmp) ? "image/bmp" :
                                            image.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Tiff) ? "image/tiff" :
                                            "image/unknown";
                            }
                        }
                        catch
                        {
                            // 如果无法读取图片信息，跳过这个文件
                            continue;
                        }

                        // 计算数据哈希值用于快速比较
                        var dataHash = ComputeImageHash(imageData);

                        var refImage = new ReferenceImageInfo
                        {
                            FilePath = imagePath,
                            Width = width,
                            Height = height,
                            ImageData = imageData,
                            ContentType = contentType,
                            FileSize = fileInfo.Length,
                            DataHash = dataHash
                        };

                        referenceImages.Add(refImage);
                    }
                }
                catch (Exception)
                {
                    // 加载图片失败，跳过这个文件
                    continue;
                }
            }

            return referenceImages;
        }

        /// <summary>
        /// 计算图片数据的哈希值
        /// </summary>
        private string ComputeImageHash(byte[] imageData)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(imageData);
                return Convert.ToBase64String(hashBytes);
            }
        }

        /// <summary>
        /// 判断幻灯片是否包含指定的图片
        /// </summary>
        private bool SlideContainsSpecificImages(ISlide slide, List<ReferenceImageInfo> referenceImages)
        {
            // 遍历幻灯片中的所有形状
            foreach (IShape shape in slide.Shapes)
            {
                // 检查图片框
                if (shape is IPictureFrame pictureFrame)
                {
                    if (IsMatchingImage(pictureFrame, referenceImages))
                        return true;
                }
                // 检查组合形状中的图片
                else if (shape is IGroupShape groupShape)
                {
                    if (GroupShapeContainsSpecificImages(groupShape, referenceImages))
                        return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 检查组合形状是否包含指定图片
        /// </summary>
        private bool GroupShapeContainsSpecificImages(IGroupShape groupShape, List<ReferenceImageInfo> referenceImages)
        {
            // 使用索引遍历组合形状中的子形状
            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IPictureFrame pictureFrame)
                {
                    if (IsMatchingImage(pictureFrame, referenceImages))
                        return true;
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    if (GroupShapeContainsSpecificImages(nestedGroupShape, referenceImages))
                        return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 判断图片是否匹配参考图片列表 - 基于图片内容进行精确匹配
        /// </summary>
        private bool IsMatchingImage(IPictureFrame pictureFrame, List<ReferenceImageInfo> referenceImages)
        {
            try
            {
                // 获取幻灯片中图片的信息
                var picture = pictureFrame.PictureFormat?.Picture;
                if (picture?.Image == null) return false;

                var slideImageData = picture.Image.BinaryData;
                var slideImageWidth = picture.Image.Width;
                var slideImageHeight = picture.Image.Height;
                var slideImageContentType = picture.Image.ContentType;

                // 计算幻灯片图片的哈希值
                var slideImageHash = ComputeImageHash(slideImageData);

                // 与每个参考图片进行比较
                foreach (var refImage in referenceImages)
                {
                    // 1. 首先比较哈希值（最快速的方式）
                    if (slideImageHash == refImage.DataHash)
                    {
                        return true;
                    }

                    // 2. 比较图片尺寸和文件大小
                    if (slideImageWidth == refImage.Width &&
                        slideImageHeight == refImage.Height &&
                        slideImageData.Length == refImage.FileSize)
                    {
                        // 3. 比较ContentType
                        if (slideImageContentType == refImage.ContentType)
                        {
                            // 4. 最后比较完整的二进制数据
                            if (CompareImageData(slideImageData, refImage.ImageData))
                            {
                                return true;
                            }
                        }
                    }

                    // 5. 对于可能经过压缩或格式转换的图片，比较尺寸和部分数据
                    if (slideImageWidth == refImage.Width && slideImageHeight == refImage.Height)
                    {
                        // 比较前1024字节的文件头信息
                        if (CompareImageHeaders(slideImageData, refImage.ImageData))
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception)
            {
                // 发生异常时返回false，继续处理其他图片
                return false;
            }
        }

        /// <summary>
        /// 比较两个图片的二进制数据
        /// </summary>
        private bool CompareImageData(byte[] data1, byte[] data2)
        {
            if (data1.Length != data2.Length)
                return false;

            for (int i = 0; i < data1.Length; i++)
            {
                if (data1[i] != data2[i])
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 比较两个图片的文件头信息（前1024字节）
        /// </summary>
        private bool CompareImageHeaders(byte[] data1, byte[] data2)
        {
            var compareLength = Math.Min(Math.Min(1024, data1.Length), data2.Length);

            for (int i = 0; i < compareLength; i++)
            {
                if (data1[i] != data2[i])
                    return false;
            }

            return true;
        }



        /// <summary>
        /// 删除指定范围的幻灯片
        /// </summary>
        private int DeleteSlideRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;
            
            // 确保索引有效
            startIndex = Math.Max(0, startIndex - 1); // 转换为0基索引
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            if (startIndex <= endIndex)
            {
                // 从后往前删除，避免索引变化问题
                for (int i = endIndex; i >= startIndex; i--)
                {
                    if (i < presentation.Slides.Count)
                    {
                        presentation.Slides.RemoveAt(i);
                        deletedCount++;
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除包含关键词的幻灯片
        /// </summary>
        private int DeleteSlidesWithKeywords(IPresentation presentation, List<string> keywords)
        {
            int deletedCount = 0;
            var slidesToDelete = new List<ISlide>();

            foreach (ISlide slide in presentation.Slides)
            {
                if (SlideContainsKeywords(slide, keywords))
                {
                    slidesToDelete.Add(slide);
                }
            }

            foreach (var slide in slidesToDelete)
            {
                slide.Remove();
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 检查幻灯片是否包含关键词
        /// </summary>
        private bool SlideContainsKeywords(ISlide slide, List<string> keywords)
        {
            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    var text = autoShape.TextFrame.Text;
                    if (keywords.Any(keyword => text.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 删除空白文本内容
        /// </summary>
        private void DeleteBlankTextContent(IPresentation presentation, bool deleteBlankParagraphs, bool deleteBlankLines)
        {
            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        if (deleteBlankParagraphs)
                        {
                            DeleteBlankParagraphs(autoShape.TextFrame);
                        }
                        
                        if (deleteBlankLines)
                        {
                            DeleteBlankLines(autoShape.TextFrame);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 删除空白段落
        /// </summary>
        private void DeleteBlankParagraphs(ITextFrame textFrame)
        {
            var paragraphsToDelete = new List<IParagraph>();
            
            foreach (IParagraph paragraph in textFrame.Paragraphs)
            {
                if (string.IsNullOrWhiteSpace(paragraph.Text))
                {
                    paragraphsToDelete.Add(paragraph);
                }
            }

            foreach (var paragraph in paragraphsToDelete)
            {
                textFrame.Paragraphs.Remove(paragraph);
            }
        }

        /// <summary>
        /// 删除空白行
        /// </summary>
        private void DeleteBlankLines(ITextFrame textFrame)
        {
            foreach (IParagraph paragraph in textFrame.Paragraphs)
            {
                var portionsToDelete = new List<IPortion>();
                
                foreach (IPortion portion in paragraph.Portions)
                {
                    if (string.IsNullOrWhiteSpace(portion.Text))
                    {
                        portionsToDelete.Add(portion);
                    }
                }

                foreach (var portion in portionsToDelete)
                {
                    paragraph.Portions.Remove(portion);
                }
            }
        }

        /// <summary>
        /// 在指定范围内删除空白文本内容（段落和行）
        /// </summary>
        private void DeleteBlankTextContentInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope, bool deleteBlankParagraphs, bool deleteBlankLines)
        {
            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                DeleteBlankTextContentFromSlide(slide, deleteBlankParagraphs, deleteBlankLines);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                DeleteBlankTextContentFromSlide(master, deleteBlankParagraphs, deleteBlankLines);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                DeleteBlankTextContentFromSlide(layout, deleteBlankParagraphs, deleteBlankLines);
            }
        }

        /// <summary>
        /// 从单个幻灯片删除空白文本内容
        /// </summary>
        private void DeleteBlankTextContentFromSlide(IBaseSlide slide, bool deleteBlankParagraphs, bool deleteBlankLines)
        {
            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    if (deleteBlankParagraphs)
                    {
                        DeleteBlankParagraphs(autoShape.TextFrame);
                    }

                    if (deleteBlankLines)
                    {
                        DeleteBlankLines(autoShape.TextFrame);
                    }
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                if (deleteBlankParagraphs)
                                {
                                    DeleteBlankParagraphs(cell.TextFrame);
                                }

                                if (deleteBlankLines)
                                {
                                    DeleteBlankLines(cell.TextFrame);
                                }
                            }
                        }
                    }
                }
                else if (shape is IGroupShape groupShape)
                {
                    DeleteBlankTextContentFromGroupShape(groupShape, deleteBlankParagraphs, deleteBlankLines);
                }
            }
        }

        /// <summary>
        /// 从组合形状删除空白文本内容
        /// </summary>
        private void DeleteBlankTextContentFromGroupShape(IGroupShape groupShape, bool deleteBlankParagraphs, bool deleteBlankLines)
        {
            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    if (deleteBlankParagraphs)
                    {
                        DeleteBlankParagraphs(autoShape.TextFrame);
                    }

                    if (deleteBlankLines)
                    {
                        DeleteBlankLines(autoShape.TextFrame);
                    }
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                if (deleteBlankParagraphs)
                                {
                                    DeleteBlankParagraphs(cell.TextFrame);
                                }

                                if (deleteBlankLines)
                                {
                                    DeleteBlankLines(cell.TextFrame);
                                }
                            }
                        }
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    DeleteBlankTextContentFromGroupShape(nestedGroupShape, deleteBlankParagraphs, deleteBlankLines);
                }
            }
        }

        #endregion

        #region 文本删除处理

        /// <summary>
        /// 处理文本删除
        /// </summary>
        private async Task ProcessTextDeletionAsync(IPresentation presentation, TextDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableTextDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                // 删除所有文本 - 删除PPT中的所有文本内容
                if (settings.DeleteAllText)
                {
                    deletedCount += DeleteAllTextInScope(slideScope);
                }

                // 删除文本框 - 删除PPT中的文本框
                if (settings.DeleteTextBoxes)
                {
                    deletedCount += DeleteTextBoxesInScope(slideScope);
                }

                // 删除标题 - 删除PPT中的标题文本
                if (settings.DeleteTitles)
                {
                    deletedCount += DeleteTitlesInScope(slideScope);
                }

                // 删除项目符号 - 删除PPT中的项目符号和编号列表
                if (settings.DeleteBulletPoints)
                {
                    deletedCount += DeleteBulletPointsInScope(slideScope);
                }

                // 删除特定文本 - 删除指定的文本内容
                if (settings.DeleteSpecificText && settings.SpecificTextContent?.Any() == true)
                {
                    deletedCount += DeleteSpecificTextInScope(slideScope, settings.SpecificTextContent);
                }

                // 删除文本范围 - 删除指定幻灯片范围内的文本（仅适用于普通幻灯片）
                if (settings.DeleteTextRange && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteTextInRange(presentation, settings.TextSlideRangeStart, settings.TextSlideRangeEnd);
                }

                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个文本元素");
                }
            });
        }

        /// <summary>
        /// 删除所有文本 - 删除PPT中的所有文本内容
        /// </summary>
        private int DeleteAllText(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        // 清空文本框内容
                        autoShape.TextFrame.Text = "";
                        deletedCount++;
                    }
                    else if (shape is ITable table)
                    {
                        // 清空表格中的文本
                        for (int row = 0; row < table.Rows.Count; row++)
                        {
                            for (int col = 0; col < table.Columns.Count; col++)
                            {
                                var cell = table[col, row];
                                if (cell.TextFrame != null)
                                {
                                    cell.TextFrame.Text = "";
                                    deletedCount++;
                                }
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除文本框 - 删除PPT中的文本框
        /// </summary>
        private int DeleteTextBoxes(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                var shapesToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        // 判断是否为文本框（非占位符）
                        if (autoShape.Placeholder == null)
                        {
                            shapesToDelete.Add(shape);
                        }
                    }
                }

                foreach (var shape in shapesToDelete)
                {
                    slide.Shapes.Remove(shape);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除标题 - 删除PPT中的标题文本
        /// </summary>
        private int DeleteTitles(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        // 判断是否为标题占位符
                        if (autoShape.Placeholder != null &&
                            (autoShape.Placeholder.Type == PlaceholderType.Title ||
                             autoShape.Placeholder.Type == PlaceholderType.CenteredTitle))
                        {
                            autoShape.TextFrame.Text = "";
                            deletedCount++;
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除项目符号 - 删除PPT中的项目符号和编号列表
        /// </summary>
        private int DeleteBulletPoints(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                        {
                            if (paragraph.ParagraphFormat.Bullet.Type != BulletType.None)
                            {
                                // 移除项目符号格式
                                paragraph.ParagraphFormat.Bullet.Type = BulletType.None;
                                deletedCount++;
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除特定文本 - 删除指定的文本内容
        /// </summary>
        private int DeleteSpecificText(IPresentation presentation, List<string> textContent)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        foreach (var textToDelete in textContent)
                        {
                            if (autoShape.TextFrame.Text.Contains(textToDelete, StringComparison.OrdinalIgnoreCase))
                            {
                                autoShape.TextFrame.Text = autoShape.TextFrame.Text.Replace(textToDelete, "", StringComparison.OrdinalIgnoreCase);
                                deletedCount++;
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除文本范围 - 删除指定幻灯片范围内的文本
        /// </summary>
        private int DeleteTextInRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            startIndex = Math.Max(0, startIndex - 1); // 转换为0基索引
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            for (int i = startIndex; i <= endIndex && i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        autoShape.TextFrame.Text = "";
                        deletedCount++;
                    }
                }
            }

            return deletedCount;
        }



        /// <summary>
        /// 删除包含关键词的段落
        /// </summary>
        private int DeleteParagraphsWithKeywords(IPresentation presentation, List<string> keywords)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        var paragraphsToDelete = new List<IParagraph>();

                        foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                        {
                            if (keywords.Any(keyword => paragraph.Text.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
                            {
                                paragraphsToDelete.Add(paragraph);
                            }
                        }

                        foreach (var paragraph in paragraphsToDelete)
                        {
                            autoShape.TextFrame.Paragraphs.Remove(paragraph);
                            deletedCount++;
                        }
                    }
                }
            }

            return deletedCount;
        }



        /// <summary>
        /// 在指定范围内删除所有文本
        /// </summary>
        private int DeleteAllTextInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteAllTextFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteAllTextFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteAllTextFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除文本框
        /// </summary>
        private int DeleteTextBoxesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteTextBoxesFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteTextBoxesFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteTextBoxesFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除标题
        /// </summary>
        private int DeleteTitlesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteTitlesFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteTitlesFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteTitlesFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除项目符号
        /// </summary>
        private int DeleteBulletPointsInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteBulletPointsFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteBulletPointsFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteBulletPointsFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除特定文本
        /// </summary>
        private int DeleteSpecificTextInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope, List<string> textContent)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteSpecificTextFromSlide(slide, textContent);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteSpecificTextFromSlide(master, textContent);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteSpecificTextFromSlide(layout, textContent);
            }

            return deletedCount;
        }



        /// <summary>
        /// 从单个幻灯片删除所有文本
        /// </summary>
        private int DeleteAllTextFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;
            var shapesToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    autoShape.TextFrame.Text = "";
                    deletedCount++;
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                cell.TextFrame.Text = "";
                                deletedCount++;
                            }
                        }
                    }
                }
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteAllTextFromGroupShape(groupShape);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状删除所有文本
        /// </summary>
        private int DeleteAllTextFromGroupShape(IGroupShape groupShape)
        {
            int deletedCount = 0;

            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    autoShape.TextFrame.Text = "";
                    deletedCount++;
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                cell.TextFrame.Text = "";
                                deletedCount++;
                            }
                        }
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    deletedCount += DeleteAllTextFromGroupShape(nestedGroupShape);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除文本框
        /// </summary>
        private int DeleteTextBoxesFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;
            var shapesToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null &&
                    autoShape.ShapeType == ShapeType.Rectangle) // 文本框通常是矩形形状
                {
                    shapesToDelete.Add(shape);
                }
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteTextBoxesFromGroupShape(groupShape);
                }
            }

            foreach (var shape in shapesToDelete)
            {
                slide.Shapes.Remove(shape);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状删除文本框
        /// </summary>
        private int DeleteTextBoxesFromGroupShape(IGroupShape groupShape)
        {
            int deletedCount = 0;
            var shapesToDelete = new List<IShape>();

            for (int i = groupShape.Shapes.Count - 1; i >= 0; i--)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null &&
                    autoShape.ShapeType == ShapeType.Rectangle)
                {
                    groupShape.Shapes.RemoveAt(i);
                    deletedCount++;
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    deletedCount += DeleteTextBoxesFromGroupShape(nestedGroupShape);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除标题
        /// </summary>
        private int DeleteTitlesFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    // 检查是否为标题占位符
                    if (autoShape.Placeholder != null &&
                        (autoShape.Placeholder.Type == PlaceholderType.Title ||
                         autoShape.Placeholder.Type == PlaceholderType.CenteredTitle))
                    {
                        autoShape.TextFrame.Text = "";
                        deletedCount++;
                    }
                }
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteTitlesFromGroupShape(groupShape);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状删除标题
        /// </summary>
        private int DeleteTitlesFromGroupShape(IGroupShape groupShape)
        {
            int deletedCount = 0;

            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    if (autoShape.Placeholder != null &&
                        (autoShape.Placeholder.Type == PlaceholderType.Title ||
                         autoShape.Placeholder.Type == PlaceholderType.CenteredTitle))
                    {
                        autoShape.TextFrame.Text = "";
                        deletedCount++;
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    deletedCount += DeleteTitlesFromGroupShape(nestedGroupShape);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除项目符号
        /// </summary>
        private int DeleteBulletPointsFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    var textFrame = autoShape.TextFrame;
                    for (int i = 0; i < textFrame.Paragraphs.Count; i++)
                    {
                        var paragraph = textFrame.Paragraphs[i];
                        if (paragraph.ParagraphFormat.Bullet.Type != BulletType.None)
                        {
                            paragraph.ParagraphFormat.Bullet.Type = BulletType.None;
                            deletedCount++;
                        }
                    }
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                var textFrame = cell.TextFrame;
                                for (int i = 0; i < textFrame.Paragraphs.Count; i++)
                                {
                                    var paragraph = textFrame.Paragraphs[i];
                                    if (paragraph.ParagraphFormat.Bullet.Type != BulletType.None)
                                    {
                                        paragraph.ParagraphFormat.Bullet.Type = BulletType.None;
                                        deletedCount++;
                                    }
                                }
                            }
                        }
                    }
                }
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteBulletPointsFromGroupShape(groupShape);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状删除项目符号
        /// </summary>
        private int DeleteBulletPointsFromGroupShape(IGroupShape groupShape)
        {
            int deletedCount = 0;

            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    var textFrame = autoShape.TextFrame;
                    for (int j = 0; j < textFrame.Paragraphs.Count; j++)
                    {
                        var paragraph = textFrame.Paragraphs[j];
                        if (paragraph.ParagraphFormat.Bullet.Type != BulletType.None)
                        {
                            paragraph.ParagraphFormat.Bullet.Type = BulletType.None;
                            deletedCount++;
                        }
                    }
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                var textFrame = cell.TextFrame;
                                for (int k = 0; k < textFrame.Paragraphs.Count; k++)
                                {
                                    var paragraph = textFrame.Paragraphs[k];
                                    if (paragraph.ParagraphFormat.Bullet.Type != BulletType.None)
                                    {
                                        paragraph.ParagraphFormat.Bullet.Type = BulletType.None;
                                        deletedCount++;
                                    }
                                }
                            }
                        }
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    deletedCount += DeleteBulletPointsFromGroupShape(nestedGroupShape);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除特定文本
        /// </summary>
        private int DeleteSpecificTextFromSlide(IBaseSlide slide, List<string> textContent)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    var originalText = autoShape.TextFrame.Text;
                    var modifiedText = originalText;

                    foreach (var text in textContent)
                    {
                        if (!string.IsNullOrEmpty(text))
                        {
                            modifiedText = modifiedText.Replace(text, "", StringComparison.OrdinalIgnoreCase);
                        }
                    }

                    if (modifiedText != originalText)
                    {
                        autoShape.TextFrame.Text = modifiedText;
                        deletedCount++;
                    }
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                var originalText = cell.TextFrame.Text;
                                var modifiedText = originalText;

                                foreach (var text in textContent)
                                {
                                    if (!string.IsNullOrEmpty(text))
                                    {
                                        modifiedText = modifiedText.Replace(text, "", StringComparison.OrdinalIgnoreCase);
                                    }
                                }

                                if (modifiedText != originalText)
                                {
                                    cell.TextFrame.Text = modifiedText;
                                    deletedCount++;
                                }
                            }
                        }
                    }
                }
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteSpecificTextFromGroupShape(groupShape, textContent);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状删除特定文本
        /// </summary>
        private int DeleteSpecificTextFromGroupShape(IGroupShape groupShape, List<string> textContent)
        {
            int deletedCount = 0;

            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    var originalText = autoShape.TextFrame.Text;
                    var modifiedText = originalText;

                    foreach (var text in textContent)
                    {
                        if (!string.IsNullOrEmpty(text))
                        {
                            modifiedText = modifiedText.Replace(text, "", StringComparison.OrdinalIgnoreCase);
                        }
                    }

                    if (modifiedText != originalText)
                    {
                        autoShape.TextFrame.Text = modifiedText;
                        deletedCount++;
                    }
                }
                else if (shape is ITable table)
                {
                    for (int row = 0; row < table.Rows.Count; row++)
                    {
                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            var cell = table[col, row];
                            if (cell.TextFrame != null)
                            {
                                var originalText = cell.TextFrame.Text;
                                var modifiedText = originalText;

                                foreach (var text in textContent)
                                {
                                    if (!string.IsNullOrEmpty(text))
                                    {
                                        modifiedText = modifiedText.Replace(text, "", StringComparison.OrdinalIgnoreCase);
                                    }
                                }

                                if (modifiedText != originalText)
                                {
                                    cell.TextFrame.Text = modifiedText;
                                    deletedCount++;
                                }
                            }
                        }
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    deletedCount += DeleteSpecificTextFromGroupShape(nestedGroupShape, textContent);
                }
            }

            return deletedCount;
        }





        #endregion

        #region 图片删除处理

        /// <summary>
        /// 处理图片删除
        /// </summary>
        private async Task ProcessImageDeletionAsync(IPresentation presentation, ImageDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableImageDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                // ✅ 修复逻辑冲突：删除所有图片与删除指定图片互斥
                if (settings.DeleteAllImages)
                {
                    int allImagesDeleted = DeleteAllImagesInScope(slideScope);
                    deletedCount += allImagesDeleted;
                    if (allImagesDeleted > 0)
                    {
                        messages.Add($"删除所有图片: {allImagesDeleted} 张");
                    }
                }
                else if (settings.EnableSpecificImageDeletion && settings.SpecificImageNames?.Any() == true)
                {
                    // 只有在没有删除所有图片时才执行删除指定图片
                    int specificImagesDeleted = DeleteSpecificImagesInScope(slideScope, settings.SpecificImageNames);
                    deletedCount += specificImagesDeleted;
                    if (specificImagesDeleted > 0)
                    {
                        messages.Add($"删除指定图片: {specificImagesDeleted} 张");
                    }
                }

                // 删除最后一页图片（根据删除范围确定最后一页）
                if (settings.DeleteLastSlideImages)
                {
                    int lastSlideImagesDeleted = DeleteLastSlideImagesInScope(slideScope);
                    deletedCount += lastSlideImagesDeleted;
                    if (lastSlideImagesDeleted > 0)
                    {
                        messages.Add($"删除最后一页图片: {lastSlideImagesDeleted} 张");
                    }
                }

                // 删除背景图片
                if (settings.DeleteBackgroundImages)
                {
                    int backgroundImagesDeleted = DeleteBackgroundImagesInScope(slideScope);
                    deletedCount += backgroundImagesDeleted;
                    if (backgroundImagesDeleted > 0)
                    {
                        messages.Add($"删除背景图片: {backgroundImagesDeleted} 张");
                    }
                }

                // 删除指定范围图片（根据删除范围和页范围设置）
                if (settings.EnableSlideRangeImageDeletion)
                {
                    int rangeImagesDeleted = DeleteImageRangeInScope(slideScope, settings.ImageSlideRangeStart, settings.ImageSlideRangeEnd);
                    deletedCount += rangeImagesDeleted;
                    if (rangeImagesDeleted > 0)
                    {
                        messages.Add($"删除指定范围图片: {rangeImagesDeleted} 张");
                    }
                }

                // 按位置删除图片（完全包含在指定区域内的图片）
                if (settings.EnablePositionImageDeletion)
                {
                    int positionImagesDeleted = 0;
                    // 优先使用新的多区域功能
                    if (settings.PositionRegions?.Count > 0)
                    {
                        var enabledRegions = settings.PositionRegions.Where(r => r.IsEnabled).ToList();
                        if (enabledRegions.Count > 0)
                        {
                            // ✅ 修复：传递启用的区域而不是所有区域
                            positionImagesDeleted = DeleteImagesByMultipleRegionsInScope(slideScope, enabledRegions);
                            if (positionImagesDeleted > 0)
                            {
                                messages.Add($"按位置删除图片 (多区域): {positionImagesDeleted} 张，启用区域数: {enabledRegions.Count}");
                            }
                            else
                            {
                                messages.Add($"按位置删除图片: 未找到符合条件的图片，启用区域数: {enabledRegions.Count}");
                            }
                        }
                        else
                        {
                            messages.Add("按位置删除图片: 没有启用的区域");
                        }
                    }
                    else
                    {
                        // 向后兼容：使用单个区域设置
                        positionImagesDeleted = DeleteImagesByPositionPercentInScope(slideScope, settings.PositionXPercent, settings.PositionYPercent,
                            settings.PositionWidthPercent, settings.PositionHeightPercent);
                        if (positionImagesDeleted > 0)
                        {
                            messages.Add($"按位置删除图片 (单区域): {positionImagesDeleted} 张");
                        }
                        else
                        {
                            messages.Add("按位置删除图片: 未找到符合条件的图片");
                        }
                    }
                    deletedCount += positionImagesDeleted;
                }

                // 注意：删除包含指定图片的页面和删除无文字内容页面功能
                // 这些功能属于文档删除范畴，不在图片删除设置中

                // 不再需要总结信息，因为每个功能都有详细的信息
            });
        }

        /// <summary>
        /// 删除所有图片
        /// </summary>
        private int DeleteAllImages(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                var imagesToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IPictureFrame)
                    {
                        imagesToDelete.Add(shape);
                    }
                }

                foreach (var image in imagesToDelete)
                {
                    slide.Shapes.Remove(image);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除指定图片
        /// </summary>
        private int DeleteSpecificImages(IPresentation presentation, List<string> imageNames)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                var imagesToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IPictureFrame pictureFrame)
                    {
                        // 这里可以根据图片的属性或名称进行匹配
                        // Aspose.Slides 中图片可能没有直接的名称属性，可以使用其他方式匹配
                        if (imageNames.Any(name => shape.Name?.Contains(name, StringComparison.OrdinalIgnoreCase) == true))
                        {
                            imagesToDelete.Add(shape);
                        }
                    }
                }

                foreach (var image in imagesToDelete)
                {
                    slide.Shapes.Remove(image);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除最后一页图片
        /// </summary>
        private int DeleteLastSlideImages(IPresentation presentation)
        {
            int deletedCount = 0;

            if (presentation.Slides.Count > 0)
            {
                var lastSlide = presentation.Slides[presentation.Slides.Count - 1];
                var imagesToDelete = new List<IShape>();

                foreach (IShape shape in lastSlide.Shapes)
                {
                    if (shape is IPictureFrame)
                    {
                        imagesToDelete.Add(shape);
                    }
                }

                foreach (var image in imagesToDelete)
                {
                    lastSlide.Shapes.Remove(image);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除背景图片
        /// </summary>
        private int DeleteBackgroundImages(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                // 清除幻灯片背景图片
                if (slide.Background.FillFormat.FillType == FillType.Picture)
                {
                    slide.Background.Type = BackgroundType.OwnBackground;
                    slide.Background.FillFormat.FillType = FillType.Solid;
                    slide.Background.FillFormat.SolidFillColor.Color = Color.White;
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除指定范围的图片
        /// </summary>
        private int DeleteImageRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            startIndex = Math.Max(0, startIndex - 1); // 转换为0基索引
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            for (int i = startIndex; i <= endIndex && i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                var imagesToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IPictureFrame)
                    {
                        imagesToDelete.Add(shape);
                    }
                }

                foreach (var image in imagesToDelete)
                {
                    slide.Shapes.Remove(image);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 按位置百分比删除图片（只删除完全位于指定区域内的图片）
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        /// <param name="regionXPercent">删除区域左边距百分比（0-100）</param>
        /// <param name="regionYPercent">删除区域上边距百分比（0-100）</param>
        /// <param name="regionWidthPercent">删除区域宽度百分比（0-100）</param>
        /// <param name="regionHeightPercent">删除区域高度百分比（0-100）</param>
        /// <returns>删除的图片数量</returns>
        private int DeleteImagesByPositionPercent(IPresentation presentation, float regionXPercent, float regionYPercent, float regionWidthPercent, float regionHeightPercent)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                // 获取幻灯片尺寸
                float slideWidth = presentation.SlideSize.Size.Width;
                float slideHeight = presentation.SlideSize.Size.Height;

                // 将百分比转换为实际坐标
                float regionX = slideWidth * regionXPercent / 100f;
                float regionY = slideHeight * regionYPercent / 100f;
                float regionWidth = slideWidth * regionWidthPercent / 100f;
                float regionHeight = slideHeight * regionHeightPercent / 100f;

                var imagesToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IPictureFrame pictureFrame)
                    {
                        // 获取图片的位置和尺寸
                        float imageX = shape.X;
                        float imageY = shape.Y;
                        float imageWidth = shape.Width;
                        float imageHeight = shape.Height;

                        // 计算图片的右边界和下边界
                        float imageRight = imageX + imageWidth;
                        float imageBottom = imageY + imageHeight;

                        // 计算删除区域的右边界和下边界
                        float regionRight = regionX + regionWidth;
                        float regionBottom = regionY + regionHeight;

                        // 判断图片是否完全位于删除区域内
                        // 图片完全在区域内的条件：
                        // 1. 图片左边界 >= 区域左边界
                        // 2. 图片上边界 >= 区域上边界
                        // 3. 图片右边界 <= 区域右边界
                        // 4. 图片下边界 <= 区域下边界
                        bool isCompletelyInside = imageX >= regionX &&
                                                imageY >= regionY &&
                                                imageRight <= regionRight &&
                                                imageBottom <= regionBottom;

                        if (isCompletelyInside)
                        {
                            imagesToDelete.Add(shape);
                        }
                    }
                }

                // 删除符合条件的图片
                foreach (var image in imagesToDelete)
                {
                    slide.Shapes.Remove(image);
                    deletedCount++;
                }
            }

            return deletedCount;
        }



        /// <summary>
        /// 在指定范围内删除所有图片 - 支持普通幻灯片、母版、版式页
        /// </summary>
        private int DeleteAllImagesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteImagesFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteImagesFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteImagesFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除指定图片 - 支持普通幻灯片、母版、版式页
        /// </summary>
        private int DeleteSpecificImagesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope, List<string> imageNames)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteSpecificImagesFromSlide(slide, imageNames);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteSpecificImagesFromSlide(master, imageNames);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteSpecificImagesFromSlide(layout, imageNames);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除背景图片 - 支持普通幻灯片、母版、版式页
        /// </summary>
        private int DeleteBackgroundImagesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteBackgroundImageFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteBackgroundImageFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteBackgroundImageFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除最后一页图片 - 支持普通幻灯片、母版、版式页
        /// </summary>
        private int DeleteLastSlideImagesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 删除普通幻灯片的最后一页图片
            if (slideScope.NormalSlides.Count > 0)
            {
                var lastSlide = slideScope.NormalSlides[slideScope.NormalSlides.Count - 1];
                deletedCount += DeleteImagesFromSlide(lastSlide);
            }

            // 删除母版的最后一页图片
            if (slideScope.Masters.Count > 0)
            {
                var lastMaster = slideScope.Masters[slideScope.Masters.Count - 1];
                deletedCount += DeleteImagesFromSlide(lastMaster);
            }

            // 删除版式页的最后一页图片
            if (slideScope.LayoutSlides.Count > 0)
            {
                var lastLayout = slideScope.LayoutSlides[slideScope.LayoutSlides.Count - 1];
                deletedCount += DeleteImagesFromSlide(lastLayout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除页范围图片 - 支持普通幻灯片、母版、版式页
        /// </summary>
        private int DeleteImageRangeInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            // 处理普通幻灯片的页范围删除
            if (slideScope.NormalSlides.Count > 0)
            {
                // 转换为0基索引并限制在有效范围内
                int normalStartIndex = Math.Max(0, startIndex - 1);
                int normalEndIndex = Math.Min(slideScope.NormalSlides.Count - 1, endIndex - 1);

                for (int i = normalStartIndex; i <= normalEndIndex && i < slideScope.NormalSlides.Count; i++)
                {
                    deletedCount += DeleteImagesFromSlide(slideScope.NormalSlides[i]);
                }
            }

            // 处理母版的页范围删除
            if (slideScope.Masters.Count > 0)
            {
                // 转换为0基索引并限制在有效范围内
                int masterStartIndex = Math.Max(0, startIndex - 1);
                int masterEndIndex = Math.Min(slideScope.Masters.Count - 1, endIndex - 1);

                for (int i = masterStartIndex; i <= masterEndIndex && i < slideScope.Masters.Count; i++)
                {
                    deletedCount += DeleteImagesFromSlide(slideScope.Masters[i]);
                }
            }

            // 处理版式页的页范围删除
            if (slideScope.LayoutSlides.Count > 0)
            {
                // 转换为0基索引并限制在有效范围内
                int layoutStartIndex = Math.Max(0, startIndex - 1);
                int layoutEndIndex = Math.Min(slideScope.LayoutSlides.Count - 1, endIndex - 1);

                for (int i = layoutStartIndex; i <= layoutEndIndex && i < slideScope.LayoutSlides.Count; i++)
                {
                    deletedCount += DeleteImagesFromSlide(slideScope.LayoutSlides[i]);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内按位置删除图片（多区域） - 支持普通幻灯片、母版、版式页
        /// </summary>
        private int DeleteImagesByMultipleRegionsInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope, List<Models.PositionRegion> regions)
        {
            int deletedCount = 0;

            // 过滤出启用的区域
            var enabledRegions = regions.Where(r => r.IsEnabled).ToList();
            if (enabledRegions.Count == 0)
            {
                return 0; // 没有启用的区域，直接返回
            }

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteImagesByRegionsFromSlide(slide, enabledRegions);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteImagesByRegionsFromSlide(master, enabledRegions);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteImagesByRegionsFromSlide(layout, enabledRegions);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内按位置删除图片（单区域） - 支持普通幻灯片、母版、版式页
        /// </summary>
        private int DeleteImagesByPositionPercentInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope,
            float regionXPercent, float regionYPercent, float regionWidthPercent, float regionHeightPercent)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteImagesByPositionFromSlide(slide, regionXPercent, regionYPercent, regionWidthPercent, regionHeightPercent);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteImagesByPositionFromSlide(master, regionXPercent, regionYPercent, regionWidthPercent, regionHeightPercent);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteImagesByPositionFromSlide(layout, regionXPercent, regionYPercent, regionWidthPercent, regionHeightPercent);
            }

            return deletedCount;
        }

        #endregion

        #region 表格删除处理

        /// <summary>
        /// 处理表格删除
        /// </summary>
        private async Task ProcessTableDeletionAsync(IPresentation presentation, TableDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableTableDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                // 删除所有表格
                if (settings.DeleteAllTables)
                {
                    deletedCount += DeleteAllTablesInScope(slideScope);
                }

                // 删除最后一页表格（仅适用于普通幻灯片）
                if (settings.DeleteLastSlideTables && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteLastSlideTables(presentation);
                }

                // 删除指定范围表格（仅适用于普通幻灯片）
                if (settings.EnableSlideRangeTableDeletion && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteTableRange(presentation, settings.TableSlideRangeStart, settings.TableSlideRangeEnd);
                }

                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个表格");
                }
            });
        }

        /// <summary>
        /// 删除所有表格
        /// </summary>
        private int DeleteAllTables(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                var tablesToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is ITable)
                    {
                        tablesToDelete.Add(shape);
                    }
                }

                foreach (var table in tablesToDelete)
                {
                    slide.Shapes.Remove(table);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除最后一页表格
        /// </summary>
        private int DeleteLastSlideTables(IPresentation presentation)
        {
            int deletedCount = 0;

            if (presentation.Slides.Count > 0)
            {
                var lastSlide = presentation.Slides[presentation.Slides.Count - 1];
                var tablesToDelete = new List<IShape>();

                foreach (IShape shape in lastSlide.Shapes)
                {
                    if (shape is ITable)
                    {
                        tablesToDelete.Add(shape);
                    }
                }

                foreach (var table in tablesToDelete)
                {
                    lastSlide.Shapes.Remove(table);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除指定范围的表格
        /// </summary>
        private int DeleteTableRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            startIndex = Math.Max(0, startIndex - 1); // 转换为0基索引
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            for (int i = startIndex; i <= endIndex && i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                var tablesToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is ITable)
                    {
                        tablesToDelete.Add(shape);
                    }
                }

                foreach (var table in tablesToDelete)
                {
                    slide.Shapes.Remove(table);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除所有表格
        /// </summary>
        private int DeleteAllTablesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteTablesFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteTablesFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteTablesFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除所有表格
        /// </summary>
        private int DeleteTablesFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;
            var tablesToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is ITable)
                {
                    tablesToDelete.Add(shape);
                }
            }

            foreach (var table in tablesToDelete)
            {
                slide.Shapes.Remove(table);
                deletedCount++;
            }

            return deletedCount;
        }

        #endregion

        #region 图表删除处理

        /// <summary>
        /// 处理图表删除
        /// </summary>
        private async Task ProcessChartDeletionAsync(IPresentation presentation, ChartDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableChartDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                // 删除所有图表
                if (settings.DeleteAllCharts)
                {
                    deletedCount += DeleteAllChartsInScope(slideScope);
                }

                // 删除最后一页图表（仅适用于普通幻灯片）
                if (settings.DeleteLastSlideCharts && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteLastSlideCharts(presentation);
                }

                // 删除指定范围图表（仅适用于普通幻灯片）
                if (settings.EnableSlideRangeChartDeletion && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteChartRange(presentation, settings.ChartSlideRangeStart, settings.ChartSlideRangeEnd);
                }

                // 删除包含指定文本的图表
                if (settings.DeleteChartsWithText && settings.ChartKeywords?.Any() == true)
                {
                    deletedCount += DeleteChartsWithTextInScope(slideScope, settings.ChartKeywords);
                }

                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个图表");
                }
            });
        }

        /// <summary>
        /// 删除所有图表
        /// </summary>
        private int DeleteAllCharts(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                var chartsToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IChart)
                    {
                        chartsToDelete.Add(shape);
                    }
                }

                foreach (var chart in chartsToDelete)
                {
                    slide.Shapes.Remove(chart);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除最后一页图表
        /// </summary>
        private int DeleteLastSlideCharts(IPresentation presentation)
        {
            int deletedCount = 0;

            if (presentation.Slides.Count > 0)
            {
                var lastSlide = presentation.Slides[presentation.Slides.Count - 1];
                var chartsToDelete = new List<IShape>();

                foreach (IShape shape in lastSlide.Shapes)
                {
                    if (shape is IChart)
                    {
                        chartsToDelete.Add(shape);
                    }
                }

                foreach (var chart in chartsToDelete)
                {
                    lastSlide.Shapes.Remove(chart);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除指定范围的图表
        /// </summary>
        private int DeleteChartRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            startIndex = Math.Max(0, startIndex - 1); // 转换为0基索引
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            for (int i = startIndex; i <= endIndex && i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                var chartsToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IChart)
                    {
                        chartsToDelete.Add(shape);
                    }
                }

                foreach (var chart in chartsToDelete)
                {
                    slide.Shapes.Remove(chart);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除所有图表
        /// </summary>
        private int DeleteAllChartsInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteChartsFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteChartsFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteChartsFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除所有图表
        /// </summary>
        private int DeleteChartsFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;
            var chartsToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IChart)
                {
                    chartsToDelete.Add(shape);
                }
                else if (shape is IGroupShape groupShape)
                {
                    // 递归处理组合形状中的图表
                    deletedCount += DeleteChartsFromGroupShape(groupShape);
                }
            }

            foreach (var chart in chartsToDelete)
            {
                slide.Shapes.Remove(chart);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状中删除所有图表
        /// </summary>
        private int DeleteChartsFromGroupShape(IGroupShape groupShape)
        {
            int deletedCount = 0;
            var chartsToDelete = new List<IShape>();

            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IChart)
                {
                    chartsToDelete.Add(shape);
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    // 递归处理嵌套的组合形状
                    deletedCount += DeleteChartsFromGroupShape(nestedGroupShape);
                }
            }

            foreach (var chart in chartsToDelete)
            {
                groupShape.Shapes.Remove(chart);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除包含指定文本的图表
        /// </summary>
        private int DeleteChartsWithTextInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope, List<string> keywords)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteChartsWithTextFromSlide(slide, keywords);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteChartsWithTextFromSlide(master, keywords);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteChartsWithTextFromSlide(layout, keywords);
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除包含指定文本的图表
        /// </summary>
        private int DeleteChartsWithTextFromSlide(IBaseSlide slide, List<string> keywords)
        {
            int deletedCount = 0;
            var chartsToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IChart chart)
                {
                    // 检查图表是否包含指定的关键词
                    if (ChartContainsKeywords(chart, keywords))
                    {
                        chartsToDelete.Add(shape);
                    }
                }
                else if (shape is IGroupShape groupShape)
                {
                    // 递归处理组合形状中的图表
                    deletedCount += DeleteChartsWithTextFromGroupShape(groupShape, keywords);
                }
            }

            foreach (var chartShape in chartsToDelete)
            {
                slide.Shapes.Remove(chartShape);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状中删除包含指定文本的图表
        /// </summary>
        private int DeleteChartsWithTextFromGroupShape(IGroupShape groupShape, List<string> keywords)
        {
            int deletedCount = 0;
            var chartsToDelete = new List<IShape>();

            for (int i = 0; i < groupShape.Shapes.Count; i++)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IChart chart)
                {
                    // 检查图表是否包含指定的关键词
                    if (ChartContainsKeywords(chart, keywords))
                    {
                        chartsToDelete.Add(shape);
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    // 递归处理嵌套的组合形状
                    deletedCount += DeleteChartsWithTextFromGroupShape(nestedGroupShape, keywords);
                }
            }

            foreach (var chartShape in chartsToDelete)
            {
                groupShape.Shapes.Remove(chartShape);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 检查图表是否包含指定的关键词
        /// </summary>
        private bool ChartContainsKeywords(IChart chart, List<string> keywords)
        {
            try
            {
                // 检查图表标题
                if (chart.HasTitle && chart.ChartTitle != null)
                {
                    // 使用TextFrameForOverriding获取图表标题文本
                    var titleText = chart.ChartTitle.TextFrameForOverriding?.Text;
                    if (!string.IsNullOrEmpty(titleText) && ContainsAnyKeyword(titleText, keywords))
                        return true;
                }

                // 检查图表数据标签
                if (chart.ChartData?.Series != null)
                {
                    foreach (var series in chart.ChartData.Series)
                    {
                        // 检查系列名称 - 使用AsLiteralString或ToString方法
                        if (series.Name != null)
                        {
                            var seriesName = series.Name.AsLiteralString ?? series.Name.ToString();
                            if (!string.IsNullOrEmpty(seriesName) && ContainsAnyKeyword(seriesName, keywords))
                                return true;
                        }

                        // 检查数据点标签
                        if (series.DataPoints != null)
                        {
                            foreach (var dataPoint in series.DataPoints)
                            {
                                // 使用GetActualLabelText方法获取数据标签文本
                                if (dataPoint.Label != null)
                                {
                                    var labelText = dataPoint.Label.GetActualLabelText();
                                    if (!string.IsNullOrEmpty(labelText) && ContainsAnyKeyword(labelText, keywords))
                                        return true;
                                }
                            }
                        }
                    }
                }

                // 检查图表分类标签
                if (chart.ChartData?.Categories != null)
                {
                    foreach (var category in chart.ChartData.Categories)
                    {
                        // 使用Value属性或AsLiteral属性
                        if (category != null)
                        {
                            var categoryText = category.Value?.ToString() ?? category.AsLiteral?.ToString();
                            if (!string.IsNullOrEmpty(categoryText) && ContainsAnyKeyword(categoryText, keywords))
                                return true;
                        }
                    }
                }

                // 检查图表轴标题
                if (chart.Axes?.HorizontalAxis?.Title != null)
                {
                    // 使用TextFrameForOverriding获取轴标题文本
                    var hAxisTitle = chart.Axes.HorizontalAxis.Title.TextFrameForOverriding?.Text;
                    if (!string.IsNullOrEmpty(hAxisTitle) && ContainsAnyKeyword(hAxisTitle, keywords))
                        return true;
                }

                if (chart.Axes?.VerticalAxis?.Title != null)
                {
                    // 使用TextFrameForOverriding获取轴标题文本
                    var vAxisTitle = chart.Axes.VerticalAxis.Title.TextFrameForOverriding?.Text;
                    if (!string.IsNullOrEmpty(vAxisTitle) && ContainsAnyKeyword(vAxisTitle, keywords))
                        return true;
                }

                return false;
            }
            catch (Exception)
            {
                // 如果访问图表数据时出现异常，返回false
                return false;
            }
        }

        /// <summary>
        /// 检查文本是否包含任何关键词
        /// </summary>
        private bool ContainsAnyKeyword(string? text, List<string> keywords)
        {
            if (string.IsNullOrEmpty(text) || keywords == null || !keywords.Any())
                return false;

            foreach (var keyword in keywords)
            {
                if (!string.IsNullOrEmpty(keyword) &&
                    text.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    return true;
                }
            }

            return false;
        }

        #endregion

        #region 音频视频删除处理

        /// <summary>
        /// 处理音频视频删除
        /// </summary>
        private async Task ProcessMediaDeletionAsync(IPresentation presentation, MediaDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableMediaDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                // 删除音频
                if (settings.DeleteAllAudio)
                {
                    deletedCount += DeleteAllAudioInScope(slideScope);
                }

                if (settings.DeleteLastSlideAudio && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteLastSlideAudio(presentation);
                }

                if (settings.EnableSlideRangeAudioDeletion && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteAudioRange(presentation, settings.AudioSlideRangeStart, settings.AudioSlideRangeEnd);
                }

                // 删除视频
                if (settings.DeleteAllVideo)
                {
                    deletedCount += DeleteAllVideoInScope(slideScope);
                }

                if (settings.DeleteLastSlideVideo && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteLastSlideVideo(presentation);
                }

                if (settings.EnableSlideRangeVideoDeletion && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteVideoRange(presentation, settings.VideoSlideRangeStart, settings.VideoSlideRangeEnd);
                }

                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个音频视频元素");
                }
            });
        }

        /// <summary>
        /// 删除所有音频
        /// </summary>
        private int DeleteAllAudio(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                var audioToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAudioFrame)
                    {
                        audioToDelete.Add(shape);
                    }
                }

                foreach (var audio in audioToDelete)
                {
                    slide.Shapes.Remove(audio);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除最后一页音频
        /// </summary>
        private int DeleteLastSlideAudio(IPresentation presentation)
        {
            int deletedCount = 0;

            if (presentation.Slides.Count > 0)
            {
                var lastSlide = presentation.Slides[presentation.Slides.Count - 1];
                var audioToDelete = new List<IShape>();

                foreach (IShape shape in lastSlide.Shapes)
                {
                    if (shape is IAudioFrame)
                    {
                        audioToDelete.Add(shape);
                    }
                }

                foreach (var audio in audioToDelete)
                {
                    lastSlide.Shapes.Remove(audio);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除指定范围的音频
        /// </summary>
        private int DeleteAudioRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            startIndex = Math.Max(0, startIndex - 1);
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            for (int i = startIndex; i <= endIndex && i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                var audioToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAudioFrame)
                    {
                        audioToDelete.Add(shape);
                    }
                }

                foreach (var audio in audioToDelete)
                {
                    slide.Shapes.Remove(audio);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除所有视频
        /// </summary>
        private int DeleteAllVideo(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                var videoToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IVideoFrame)
                    {
                        videoToDelete.Add(shape);
                    }
                }

                foreach (var video in videoToDelete)
                {
                    slide.Shapes.Remove(video);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除最后一页视频
        /// </summary>
        private int DeleteLastSlideVideo(IPresentation presentation)
        {
            int deletedCount = 0;

            if (presentation.Slides.Count > 0)
            {
                var lastSlide = presentation.Slides[presentation.Slides.Count - 1];
                var videoToDelete = new List<IShape>();

                foreach (IShape shape in lastSlide.Shapes)
                {
                    if (shape is IVideoFrame)
                    {
                        videoToDelete.Add(shape);
                    }
                }

                foreach (var video in videoToDelete)
                {
                    lastSlide.Shapes.Remove(video);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除指定范围的视频
        /// </summary>
        private int DeleteVideoRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            startIndex = Math.Max(0, startIndex - 1);
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            for (int i = startIndex; i <= endIndex && i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                var videoToDelete = new List<IShape>();

                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IVideoFrame)
                    {
                        videoToDelete.Add(shape);
                    }
                }

                foreach (var video in videoToDelete)
                {
                    slide.Shapes.Remove(video);
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除所有音频
        /// </summary>
        private int DeleteAllAudioInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteAudioFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteAudioFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteAudioFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除所有视频
        /// </summary>
        private int DeleteAllVideoInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteVideoFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteVideoFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteVideoFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除所有音频
        /// </summary>
        private int DeleteAudioFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;
            var audioToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAudioFrame)
                {
                    audioToDelete.Add(shape);
                }
            }

            foreach (var audio in audioToDelete)
            {
                slide.Shapes.Remove(audio);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除所有视频
        /// </summary>
        private int DeleteVideoFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;
            var videoToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IVideoFrame)
                {
                    videoToDelete.Add(shape);
                }
            }

            foreach (var video in videoToDelete)
            {
                slide.Shapes.Remove(video);
                deletedCount++;
            }

            return deletedCount;
        }

        #endregion

        #region 联系方式删除处理

        /// <summary>
        /// 处理联系方式删除
        /// </summary>
        private async Task ProcessContactDeletionAsync(IPresentation presentation, ContactDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableContactDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                if (settings.DeletePhoneNumbers || settings.DeleteLandlineNumbers ||
                    settings.DeleteEmailAddresses || settings.DeleteWebsites || settings.DeleteHyperlinks)
                {
                    deletedCount += DeleteContactInformationInScope(slideScope, settings);
                }

                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个联系方式信息");
                }
            });
        }

        /// <summary>
        /// 删除联系方式信息
        /// </summary>
        private int DeleteContactInformation(IPresentation presentation, ContactDeletionSettings settings)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        var originalText = autoShape.TextFrame.Text;
                        var modifiedText = originalText;

                        // 删除电话号码
                        if (settings.DeletePhoneNumbers)
                        {
                            modifiedText = Regex.Replace(modifiedText, @"1[3-9]\d{9}", "", RegexOptions.IgnoreCase);
                        }

                        // 删除座机号码
                        if (settings.DeleteLandlineNumbers)
                        {
                            modifiedText = Regex.Replace(modifiedText, @"0\d{2,3}-?\d{7,8}", "", RegexOptions.IgnoreCase);
                        }

                        // 删除邮箱地址
                        if (settings.DeleteEmailAddresses)
                        {
                            // 使用更强大的邮箱正则表达式，支持各种邮箱格式
                            modifiedText = Regex.Replace(modifiedText, @"[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}", "", RegexOptions.IgnoreCase);
                        }

                        // 删除网址
                        if (settings.DeleteWebsites)
                        {
                            modifiedText = Regex.Replace(modifiedText, @"https?://[^\s]+", "", RegexOptions.IgnoreCase);
                            modifiedText = Regex.Replace(modifiedText, @"www\.[^\s]+", "", RegexOptions.IgnoreCase);
                        }

                        if (modifiedText != originalText)
                        {
                            autoShape.TextFrame.Text = modifiedText;
                            deletedCount++;
                        }
                    }

                    // 删除超链接
                    if (settings.DeleteHyperlinks && shape is IAutoShape autoShapeWithHyperlink && autoShapeWithHyperlink.TextFrame != null)
                    {
                        foreach (IParagraph paragraph in autoShapeWithHyperlink.TextFrame.Paragraphs)
                        {
                            foreach (IPortion portion in paragraph.Portions)
                            {
                                if (portion.PortionFormat.HyperlinkClick != null)
                                {
                                    portion.PortionFormat.HyperlinkClick = null;
                                    deletedCount++;
                                }
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除联系方式信息
        /// </summary>
        private int DeleteContactInformationInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope, ContactDeletionSettings settings)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteContactInformationFromSlide(slide, settings);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteContactInformationFromSlide(master, settings);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteContactInformationFromSlide(layout, settings);
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除联系方式信息
        /// </summary>
        private int DeleteContactInformationFromSlide(IBaseSlide slide, ContactDeletionSettings settings)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    var originalText = autoShape.TextFrame.Text;
                    var modifiedText = originalText;

                    // 删除电话号码
                    if (settings.DeletePhoneNumbers)
                    {
                        modifiedText = Regex.Replace(modifiedText, @"1[3-9]\d{9}", "", RegexOptions.IgnoreCase);
                    }

                    // 删除座机号码
                    if (settings.DeleteLandlineNumbers)
                    {
                        modifiedText = Regex.Replace(modifiedText, @"0\d{2,3}-?\d{7,8}", "", RegexOptions.IgnoreCase);
                    }

                    // 删除邮箱地址
                    if (settings.DeleteEmailAddresses)
                    {
                        // 使用更强大的邮箱正则表达式，支持各种邮箱格式
                        modifiedText = Regex.Replace(modifiedText, @"[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}", "", RegexOptions.IgnoreCase);
                    }

                    // 删除网址
                    if (settings.DeleteWebsites)
                    {
                        modifiedText = Regex.Replace(modifiedText, @"https?://[^\s]+", "", RegexOptions.IgnoreCase);
                        modifiedText = Regex.Replace(modifiedText, @"www\.[^\s]+", "", RegexOptions.IgnoreCase);
                    }

                    if (modifiedText != originalText)
                    {
                        autoShape.TextFrame.Text = modifiedText;
                        deletedCount++;
                    }
                }

                // 删除超链接
                if (settings.DeleteHyperlinks && shape is IAutoShape autoShapeWithHyperlink && autoShapeWithHyperlink.TextFrame != null)
                {
                    foreach (IParagraph paragraph in autoShapeWithHyperlink.TextFrame.Paragraphs)
                    {
                        foreach (IPortion portion in paragraph.Portions)
                        {
                            if (portion.PortionFormat.HyperlinkClick != null)
                            {
                                portion.PortionFormat.HyperlinkClick = null;
                                deletedCount++;
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        #endregion

        #region 动画删除处理

        /// <summary>
        /// 处理动画删除
        /// </summary>
        private async Task ProcessAnimationDeletionAsync(IPresentation presentation, AnimationDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableAnimationDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                // 删除动画
                if (settings.DeleteAllAnimations)
                {
                    deletedCount += DeleteAllAnimationsInScope(slideScope);
                }

                if (settings.DeleteLastSlideAnimations && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteLastSlideAnimations(presentation);
                }

                if (settings.EnableSlideRangeAnimationDeletion && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteAnimationRange(presentation, settings.AnimationSlideRangeStart, settings.AnimationSlideRangeEnd);
                }

                // 删除切换效果
                if (settings.DeleteAllTransitions)
                {
                    deletedCount += DeleteAllTransitionsInScope(slideScope);
                }

                if (settings.DeleteLastSlideTransitions && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteLastSlideTransitions(presentation);
                }

                if (settings.EnableSlideRangeTransitionDeletion && slideScope.NormalSlides.Count > 0)
                {
                    deletedCount += DeleteTransitionRange(presentation, settings.TransitionSlideRangeStart, settings.TransitionSlideRangeEnd);
                }

                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个动画和切换效果");
                }
            });
        }

        /// <summary>
        /// 删除所有动画
        /// </summary>
        private int DeleteAllAnimations(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                if (slide.Timeline?.MainSequence != null)
                {
                    var effectsCount = slide.Timeline.MainSequence.Count;
                    slide.Timeline.MainSequence.Clear();
                    deletedCount += effectsCount;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除最后一页动画
        /// </summary>
        private int DeleteLastSlideAnimations(IPresentation presentation)
        {
            int deletedCount = 0;

            if (presentation.Slides.Count > 0)
            {
                var lastSlide = presentation.Slides[presentation.Slides.Count - 1];
                if (lastSlide.Timeline?.MainSequence != null)
                {
                    var effectsCount = lastSlide.Timeline.MainSequence.Count;
                    lastSlide.Timeline.MainSequence.Clear();
                    deletedCount += effectsCount;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除指定范围的动画
        /// </summary>
        private int DeleteAnimationRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            startIndex = Math.Max(0, startIndex - 1);
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            for (int i = startIndex; i <= endIndex && i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                if (slide.Timeline?.MainSequence != null)
                {
                    var effectsCount = slide.Timeline.MainSequence.Count;
                    slide.Timeline.MainSequence.Clear();
                    deletedCount += effectsCount;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除所有切换效果
        /// </summary>
        private int DeleteAllTransitions(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                if (slide.SlideShowTransition.Type != TransitionType.None)
                {
                    slide.SlideShowTransition.Type = TransitionType.None;
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除最后一页切换效果
        /// </summary>
        private int DeleteLastSlideTransitions(IPresentation presentation)
        {
            int deletedCount = 0;

            if (presentation.Slides.Count > 0)
            {
                var lastSlide = presentation.Slides[presentation.Slides.Count - 1];
                if (lastSlide.SlideShowTransition.Type != TransitionType.None)
                {
                    lastSlide.SlideShowTransition.Type = TransitionType.None;
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除指定范围的切换效果
        /// </summary>
        private int DeleteTransitionRange(IPresentation presentation, int startIndex, int endIndex)
        {
            int deletedCount = 0;

            startIndex = Math.Max(0, startIndex - 1);
            endIndex = Math.Min(presentation.Slides.Count - 1, endIndex - 1);

            for (int i = startIndex; i <= endIndex && i < presentation.Slides.Count; i++)
            {
                var slide = presentation.Slides[i];
                if (slide.SlideShowTransition.Type != TransitionType.None)
                {
                    slide.SlideShowTransition.Type = TransitionType.None;
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除所有动画
        /// </summary>
        private int DeleteAllAnimationsInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteAnimationsFromSlide(slide);
            }

            // 注意：母版和版式页通常不包含动画效果，但为了完整性仍然处理
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteAnimationsFromSlide(master);
            }

            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteAnimationsFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除所有切换效果
        /// </summary>
        private int DeleteAllTransitionsInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteTransitionsFromSlide(slide);
            }

            // 注意：母版和版式页通常不包含切换效果，但为了完整性仍然处理
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteTransitionsFromSlide(master);
            }

            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteTransitionsFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除动画 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteAnimationsFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            try
            {
                // 只有普通幻灯片才有动画时间轴
                if (slide is ISlide normalSlide && normalSlide.Timeline?.MainSequence != null)
                {
                    var effectsCount = normalSlide.Timeline.MainSequence.Count;
                    if (effectsCount > 0)
                    {
                        normalSlide.Timeline.MainSequence.Clear();
                        deletedCount += effectsCount;
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但继续处理其他幻灯片
                System.Diagnostics.Debug.WriteLine($"删除幻灯片动画时发生错误: {ex.Message}");
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除切换效果 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteTransitionsFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            try
            {
                // 只有普通幻灯片才有切换效果
                if (slide is ISlide normalSlide && normalSlide.SlideShowTransition != null)
                {
                    if (normalSlide.SlideShowTransition.Type != TransitionType.None)
                    {
                        normalSlide.SlideShowTransition.Type = TransitionType.None;
                        // 清除切换效果的其他属性
                        normalSlide.SlideShowTransition.AdvanceOnClick = true;
                        normalSlide.SlideShowTransition.AdvanceAfterTime = 0;
                        deletedCount++;
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但继续处理其他幻灯片
                System.Diagnostics.Debug.WriteLine($"删除幻灯片切换效果时发生错误: {ex.Message}");
            }

            return deletedCount;
        }

        #endregion

        #region 备注删除处理

        /// <summary>
        /// 处理备注删除
        /// </summary>
        private async Task ProcessNotesDeletionAsync(IPresentation presentation, NotesDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableNotesDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                if (settings.DeleteSlideNotes)
                {
                    deletedCount += DeleteSlideNotesInScope(slideScope);
                }

                if (settings.ClearNotesContent)
                {
                    deletedCount += ClearNotesContentInScope(slideScope);
                }

                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个备注");
                }
            });
        }

        /// <summary>
        /// 删除幻灯片备注
        /// </summary>
        private int DeleteSlideNotes(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                if (slide.NotesSlideManager.NotesSlide != null)
                {
                    slide.NotesSlideManager.RemoveNotesSlide();
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 清除备注内容 - 符合Aspose.Slides API规范
        /// </summary>
        private int ClearNotesContent(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                var notesSlide = slide.NotesSlideManager.NotesSlide;
                if (notesSlide != null)
                {
                    // 优先使用NotesTextFrame.Text属性（符合API规范）
                    if (notesSlide.NotesTextFrame != null && !string.IsNullOrWhiteSpace(notesSlide.NotesTextFrame.Text))
                    {
                        notesSlide.NotesTextFrame.Text = "";
                        deletedCount++;
                    }
                    else
                    {
                        // 备用方案：遍历所有形状清除文本内容
                        foreach (IShape shape in notesSlide.Shapes)
                        {
                            if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                            {
                                if (!string.IsNullOrWhiteSpace(autoShape.TextFrame.Text))
                                {
                                    autoShape.TextFrame.Text = "";
                                    deletedCount++;
                                }
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除幻灯片备注
        /// </summary>
        private int DeleteSlideNotesInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 只处理普通幻灯片，因为母版和版式页通常没有备注
            foreach (var slide in slideScope.NormalSlides)
            {
                if (slide.NotesSlideManager.NotesSlide != null)
                {
                    slide.NotesSlideManager.RemoveNotesSlide();
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内清除备注内容 - 符合Aspose.Slides API规范
        /// </summary>
        private int ClearNotesContentInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 只处理普通幻灯片，因为母版和版式页通常没有备注
            foreach (var slide in slideScope.NormalSlides)
            {
                var notesSlide = slide.NotesSlideManager.NotesSlide;
                if (notesSlide != null)
                {
                    // 优先使用NotesTextFrame.Text属性（符合API规范）
                    if (notesSlide.NotesTextFrame != null && !string.IsNullOrWhiteSpace(notesSlide.NotesTextFrame.Text))
                    {
                        notesSlide.NotesTextFrame.Text = "";
                        deletedCount++;
                    }
                    else
                    {
                        // 备用方案：遍历所有形状清除文本内容
                        foreach (IShape shape in notesSlide.Shapes)
                        {
                            if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                            {
                                if (!string.IsNullOrWhiteSpace(autoShape.TextFrame.Text))
                                {
                                    autoShape.TextFrame.Text = "";
                                    deletedCount++;
                                }
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        #endregion

        #region 格式删除处理

        /// <summary>
        /// 处理格式删除
        /// </summary>
        private async Task ProcessFormatDeletionAsync(IPresentation presentation, FormatDeletionSettings settings, List<string> messages)
        {
            await Task.Run(() =>
            {
                // 检查总开关
                if (!settings.EnableFormatDeletion)
                {
                    return;
                }

                int deletedCount = 0;

                // 根据删除范围获取需要处理的幻灯片集合
                var slideScope = Forms.ContentDeletionForm.GetSlidesForDeletionScope(presentation, settings.DeletionScope);

                if (settings.DeleteFontFormatting)
                {
                    deletedCount += DeleteFontFormattingInScope(slideScope);
                }

                if (settings.DeleteParagraphFormatting)
                {
                    deletedCount += DeleteParagraphFormattingInScope(slideScope);
                }

                if (settings.DeleteTableFormatting)
                {
                    deletedCount += DeleteTableFormattingInScope(slideScope);
                }

                if (settings.DeleteListFormatting)
                {
                    deletedCount += DeleteListFormattingInScope(slideScope);
                }

                if (settings.DeleteShapeFormatting)
                {
                    deletedCount += DeleteShapeFormattingInScope(slideScope);
                }

                if (settings.DeleteImageFormatting)
                {
                    deletedCount += DeleteImageFormattingInScope(slideScope);
                }

                if (settings.DeleteChartFormatting)
                {
                    deletedCount += DeleteChartFormattingInScope(slideScope);
                }

                if (settings.DeleteBackgroundFormatting)
                {
                    deletedCount += DeleteBackgroundFormattingInScope(slideScope);
                }

                if (deletedCount > 0)
                {
                    messages.Add($"已删除 {deletedCount} 个格式设置");
                }
            });
        }

        /// <summary>
        /// 删除字体格式
        /// </summary>
        private int DeleteFontFormatting(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                        {
                            foreach (IPortion portion in paragraph.Portions)
                            {
                                var format = portion.PortionFormat;

                                // 重置字体格式为默认值
                                format.FontBold = NullableBool.False;
                                format.FontItalic = NullableBool.False;
                                format.FontUnderline = TextUnderlineType.None;
                                format.FontHeight = 18; // 默认字体大小
                                format.FillFormat.FillType = FillType.Solid;
                                format.FillFormat.SolidFillColor.Color = Color.Black;

                                deletedCount++;
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除段落格式
        /// </summary>
        private int DeleteParagraphFormatting(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                        {
                            var format = paragraph.ParagraphFormat;

                            // 重置段落格式为默认值
                            format.Alignment = TextAlignment.Left;
                            format.SpaceAfter = 0;
                            format.SpaceBefore = 0;
                            format.SpaceWithin = 1.0f;
                            format.Indent = 0;
                            format.MarginLeft = 0;
                            format.MarginRight = 0;

                            deletedCount++;
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除表格格式
        /// </summary>
        private int DeleteTableFormatting(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is ITable table)
                    {
                        // 重置表格样式
                        table.StylePreset = TableStylePreset.None;

                        // 重置所有单元格格式
                        for (int row = 0; row < table.Rows.Count; row++)
                        {
                            for (int col = 0; col < table.Columns.Count; col++)
                            {
                                var cell = table[col, row];
                                cell.CellFormat.FillFormat.FillType = FillType.NoFill;
                                cell.CellFormat.BorderTop.FillFormat.FillType = FillType.Solid;
                                cell.CellFormat.BorderTop.FillFormat.SolidFillColor.Color = Color.Black;
                                cell.CellFormat.BorderTop.Width = 1;
                                cell.CellFormat.BorderBottom.FillFormat.FillType = FillType.Solid;
                                cell.CellFormat.BorderBottom.FillFormat.SolidFillColor.Color = Color.Black;
                                cell.CellFormat.BorderBottom.Width = 1;
                                cell.CellFormat.BorderLeft.FillFormat.FillType = FillType.Solid;
                                cell.CellFormat.BorderLeft.FillFormat.SolidFillColor.Color = Color.Black;
                                cell.CellFormat.BorderLeft.Width = 1;
                                cell.CellFormat.BorderRight.FillFormat.FillType = FillType.Solid;
                                cell.CellFormat.BorderRight.FillFormat.SolidFillColor.Color = Color.Black;
                                cell.CellFormat.BorderRight.Width = 1;
                            }
                        }

                        deletedCount++;
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除列表格式
        /// </summary>
        private int DeleteListFormatting(IPresentation presentation)
        {
            int deletedCount = 0;

            foreach (ISlide slide in presentation.Slides)
            {
                foreach (IShape shape in slide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                        {
                            var format = paragraph.ParagraphFormat;

                            // 删除项目符号和编号
                            format.Bullet.Type = BulletType.None;

                            deletedCount++;
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除字体格式
        /// </summary>
        private int DeleteFontFormattingInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteFontFormattingFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteFontFormattingFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteFontFormattingFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除段落格式
        /// </summary>
        private int DeleteParagraphFormattingInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteParagraphFormattingFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteParagraphFormattingFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteParagraphFormattingFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除表格格式
        /// </summary>
        private int DeleteTableFormattingInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteTableFormattingFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteTableFormattingFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteTableFormattingFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除列表格式
        /// </summary>
        private int DeleteListFormattingInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteListFormattingFromSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteListFormattingFromSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteListFormattingFromSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除字体格式
        /// </summary>
        private int DeleteFontFormattingFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                    {
                        foreach (IPortion portion in paragraph.Portions)
                        {
                            var format = portion.PortionFormat;

                            // 重置字体格式为默认值
                            format.FontBold = NullableBool.False;
                            format.FontItalic = NullableBool.False;
                            format.FontUnderline = TextUnderlineType.None;
                            format.FontHeight = 18; // 默认字体大小
                            format.FillFormat.FillType = FillType.Solid;
                            format.FillFormat.SolidFillColor.Color = Color.Black;

                            deletedCount++;
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除段落格式
        /// </summary>
        private int DeleteParagraphFormattingFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                    {
                        var format = paragraph.ParagraphFormat;

                        // 重置段落格式为默认值
                        format.Alignment = TextAlignment.Left;
                        format.SpaceAfter = 0;
                        format.SpaceBefore = 0;
                        format.SpaceWithin = 1.0f;
                        format.Indent = 0;
                        format.MarginLeft = 0;
                        format.MarginRight = 0;

                        deletedCount++;
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除表格格式
        /// </summary>
        private int DeleteTableFormattingFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is ITable table)
                {
                    // 重置表格样式
                    table.StylePreset = TableStylePreset.None;
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除列表格式
        /// </summary>
        private int DeleteListFormattingFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                    {
                        var format = paragraph.ParagraphFormat;

                        // 重置列表格式
                        format.Bullet.Type = BulletType.None;
                        deletedCount++;
                    }
                }
            }

            return deletedCount;
        }

        #endregion

        #region 辅助方法 - 单个幻灯片操作

        /// <summary>
        /// 从单个幻灯片删除所有图片
        /// </summary>
        private int DeleteImagesFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;
            var imagesToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IPictureFrame)
                {
                    imagesToDelete.Add(shape);
                }
                // 处理组合形状中的图片
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteImagesFromGroupShape(groupShape);
                }
            }

            foreach (var image in imagesToDelete)
            {
                slide.Shapes.Remove(image);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状删除所有图片
        /// </summary>
        private int DeleteImagesFromGroupShape(IGroupShape groupShape)
        {
            int deletedCount = 0;

            // 从后往前遍历，避免删除时索引变化的问题
            for (int i = groupShape.Shapes.Count - 1; i >= 0; i--)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IPictureFrame)
                {
                    groupShape.Shapes.RemoveAt(i);
                    deletedCount++;
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    deletedCount += DeleteImagesFromGroupShape(nestedGroupShape);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除指定图片
        /// </summary>
        private int DeleteSpecificImagesFromSlide(IBaseSlide slide, List<string> imageNames)
        {
            int deletedCount = 0;
            var imagesToDelete = new List<IShape>();

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IPictureFrame pictureFrame)
                {
                    // 根据图片的属性或名称进行匹配
                    if (imageNames.Any(name => shape.Name?.Contains(name, StringComparison.OrdinalIgnoreCase) == true))
                    {
                        imagesToDelete.Add(shape);
                    }
                }
                // 处理组合形状中的指定图片
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteSpecificImagesFromGroupShape(groupShape, imageNames);
                }
            }

            foreach (var image in imagesToDelete)
            {
                slide.Shapes.Remove(image);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状删除指定图片
        /// </summary>
        private int DeleteSpecificImagesFromGroupShape(IGroupShape groupShape, List<string> imageNames)
        {
            int deletedCount = 0;

            // 从后往前遍历，避免删除时索引变化的问题
            for (int i = groupShape.Shapes.Count - 1; i >= 0; i--)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IPictureFrame pictureFrame)
                {
                    // 根据图片的属性或名称进行匹配
                    if (imageNames.Any(name => shape.Name?.Contains(name, StringComparison.OrdinalIgnoreCase) == true))
                    {
                        groupShape.Shapes.RemoveAt(i);
                        deletedCount++;
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    deletedCount += DeleteSpecificImagesFromGroupShape(nestedGroupShape, imageNames);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片删除背景图片
        /// </summary>
        private int DeleteBackgroundImageFromSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            // 清除幻灯片背景图片
            if (slide.Background.FillFormat.FillType == FillType.Picture)
            {
                slide.Background.Type = BackgroundType.OwnBackground;
                slide.Background.FillFormat.FillType = FillType.Solid;
                slide.Background.FillFormat.SolidFillColor.Color = Color.White;
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片按多个区域删除图片
        /// </summary>
        private int DeleteImagesByRegionsFromSlide(IBaseSlide slide, List<Models.PositionRegion> enabledRegions)
        {
            int deletedCount = 0;
            var imagesToDelete = new List<IShape>();

            // 获取幻灯片尺寸 - 需要从演示文稿获取
            float slideWidth = 720f; // 默认值，实际应该从presentation获取
            float slideHeight = 540f; // 默认值，实际应该从presentation获取

            // 如果是普通幻灯片，可以获取演示文稿尺寸
            if (slide is ISlide normalSlide)
            {
                slideWidth = normalSlide.Presentation.SlideSize.Size.Width;
                slideHeight = normalSlide.Presentation.SlideSize.Size.Height;
            }

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IPictureFrame pictureFrame)
                {
                    // 获取图片的位置和尺寸
                    float imageX = shape.X;
                    float imageY = shape.Y;
                    float imageWidth = shape.Width;
                    float imageHeight = shape.Height;

                    // 计算图片的右边界和下边界
                    float imageRight = imageX + imageWidth;
                    float imageBottom = imageY + imageHeight;

                    // 检查图片是否完全位于任一启用区域内
                    bool shouldDelete = false;
                    foreach (var region in enabledRegions)
                    {
                        // 将百分比转换为实际坐标
                        float regionX = slideWidth * region.XPercent / 100f;
                        float regionY = slideHeight * region.YPercent / 100f;
                        float regionWidth = slideWidth * region.WidthPercent / 100f;
                        float regionHeight = slideHeight * region.HeightPercent / 100f;

                        // 计算删除区域的右边界和下边界
                        float regionRight = regionX + regionWidth;
                        float regionBottom = regionY + regionHeight;

                        // 判断图片是否完全位于当前区域内
                        bool isCompletelyInside = imageX >= regionX &&
                                                imageY >= regionY &&
                                                imageRight <= regionRight &&
                                                imageBottom <= regionBottom;

                        if (isCompletelyInside)
                        {
                            shouldDelete = true;
                            break; // 找到一个匹配的区域就足够了
                        }
                    }

                    if (shouldDelete)
                    {
                        imagesToDelete.Add(shape);
                    }
                }
                // 处理组合形状中的图片
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteImagesByRegionsFromGroupShape(groupShape, enabledRegions, slideWidth, slideHeight);
                }
            }

            // 删除符合条件的图片
            foreach (var image in imagesToDelete)
            {
                slide.Shapes.Remove(image);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状按多个区域删除图片 - 修复组合图形中图片位置计算问题
        /// </summary>
        private int DeleteImagesByRegionsFromGroupShape(IGroupShape groupShape, List<Models.PositionRegion> enabledRegions, float slideWidth, float slideHeight)
        {
            int deletedCount = 0;

            // 从后往前遍历，避免删除时索引变化的问题
            for (int i = groupShape.Shapes.Count - 1; i >= 0; i--)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IPictureFrame pictureFrame)
                {
                    // 获取图片在组合图形中的相对位置和尺寸
                    float relativeImageX = shape.X;
                    float relativeImageY = shape.Y;
                    float imageWidth = shape.Width;
                    float imageHeight = shape.Height;

                    // 计算图片在幻灯片中的绝对位置
                    // 组合图形的位置 + 图片在组合图形中的相对位置
                    float absoluteImageX = groupShape.X + relativeImageX;
                    float absoluteImageY = groupShape.Y + relativeImageY;

                    // 计算图片的右边界和下边界（绝对坐标）
                    float absoluteImageRight = absoluteImageX + imageWidth;
                    float absoluteImageBottom = absoluteImageY + imageHeight;

                    // 检查图片是否完全位于任一启用区域内
                    bool shouldDelete = false;
                    foreach (var region in enabledRegions)
                    {
                        // 将百分比转换为实际坐标
                        float regionX = slideWidth * region.XPercent / 100f;
                        float regionY = slideHeight * region.YPercent / 100f;
                        float regionWidth = slideWidth * region.WidthPercent / 100f;
                        float regionHeight = slideHeight * region.HeightPercent / 100f;

                        // 计算删除区域的右边界和下边界
                        float regionRight = regionX + regionWidth;
                        float regionBottom = regionY + regionHeight;

                        // 判断图片是否完全位于当前区域内（使用绝对坐标）
                        bool isCompletelyInside = absoluteImageX >= regionX &&
                                                absoluteImageY >= regionY &&
                                                absoluteImageRight <= regionRight &&
                                                absoluteImageBottom <= regionBottom;

                        if (isCompletelyInside)
                        {
                            shouldDelete = true;
                            break; // 找到一个匹配的区域就足够了
                        }
                    }

                    if (shouldDelete)
                    {
                        groupShape.Shapes.RemoveAt(i);
                        deletedCount++;
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    // 递归处理嵌套的组合图形，传递累积的偏移量
                    deletedCount += DeleteImagesByRegionsFromNestedGroupShape(nestedGroupShape, enabledRegions, slideWidth, slideHeight, groupShape.X, groupShape.Y);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从嵌套组合形状按多个区域删除图片 - 处理多层嵌套的组合图形
        /// </summary>
        private int DeleteImagesByRegionsFromNestedGroupShape(IGroupShape groupShape, List<Models.PositionRegion> enabledRegions,
            float slideWidth, float slideHeight, float parentOffsetX, float parentOffsetY)
        {
            int deletedCount = 0;

            // 从后往前遍历，避免删除时索引变化的问题
            for (int i = groupShape.Shapes.Count - 1; i >= 0; i--)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IPictureFrame pictureFrame)
                {
                    // 获取图片在当前组合图形中的相对位置和尺寸
                    float relativeImageX = shape.X;
                    float relativeImageY = shape.Y;
                    float imageWidth = shape.Width;
                    float imageHeight = shape.Height;

                    // 计算图片在幻灯片中的绝对位置
                    // 父级偏移量 + 当前组合图形的位置 + 图片在当前组合图形中的相对位置
                    float absoluteImageX = parentOffsetX + groupShape.X + relativeImageX;
                    float absoluteImageY = parentOffsetY + groupShape.Y + relativeImageY;

                    // 计算图片的右边界和下边界（绝对坐标）
                    float absoluteImageRight = absoluteImageX + imageWidth;
                    float absoluteImageBottom = absoluteImageY + imageHeight;

                    // 检查图片是否完全位于任一启用区域内
                    bool shouldDelete = false;
                    foreach (var region in enabledRegions)
                    {
                        // 将百分比转换为实际坐标
                        float regionX = slideWidth * region.XPercent / 100f;
                        float regionY = slideHeight * region.YPercent / 100f;
                        float regionWidth = slideWidth * region.WidthPercent / 100f;
                        float regionHeight = slideHeight * region.HeightPercent / 100f;

                        // 计算删除区域的右边界和下边界
                        float regionRight = regionX + regionWidth;
                        float regionBottom = regionY + regionHeight;

                        // 判断图片是否完全位于当前区域内（使用绝对坐标）
                        bool isCompletelyInside = absoluteImageX >= regionX &&
                                                absoluteImageY >= regionY &&
                                                absoluteImageRight <= regionRight &&
                                                absoluteImageBottom <= regionBottom;

                        if (isCompletelyInside)
                        {
                            shouldDelete = true;
                            break; // 找到一个匹配的区域就足够了
                        }
                    }

                    if (shouldDelete)
                    {
                        groupShape.Shapes.RemoveAt(i);
                        deletedCount++;
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    // 继续递归处理更深层的嵌套组合图形
                    float newOffsetX = parentOffsetX + groupShape.X;
                    float newOffsetY = parentOffsetY + groupShape.Y;
                    deletedCount += DeleteImagesByRegionsFromNestedGroupShape(nestedGroupShape, enabledRegions, slideWidth, slideHeight, newOffsetX, newOffsetY);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从单个幻灯片按位置删除图片（单区域）
        /// </summary>
        private int DeleteImagesByPositionFromSlide(IBaseSlide slide, float regionXPercent, float regionYPercent,
            float regionWidthPercent, float regionHeightPercent)
        {
            int deletedCount = 0;
            var imagesToDelete = new List<IShape>();

            // 获取幻灯片尺寸 - 需要从演示文稿获取
            float slideWidth = 720f; // 默认值，实际应该从presentation获取
            float slideHeight = 540f; // 默认值，实际应该从presentation获取

            // 如果是普通幻灯片，可以获取演示文稿尺寸
            if (slide is ISlide normalSlide)
            {
                slideWidth = normalSlide.Presentation.SlideSize.Size.Width;
                slideHeight = normalSlide.Presentation.SlideSize.Size.Height;
            }

            // 将百分比转换为实际坐标
            float regionX = slideWidth * regionXPercent / 100f;
            float regionY = slideHeight * regionYPercent / 100f;
            float regionWidth = slideWidth * regionWidthPercent / 100f;
            float regionHeight = slideHeight * regionHeightPercent / 100f;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IPictureFrame pictureFrame)
                {
                    // 获取图片的位置和尺寸
                    float imageX = shape.X;
                    float imageY = shape.Y;
                    float imageWidth = shape.Width;
                    float imageHeight = shape.Height;

                    // 计算图片的右边界和下边界
                    float imageRight = imageX + imageWidth;
                    float imageBottom = imageY + imageHeight;

                    // 计算删除区域的右边界和下边界
                    float regionRight = regionX + regionWidth;
                    float regionBottom = regionY + regionHeight;

                    // 判断图片是否完全位于删除区域内
                    bool isCompletelyInside = imageX >= regionX &&
                                            imageY >= regionY &&
                                            imageRight <= regionRight &&
                                            imageBottom <= regionBottom;

                    if (isCompletelyInside)
                    {
                        imagesToDelete.Add(shape);
                    }
                }
                // 处理组合形状中的图片
                else if (shape is IGroupShape groupShape)
                {
                    deletedCount += DeleteImagesByPositionFromGroupShape(groupShape, regionX, regionY, regionWidth, regionHeight);
                }
            }

            // 删除符合条件的图片
            foreach (var image in imagesToDelete)
            {
                slide.Shapes.Remove(image);
                deletedCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// 从组合形状按位置删除图片（单区域） - 修复组合图形中图片位置计算问题
        /// </summary>
        private int DeleteImagesByPositionFromGroupShape(IGroupShape groupShape, float regionX, float regionY, float regionWidth, float regionHeight)
        {
            int deletedCount = 0;

            // 从后往前遍历，避免删除时索引变化的问题
            for (int i = groupShape.Shapes.Count - 1; i >= 0; i--)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IPictureFrame pictureFrame)
                {
                    // 获取图片在组合图形中的相对位置和尺寸
                    float relativeImageX = shape.X;
                    float relativeImageY = shape.Y;
                    float imageWidth = shape.Width;
                    float imageHeight = shape.Height;

                    // 计算图片在幻灯片中的绝对位置
                    // 组合图形的位置 + 图片在组合图形中的相对位置
                    float absoluteImageX = groupShape.X + relativeImageX;
                    float absoluteImageY = groupShape.Y + relativeImageY;

                    // 计算图片的右边界和下边界（绝对坐标）
                    float absoluteImageRight = absoluteImageX + imageWidth;
                    float absoluteImageBottom = absoluteImageY + imageHeight;

                    // 计算删除区域的右边界和下边界
                    float regionRight = regionX + regionWidth;
                    float regionBottom = regionY + regionHeight;

                    // 判断图片是否完全位于删除区域内（使用绝对坐标）
                    bool isCompletelyInside = absoluteImageX >= regionX &&
                                            absoluteImageY >= regionY &&
                                            absoluteImageRight <= regionRight &&
                                            absoluteImageBottom <= regionBottom;

                    if (isCompletelyInside)
                    {
                        groupShape.Shapes.RemoveAt(i);
                        deletedCount++;
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    // 递归处理嵌套的组合图形，传递累积的偏移量
                    deletedCount += DeleteImagesByPositionFromNestedGroupShape(nestedGroupShape, regionX, regionY, regionWidth, regionHeight, groupShape.X, groupShape.Y);
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 从嵌套组合形状按位置删除图片（单区域） - 处理多层嵌套的组合图形
        /// </summary>
        private int DeleteImagesByPositionFromNestedGroupShape(IGroupShape groupShape, float regionX, float regionY,
            float regionWidth, float regionHeight, float parentOffsetX, float parentOffsetY)
        {
            int deletedCount = 0;

            // 从后往前遍历，避免删除时索引变化的问题
            for (int i = groupShape.Shapes.Count - 1; i >= 0; i--)
            {
                var shape = groupShape.Shapes[i];

                if (shape is IPictureFrame pictureFrame)
                {
                    // 获取图片在当前组合图形中的相对位置和尺寸
                    float relativeImageX = shape.X;
                    float relativeImageY = shape.Y;
                    float imageWidth = shape.Width;
                    float imageHeight = shape.Height;

                    // 计算图片在幻灯片中的绝对位置
                    // 父级偏移量 + 当前组合图形的位置 + 图片在当前组合图形中的相对位置
                    float absoluteImageX = parentOffsetX + groupShape.X + relativeImageX;
                    float absoluteImageY = parentOffsetY + groupShape.Y + relativeImageY;

                    // 计算图片的右边界和下边界（绝对坐标）
                    float absoluteImageRight = absoluteImageX + imageWidth;
                    float absoluteImageBottom = absoluteImageY + imageHeight;

                    // 计算删除区域的右边界和下边界
                    float regionRight = regionX + regionWidth;
                    float regionBottom = regionY + regionHeight;

                    // 判断图片是否完全位于删除区域内（使用绝对坐标）
                    bool isCompletelyInside = absoluteImageX >= regionX &&
                                            absoluteImageY >= regionY &&
                                            absoluteImageRight <= regionRight &&
                                            absoluteImageBottom <= regionBottom;

                    if (isCompletelyInside)
                    {
                        groupShape.Shapes.RemoveAt(i);
                        deletedCount++;
                    }
                }
                else if (shape is IGroupShape nestedGroupShape)
                {
                    // 继续递归处理更深层的嵌套组合图形
                    float newOffsetX = parentOffsetX + groupShape.X;
                    float newOffsetY = parentOffsetY + groupShape.Y;
                    deletedCount += DeleteImagesByPositionFromNestedGroupShape(nestedGroupShape, regionX, regionY, regionWidth, regionHeight, newOffsetX, newOffsetY);
                }
            }

            return deletedCount;
        }

        #endregion

        #region 格式删除扩展方法

        /// <summary>
        /// 在指定范围内删除形状格式 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteShapeFormattingInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteShapeFormattingInSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteShapeFormattingInSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteShapeFormattingInSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除单个幻灯片中的形状格式 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteShapeFormattingInSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                // 跳过表格、图表和图片，它们有专门的处理方法
                if (shape is ITable || shape is IChart || shape is IPictureFrame)
                    continue;

                // 重置填充格式
                if (shape.FillFormat != null)
                {
                    shape.FillFormat.FillType = FillType.NoFill;
                    deletedCount++;
                }

                // 重置线条格式
                if (shape.LineFormat != null)
                {
                    shape.LineFormat.FillFormat.FillType = FillType.NoFill;
                    shape.LineFormat.Width = 0.75; // 默认线宽
                    shape.LineFormat.DashStyle = LineDashStyle.Solid;
                    shape.LineFormat.Style = LineStyle.Single;
                    deletedCount++;
                }

                // 重置效果格式
                if (shape.EffectFormat != null && !shape.EffectFormat.IsNoEffects)
                {
                    shape.EffectFormat.DisableBlurEffect();
                    shape.EffectFormat.DisableFillOverlayEffect();
                    shape.EffectFormat.DisableGlowEffect();
                    shape.EffectFormat.DisableInnerShadowEffect();
                    shape.EffectFormat.DisableOuterShadowEffect();
                    shape.EffectFormat.DisablePresetShadowEffect();
                    shape.EffectFormat.DisableReflectionEffect();
                    shape.EffectFormat.DisableSoftEdgeEffect();
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除图片格式 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteImageFormattingInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteImageFormattingInSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteImageFormattingInSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteImageFormattingInSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除单个幻灯片中的图片格式 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteImageFormattingInSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IPictureFrame pictureFrame)
                {
                    // 重置图片填充格式
                    if (pictureFrame.FillFormat != null)
                    {
                        pictureFrame.FillFormat.FillType = FillType.Picture;
                        deletedCount++;
                    }

                    // 重置图片线条格式
                    if (pictureFrame.LineFormat != null)
                    {
                        pictureFrame.LineFormat.FillFormat.FillType = FillType.NoFill;
                        pictureFrame.LineFormat.Width = 0;
                        deletedCount++;
                    }

                    // 重置图片效果格式
                    if (pictureFrame.EffectFormat != null && !pictureFrame.EffectFormat.IsNoEffects)
                    {
                        pictureFrame.EffectFormat.DisableBlurEffect();
                        pictureFrame.EffectFormat.DisableFillOverlayEffect();
                        pictureFrame.EffectFormat.DisableGlowEffect();
                        pictureFrame.EffectFormat.DisableInnerShadowEffect();
                        pictureFrame.EffectFormat.DisableOuterShadowEffect();
                        pictureFrame.EffectFormat.DisablePresetShadowEffect();
                        pictureFrame.EffectFormat.DisableReflectionEffect();
                        pictureFrame.EffectFormat.DisableSoftEdgeEffect();
                        deletedCount++;
                    }

                    // 重置图片裁剪和调整
                    if (pictureFrame.PictureFormat != null)
                    {
                        var picFormat = pictureFrame.PictureFormat;
                        picFormat.CropLeft = 0;
                        picFormat.CropTop = 0;
                        picFormat.CropRight = 0;
                        picFormat.CropBottom = 0;
                        deletedCount++;
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除图表格式 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteChartFormattingInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteChartFormattingInSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteChartFormattingInSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteChartFormattingInSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除单个幻灯片中的图表格式 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteChartFormattingInSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            foreach (IShape shape in slide.Shapes)
            {
                if (shape is IChart chart)
                {
                    // 重置图表填充格式
                    if (chart.FillFormat != null)
                    {
                        chart.FillFormat.FillType = FillType.NoFill;
                        deletedCount++;
                    }

                    // 重置图表线条格式
                    if (chart.LineFormat != null)
                    {
                        chart.LineFormat.FillFormat.FillType = FillType.NoFill;
                        chart.LineFormat.Width = 0.75;
                        deletedCount++;
                    }

                    // 重置图表效果格式
                    if (chart.EffectFormat != null && !chart.EffectFormat.IsNoEffects)
                    {
                        chart.EffectFormat.DisableBlurEffect();
                        chart.EffectFormat.DisableFillOverlayEffect();
                        chart.EffectFormat.DisableGlowEffect();
                        chart.EffectFormat.DisableInnerShadowEffect();
                        chart.EffectFormat.DisableOuterShadowEffect();
                        chart.EffectFormat.DisablePresetShadowEffect();
                        chart.EffectFormat.DisableReflectionEffect();
                        chart.EffectFormat.DisableSoftEdgeEffect();
                        deletedCount++;
                    }

                    // 重置图表区域格式
                    if (chart.ChartData != null && chart.PlotArea != null)
                    {
                        // 重置绘图区格式
                        if (chart.PlotArea.Format != null)
                        {
                            if (chart.PlotArea.Format.Fill != null)
                            {
                                chart.PlotArea.Format.Fill.FillType = FillType.NoFill;
                                deletedCount++;
                            }
                            if (chart.PlotArea.Format.Line != null)
                            {
                                chart.PlotArea.Format.Line.FillFormat.FillType = FillType.NoFill;
                                deletedCount++;
                            }
                        }
                    }
                }
            }

            return deletedCount;
        }

        /// <summary>
        /// 在指定范围内删除背景格式 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteBackgroundFormattingInScope(Forms.ContentDeletionForm.SlideScopeResult slideScope)
        {
            int deletedCount = 0;

            // 处理普通幻灯片
            foreach (var slide in slideScope.NormalSlides)
            {
                deletedCount += DeleteBackgroundFormattingInSlide(slide);
            }

            // 处理母版
            foreach (var master in slideScope.Masters)
            {
                deletedCount += DeleteBackgroundFormattingInSlide(master);
            }

            // 处理版式页
            foreach (var layout in slideScope.LayoutSlides)
            {
                deletedCount += DeleteBackgroundFormattingInSlide(layout);
            }

            return deletedCount;
        }

        /// <summary>
        /// 删除单个幻灯片中的背景格式 - 符合Aspose.Slides API规范
        /// </summary>
        private int DeleteBackgroundFormattingInSlide(IBaseSlide slide)
        {
            int deletedCount = 0;

            // 重置背景格式 - 符合Aspose.Slides API规范
            if (slide.Background != null)
            {
                // 设置背景类型为主题背景（相当于继承母版）
                slide.Background.Type = BackgroundType.Themed;

                // 如果有自定义背景格式，重置它
                if (slide.Background.FillFormat != null)
                {
                    slide.Background.FillFormat.FillType = FillType.NoFill;
                    deletedCount++;
                }

                // 重置背景效果格式
                if (slide.Background.EffectFormat != null && !slide.Background.EffectFormat.IsNoEffects)
                {
                    slide.Background.EffectFormat.DisableBlurEffect();
                    slide.Background.EffectFormat.DisableFillOverlayEffect();
                    slide.Background.EffectFormat.DisableGlowEffect();
                    slide.Background.EffectFormat.DisableInnerShadowEffect();
                    slide.Background.EffectFormat.DisableOuterShadowEffect();
                    slide.Background.EffectFormat.DisablePresetShadowEffect();
                    slide.Background.EffectFormat.DisableReflectionEffect();
                    slide.Background.EffectFormat.DisableSoftEdgeEffect();
                    deletedCount++;
                }
            }

            return deletedCount;
        }

        #endregion
    }
}
