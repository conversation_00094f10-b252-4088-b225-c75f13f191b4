<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>

    <AssemblyTitle>PPT批量处理工具</AssemblyTitle>
    <AssemblyDescription>基于Aspose.Slides的PPT批量处理软件</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="favicon.ico" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Aspose.Slides">
      <HintPath>Aspose.Slides.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="System.Data.OleDb" Version="8.0.0" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Aspose.Total.NET.lic">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Aspose.Slides.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Log\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
  </ItemGroup>

  <!-- 配置文件复制设置 - 确保编译时将Config目录下的JSON文件复制到输出目录 -->
  <ItemGroup>
    <None Include="Config\*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
