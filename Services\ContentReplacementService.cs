using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Aspose.Slides;
using Aspose.Slides.Effects;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 内容替换服务类 - 提供PPT内容替换功能，包括文本、形状、字体、颜色替换
    /// 功能：根据用户配置的替换规则，对PPT文件进行批量内容替换处理
    /// 支持：文本替换（普通文本、正则表达式）、形状替换（图片、文本框、样式）、字体替换、颜色替换
    /// </summary>
    public class ContentReplacementService
    {
        /// <summary>
        /// 幻灯片范围结果类 - 用于存储不同类型的幻灯片集合
        /// </summary>
        private class SlideRangeResult
        {
            /// <summary>
            /// 普通幻灯片页集合
            /// </summary>
            public List<ISlide> NormalSlides { get; set; } = new List<ISlide>();

            /// <summary>
            /// 母版页集合
            /// </summary>
            public List<IMasterSlide> MasterSlides { get; set; } = new List<IMasterSlide>();

            /// <summary>
            /// 版式页集合
            /// </summary>
            public List<ILayoutSlide> LayoutSlides { get; set; } = new List<ILayoutSlide>();
        }

        /// <summary>
        /// 根据替换范围设置获取需要处理的幻灯片集合
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="scope">替换范围设置</param>
        /// <returns>分类的幻灯片集合</returns>
        private SlideRangeResult GetSlidesForReplacementScope(IPresentation presentation, ReplacementScope scope)
        {
            var result = new SlideRangeResult();

            // 处理普通幻灯片页
            if (scope.IncludeNormalSlides)
            {
                result.NormalSlides.AddRange(presentation.Slides);
            }

            // 处理母版页
            if (scope.IncludeMasterSlides)
            {
                result.MasterSlides.AddRange(presentation.Masters);
            }

            // 处理版式页
            if (scope.IncludeLayoutSlides)
            {
                foreach (var master in presentation.Masters)
                {
                    result.LayoutSlides.AddRange(master.LayoutSlides);
                }
            }

            return result;
        }
        /// <summary>
        /// 应用内容替换设置
        /// </summary>
        /// <param name="presentation">演示文稿</param>
        /// <param name="settings">内容替换设置</param>
        /// <returns>处理结果</returns>
        public async Task<ProcessingResult> ApplyContentReplacementAsync(IPresentation presentation, ContentReplacementSettings settings)
        {
            try
            {
                var result = new ProcessingResult { IsSuccess = true };
                var messages = new List<string>();

                // 应用文本替换
                if (settings.TextReplacement.EnableTextReplacement)
                {
                    var textResult = await ApplyTextReplacementAsync(presentation, settings.TextReplacement);
                    if (!textResult.IsSuccess)
                    {
                        result.IsSuccess = false;
                        messages.Add($"文本替换失败: {textResult.Message}");
                    }
                    else
                    {
                        messages.Add("文本替换完成");
                    }
                }

                // 应用形状替换
                if (settings.ShapeReplacement.EnableShapeReplacement)
                {
                    var shapeResult = await ApplyShapeReplacementAsync(presentation, settings.ShapeReplacement);
                    if (!shapeResult.IsSuccess)
                    {
                        result.IsSuccess = false;
                        messages.Add($"形状替换失败: {shapeResult.Message}");
                    }
                    else
                    {
                        messages.Add("形状替换完成");
                    }
                }

                // 应用字体替换
                if (settings.FontReplacement.EnableFontReplacement)
                {
                    var fontResult = await ApplyFontReplacementAsync(presentation, settings.FontReplacement);
                    if (!fontResult.IsSuccess)
                    {
                        result.IsSuccess = false;
                        messages.Add($"字体替换失败: {fontResult.Message}");
                    }
                    else
                    {
                        messages.Add("字体替换完成");
                    }
                }

                // 应用颜色替换
                if (settings.ColorReplacement.EnableColorReplacement)
                {
                    var colorResult = await ApplyColorReplacementAsync(presentation, settings.ColorReplacement);
                    if (!colorResult.IsSuccess)
                    {
                        result.IsSuccess = false;
                        messages.Add($"颜色替换失败: {colorResult.Message}");
                    }
                    else
                    {
                        messages.Add("颜色替换完成");
                    }
                }

                result.Message = string.Join("; ", messages);
                return result;
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    Message = $"内容替换处理异常: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 应用文本替换 - 根据替换范围设置在指定的幻灯片类型中进行文本替换
        /// 支持普通文本替换和正则表达式替换，符合Aspose.Slides API规范
        /// </summary>
        private async Task<ProcessingResult> ApplyTextReplacementAsync(IPresentation presentation, TextReplacementSettings settings)
        {
            try
            {
                var processedRules = 0;

                // 获取需要处理的幻灯片集合
                var slideRange = GetSlidesForReplacementScope(presentation, settings.ReplacementScope);

                foreach (var rule in settings.ReplacementRules.Where(r => r.IsEnabled))
                {
                    try
                    {
                        // 在普通幻灯片页中进行文本替换
                        if (settings.ReplacementScope.IncludeNormalSlides)
                        {
                            processedRules += ApplyTextReplacementToSlides(slideRange.NormalSlides, rule);
                        }

                        // 在母版页中进行文本替换
                        if (settings.ReplacementScope.IncludeMasterSlides)
                        {
                            processedRules += ApplyTextReplacementToMasterSlides(slideRange.MasterSlides, rule);
                        }

                        // 在版式页中进行文本替换
                        if (settings.ReplacementScope.IncludeLayoutSlides)
                        {
                            processedRules += ApplyTextReplacementToLayoutSlides(slideRange.LayoutSlides, rule);
                        }
                    }
                    catch (Exception ruleEx)
                    {
                        // 记录单个规则处理失败，但继续处理其他规则
                        System.Diagnostics.Debug.WriteLine($"文本替换规则 '{rule.RuleName}' 处理失败: {ruleEx.Message}");
                    }
                }

                await Task.CompletedTask;

                return new ProcessingResult
                {
                    IsSuccess = true,
                    Message = $"成功应用了 {processedRules} 条文本替换规则"
                };
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    Message = $"文本替换处理异常: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 在普通幻灯片页中应用文本替换
        /// </summary>
        private int ApplyTextReplacementToSlides(List<ISlide> slides, TextReplacementRule rule)
        {
            var processedCount = 0;

            foreach (var slide in slides)
            {
                try
                {
                    if (rule.UseRegex)
                    {
                        // 使用正则表达式替换 - 通过遍历形状进行替换
                        var regex = new Regex(rule.FindText,
                            rule.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase);
                        processedCount += ReplaceTextInShapes(slide.Shapes, regex, rule.ReplaceText, true);
                    }
                    else
                    {
                        // 普通文本替换 - 通过遍历形状进行替换
                        processedCount += ReplaceTextInShapes(slide.Shapes, rule.FindText, rule.ReplaceText, rule.CaseSensitive, rule.WholeWord);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"幻灯片 {slide.SlideNumber} 文本替换失败: {ex.Message}");
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 在母版页中应用文本替换
        /// </summary>
        private int ApplyTextReplacementToMasterSlides(List<IMasterSlide> masterSlides, TextReplacementRule rule)
        {
            var processedCount = 0;

            foreach (var masterSlide in masterSlides)
            {
                try
                {
                    if (rule.UseRegex)
                    {
                        // 使用正则表达式替换 - 通过遍历形状进行替换
                        var regex = new Regex(rule.FindText,
                            rule.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase);
                        processedCount += ReplaceTextInShapes(masterSlide.Shapes, regex, rule.ReplaceText, true);
                    }
                    else
                    {
                        // 普通文本替换 - 通过遍历形状进行替换
                        processedCount += ReplaceTextInShapes(masterSlide.Shapes, rule.FindText, rule.ReplaceText, rule.CaseSensitive, rule.WholeWord);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"母版页文本替换失败: {ex.Message}");
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 在版式页中应用文本替换
        /// </summary>
        private int ApplyTextReplacementToLayoutSlides(List<ILayoutSlide> layoutSlides, TextReplacementRule rule)
        {
            var processedCount = 0;

            foreach (var layoutSlide in layoutSlides)
            {
                try
                {
                    if (rule.UseRegex)
                    {
                        // 使用正则表达式替换 - 通过遍历形状进行替换
                        var regex = new Regex(rule.FindText,
                            rule.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase);
                        processedCount += ReplaceTextInShapes(layoutSlide.Shapes, regex, rule.ReplaceText, true);
                    }
                    else
                    {
                        // 普通文本替换 - 通过遍历形状进行替换
                        processedCount += ReplaceTextInShapes(layoutSlide.Shapes, rule.FindText, rule.ReplaceText, rule.CaseSensitive, rule.WholeWord);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"版式页文本替换失败: {ex.Message}");
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 在形状集合中替换文本 - 支持普通文本替换
        /// </summary>
        private int ReplaceTextInShapes(IShapeCollection shapes, string findText, string replaceText, bool caseSensitive, bool wholeWord)
        {
            var processedCount = 0;

            foreach (IShape shape in shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                    {
                        foreach (IPortion portion in paragraph.Portions)
                        {
                            var originalText = portion.Text;
                            var newText = ReplaceTextWithOptions(originalText, findText, replaceText, caseSensitive, wholeWord);
                            if (originalText != newText)
                            {
                                portion.Text = newText;
                                processedCount++;
                            }
                        }
                    }
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 在形状集合中替换文本 - 支持正则表达式替换
        /// </summary>
        private int ReplaceTextInShapes(IShapeCollection shapes, Regex regex, string replaceText, bool isRegex)
        {
            var processedCount = 0;

            foreach (IShape shape in shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                    {
                        foreach (IPortion portion in paragraph.Portions)
                        {
                            var originalText = portion.Text;
                            var newText = regex.Replace(originalText, replaceText);
                            if (originalText != newText)
                            {
                                portion.Text = newText;
                                processedCount++;
                            }
                        }
                    }
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 根据选项替换文本
        /// </summary>
        private string ReplaceTextWithOptions(string originalText, string findText, string replaceText, bool caseSensitive, bool wholeWord)
        {
            if (string.IsNullOrEmpty(originalText) || string.IsNullOrEmpty(findText))
                return originalText;

            var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;

            if (wholeWord)
            {
                // 全词匹配使用正则表达式
                var pattern = $@"\b{Regex.Escape(findText)}\b";
                var options = caseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
                return Regex.Replace(originalText, pattern, replaceText, options);
            }
            else
            {
                // 普通替换
                return originalText.Replace(findText, replaceText, comparison);
            }
        }

        /// <summary>
        /// 应用形状替换 - 根据替换范围设置在指定的幻灯片类型中进行形状替换
        /// </summary>
        private async Task<ProcessingResult> ApplyShapeReplacementAsync(IPresentation presentation, ShapeReplacementSettings settings)
        {
            try
            {
                var processedCount = 0;

                // 获取需要处理的幻灯片集合
                var slideRange = GetSlidesForReplacementScope(presentation, settings.ReplacementScope);

                // 图片替换
                if (settings.EnableImageReplacement)
                {
                    processedCount += await ApplyImageReplacementAsync(slideRange, settings.ImageReplacementRules, settings.ReplacementScope);
                }

                // 文本框替换
                if (settings.EnableTextBoxReplacement)
                {
                    processedCount += await ApplyTextBoxReplacementAsync(slideRange, settings.TextBoxReplacementRules, settings.ReplacementScope);
                }

                // 形状样式替换
                if (settings.EnableShapeStyleReplacement)
                {
                    processedCount += await ApplyShapeStyleReplacementAsync(slideRange, settings.ShapeStyleReplacementRules, settings.ReplacementScope);
                }

                return new ProcessingResult
                {
                    IsSuccess = true,
                    Message = $"处理了 {processedCount} 个形状替换操作"
                };
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    Message = $"形状替换异常: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 应用字体替换 - 根据替换范围设置在指定的幻灯片类型中进行字体替换
        /// 支持字体名称替换、字体样式替换和字体嵌入，符合Aspose.Slides FontsManager API规范
        /// </summary>
        private async Task<ProcessingResult> ApplyFontReplacementAsync(IPresentation presentation, FontReplacementSettings settings)
        {
            try
            {
                var replacedCount = 0;

                // 获取需要处理的幻灯片集合
                var slideRange = GetSlidesForReplacementScope(presentation, settings.ReplacementScope);

                // 字体名称替换 - 使用Aspose.Slides FontsManager.ReplaceFont方法（全局替换）
                if (settings.EnableFontNameReplacement)
                {
                    foreach (var rule in settings.FontNameReplacementRules.Where(r => r.IsEnabled))
                    {
                        try
                        {
                            // 创建字体数据对象，符合Aspose.Slides IFontData接口
                            var sourceFont = new FontData(rule.SourceFontName);
                            var targetFont = new FontData(rule.TargetFontName);

                            // 使用Aspose.Slides FontsManager.ReplaceFont API进行字体替换
                            // 注意：FontsManager.ReplaceFont是全局替换，无法按范围限制
                            presentation.FontsManager.ReplaceFont(sourceFont, targetFont);
                            replacedCount++;
                        }
                        catch (Exception ruleEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"字体名称替换规则 '{rule.RuleName}' 处理失败: {ruleEx.Message}");
                        }
                    }
                }

                // 字体样式替换 - 支持按范围替换
                if (settings.EnableFontStyleReplacement)
                {
                    replacedCount += await ApplyFontStyleReplacementAsync(slideRange, settings.FontStyleReplacementRules, settings.ReplacementScope);
                }

                // 字体嵌入 - 使用Aspose.Slides FontsManager字体嵌入功能（全局操作）
                if (settings.EnableFontEmbedding)
                {
                    await ApplyFontEmbeddingAsync(presentation, settings.FontEmbeddingSettings);
                }

                await Task.CompletedTask;

                return new ProcessingResult
                {
                    IsSuccess = true,
                    Message = $"成功应用了 {replacedCount} 条字体替换规则"
                };
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    Message = $"字体替换处理异常: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 应用颜色替换 - 根据替换范围设置在指定的幻灯片类型中进行颜色替换
        /// </summary>
        private async Task<ProcessingResult> ApplyColorReplacementAsync(IPresentation presentation, ColorReplacementSettings settings)
        {
            try
            {
                var replacedCount = 0;

                // 获取需要处理的幻灯片集合
                var slideRange = GetSlidesForReplacementScope(presentation, settings.ReplacementScope);

                // 自定义颜色替换
                if (settings.EnableCustomColorReplacement)
                {
                    replacedCount += await ApplyCustomColorReplacementAsync(slideRange, settings.CustomColorReplacementRules, settings.ReplacementScope);
                }

                await Task.CompletedTask;

                return new ProcessingResult
                {
                    IsSuccess = true,
                    Message = $"应用了 {replacedCount} 条颜色替换规则"
                };
            }
            catch (Exception ex)
            {
                return new ProcessingResult
                {
                    IsSuccess = false,
                    Message = $"颜色替换异常: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 应用图片替换 - 根据替换范围设置在指定的幻灯片类型中进行图片替换
        /// 支持按文件名、内容、尺寸匹配，符合Aspose.Slides IPictureFrame API规范
        /// </summary>
        private async Task<int> ApplyImageReplacementAsync(SlideRangeResult slideRange, List<ImageReplacementRule> rules, ReplacementScope scope)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                // 验证源图片和目标图片文件是否存在
                if (!File.Exists(rule.SourceImagePath) || !File.Exists(rule.TargetImagePath))
                {
                    System.Diagnostics.Debug.WriteLine($"图片替换规则 '{rule.RuleName}' 跳过: 源图片或目标图片文件不存在");
                    continue;
                }

                try
                {
                    // 加载目标图片数据
                    var targetImageBytes = File.ReadAllBytes(rule.TargetImagePath);

                    // 在普通幻灯片页中替换图片
                    if (scope.IncludeNormalSlides)
                    {
                        processedCount += ProcessImageReplacementInSlides(slideRange.NormalSlides, rule, targetImageBytes);
                    }

                    // 在母版页中替换图片
                    if (scope.IncludeMasterSlides)
                    {
                        processedCount += ProcessImageReplacementInMasterSlides(slideRange.MasterSlides, rule, targetImageBytes);
                    }

                    // 在版式页中替换图片
                    if (scope.IncludeLayoutSlides)
                    {
                        processedCount += ProcessImageReplacementInLayoutSlides(slideRange.LayoutSlides, rule, targetImageBytes);
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理其他规则
                    System.Diagnostics.Debug.WriteLine($"图片替换规则 '{rule.RuleName}' 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 在普通幻灯片页中处理图片替换
        /// </summary>
        private int ProcessImageReplacementInSlides(List<ISlide> slides, ImageReplacementRule rule, byte[] targetImageBytes)
        {
            var processedCount = 0;

            foreach (var slide in slides)
            {
                processedCount += ProcessImageReplacementInShapes(slide.Shapes, rule, targetImageBytes);
            }

            return processedCount;
        }

        /// <summary>
        /// 在母版页中处理图片替换
        /// </summary>
        private int ProcessImageReplacementInMasterSlides(List<IMasterSlide> masterSlides, ImageReplacementRule rule, byte[] targetImageBytes)
        {
            var processedCount = 0;

            foreach (var masterSlide in masterSlides)
            {
                processedCount += ProcessImageReplacementInShapes(masterSlide.Shapes, rule, targetImageBytes);
            }

            return processedCount;
        }

        /// <summary>
        /// 在版式页中处理图片替换
        /// </summary>
        private int ProcessImageReplacementInLayoutSlides(List<ILayoutSlide> layoutSlides, ImageReplacementRule rule, byte[] targetImageBytes)
        {
            var processedCount = 0;

            foreach (var layoutSlide in layoutSlides)
            {
                processedCount += ProcessImageReplacementInShapes(layoutSlide.Shapes, rule, targetImageBytes);
            }

            return processedCount;
        }

        /// <summary>
        /// 在形状集合中处理图片替换
        /// </summary>
        private int ProcessImageReplacementInShapes(IShapeCollection shapes, ImageReplacementRule rule, byte[] targetImageBytes)
        {
            var processedCount = 0;

            foreach (IShape shape in shapes)
            {
                // 检查是否为图片框架，符合Aspose.Slides IPictureFrame接口
                if (shape is IPictureFrame pictureFrame)
                {
                    var currentImage = pictureFrame.PictureFormat.Picture.Image;

                    // 根据匹配方式判断是否需要替换
                    bool shouldReplace = false;

                    if (rule.MatchByFileName)
                    {
                        // 按文件名匹配（注意：Aspose.Slides不保存原始文件名信息）
                        // 这里使用图片内容比较作为替代方案
                        var sourceImageBytes = File.ReadAllBytes(rule.SourceImagePath);
                        shouldReplace = CompareImageContent(currentImage, sourceImageBytes);
                    }
                    else if (rule.MatchByContent)
                    {
                        // 按图片内容匹配
                        var sourceImageBytes = File.ReadAllBytes(rule.SourceImagePath);
                        shouldReplace = CompareImageContent(currentImage, sourceImageBytes);
                    }
                    else if (rule.MatchBySize)
                    {
                        // 按图片尺寸匹配
                        shouldReplace = CompareImageSize(currentImage, rule.SourceImagePath);
                    }

                    if (shouldReplace)
                    {
                        // 使用Aspose.Slides IPPImage.ReplaceImage方法替换图片
                        currentImage.ReplaceImage(targetImageBytes);
                        processedCount++;
                    }
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 应用图片替换 - 兼容旧版本的方法签名
        /// </summary>
        private async Task<int> ApplyImageReplacementAsync(IPresentation presentation, List<ImageReplacementRule> rules)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                // 验证源图片和目标图片文件是否存在
                if (!File.Exists(rule.SourceImagePath) || !File.Exists(rule.TargetImagePath))
                {
                    System.Diagnostics.Debug.WriteLine($"图片替换规则 '{rule.RuleName}' 跳过: 源图片或目标图片文件不存在");
                    continue;
                }

                try
                {
                    // 加载目标图片数据
                    var targetImageBytes = File.ReadAllBytes(rule.TargetImagePath);

                    foreach (ISlide slide in presentation.Slides)
                    {
                        foreach (IShape shape in slide.Shapes)
                        {
                            // 检查是否为图片框架，符合Aspose.Slides IPictureFrame接口
                            if (shape is IPictureFrame pictureFrame)
                            {
                                var currentImage = pictureFrame.PictureFormat.Picture.Image;

                                // 根据匹配方式判断是否需要替换
                                bool shouldReplace = false;

                                if (rule.MatchByFileName)
                                {
                                    // 按文件名匹配（注意：Aspose.Slides不保存原始文件名信息）
                                    // 这里使用图片内容比较作为替代方案
                                    var sourceImageBytes = File.ReadAllBytes(rule.SourceImagePath);
                                    shouldReplace = CompareImageContent(currentImage, sourceImageBytes);
                                }
                                else if (rule.MatchByContent)
                                {
                                    // 按图片内容匹配
                                    var sourceImageBytes = File.ReadAllBytes(rule.SourceImagePath);
                                    shouldReplace = CompareImageContent(currentImage, sourceImageBytes);
                                }
                                else if (rule.MatchBySize)
                                {
                                    // 按图片尺寸匹配
                                    shouldReplace = CompareImageSize(currentImage, rule.SourceImagePath);
                                }

                                if (shouldReplace)
                                {
                                    // 使用Aspose.Slides IPPImage.ReplaceImage方法替换图片
                                    currentImage.ReplaceImage(targetImageBytes);
                                    processedCount++;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理其他规则
                    System.Diagnostics.Debug.WriteLine($"图片替换规则 '{rule.RuleName}' 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 比较图片内容
        /// </summary>
        private bool CompareImageContent(IPPImage currentImage, byte[] sourceImageBytes)
        {
            try
            {
                // 简化实现：比较图片大小
                return currentImage.BinaryData.Length == sourceImageBytes.Length;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 比较图片尺寸
        /// </summary>
        private bool CompareImageSize(IPPImage currentImage, string sourceImagePath)
        {
            try
            {
                using var sourceImage = System.Drawing.Image.FromFile(sourceImagePath);
                return currentImage.Width == sourceImage.Width && currentImage.Height == sourceImage.Height;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 应用文本框替换 - 根据替换范围设置进行文本框替换
        /// </summary>
        private async Task<int> ApplyTextBoxReplacementAsync(SlideRangeResult slideRange, List<TextBoxReplacementRule> rules, ReplacementScope scope)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    // 在普通幻灯片页中替换文本框
                    if (scope.IncludeNormalSlides)
                    {
                        processedCount += ProcessTextBoxReplacementInSlides(slideRange.NormalSlides, rule);
                    }

                    // 在母版页中替换文本框
                    if (scope.IncludeMasterSlides)
                    {
                        processedCount += ProcessTextBoxReplacementInMasterSlides(slideRange.MasterSlides, rule);
                    }

                    // 在版式页中替换文本框
                    if (scope.IncludeLayoutSlides)
                    {
                        processedCount += ProcessTextBoxReplacementInLayoutSlides(slideRange.LayoutSlides, rule);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"文本框替换规则 {rule.RuleName} 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 在普通幻灯片页中处理文本框替换
        /// </summary>
        private int ProcessTextBoxReplacementInSlides(List<ISlide> slides, TextBoxReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var slide in slides)
            {
                processedCount += ProcessTextBoxReplacementInShapes(slide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在母版页中处理文本框替换
        /// </summary>
        private int ProcessTextBoxReplacementInMasterSlides(List<IMasterSlide> masterSlides, TextBoxReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var masterSlide in masterSlides)
            {
                processedCount += ProcessTextBoxReplacementInShapes(masterSlide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在版式页中处理文本框替换
        /// </summary>
        private int ProcessTextBoxReplacementInLayoutSlides(List<ILayoutSlide> layoutSlides, TextBoxReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var layoutSlide in layoutSlides)
            {
                processedCount += ProcessTextBoxReplacementInShapes(layoutSlide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在形状集合中处理文本框替换
        /// </summary>
        private int ProcessTextBoxReplacementInShapes(IShapeCollection shapes, TextBoxReplacementRule rule)
        {
            var processedCount = 0;

            foreach (IShape shape in shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    bool shouldReplace = false;

                    if (rule.MatchByContent)
                    {
                        // 按内容匹配
                        var currentText = autoShape.TextFrame.Text;
                        shouldReplace = currentText.Contains(rule.SourceTextContent);
                    }
                    else if (rule.MatchByPosition)
                    {
                        // 按位置匹配
                        var tolerance = 5.0f; // 位置容差
                        shouldReplace = Math.Abs(autoShape.X - rule.MatchX) <= tolerance &&
                                       Math.Abs(autoShape.Y - rule.MatchY) <= tolerance;
                    }

                    if (shouldReplace)
                    {
                        // 替换文本内容
                        autoShape.TextFrame.Text = autoShape.TextFrame.Text.Replace(
                            rule.SourceTextContent, rule.TargetTextContent);
                        processedCount++;
                    }
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 应用文本框替换 - 兼容旧版本的方法签名
        /// </summary>
        private async Task<int> ApplyTextBoxReplacementAsync(IPresentation presentation, List<TextBoxReplacementRule> rules)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    foreach (ISlide slide in presentation.Slides)
                    {
                        foreach (IShape shape in slide.Shapes)
                        {
                            if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                            {
                                bool shouldReplace = false;

                                if (rule.MatchByContent)
                                {
                                    // 按内容匹配
                                    var currentText = autoShape.TextFrame.Text;
                                    shouldReplace = currentText.Contains(rule.SourceTextContent);
                                }
                                else if (rule.MatchByPosition)
                                {
                                    // 按位置匹配
                                    var tolerance = 5.0f; // 位置容差
                                    shouldReplace = Math.Abs(autoShape.X - rule.MatchX) <= tolerance &&
                                                   Math.Abs(autoShape.Y - rule.MatchY) <= tolerance;
                                }

                                if (shouldReplace)
                                {
                                    // 替换文本内容
                                    autoShape.TextFrame.Text = autoShape.TextFrame.Text.Replace(
                                        rule.SourceTextContent, rule.TargetTextContent);
                                    processedCount++;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"文本框替换规则 {rule.RuleName} 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 应用形状样式替换 - 根据替换范围设置进行形状样式替换
        /// </summary>
        private async Task<int> ApplyShapeStyleReplacementAsync(SlideRangeResult slideRange, List<ShapeStyleReplacementRule> rules, ReplacementScope scope)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    // 在普通幻灯片页中替换形状样式
                    if (scope.IncludeNormalSlides)
                    {
                        processedCount += ProcessShapeStyleReplacementInSlides(slideRange.NormalSlides, rule);
                    }

                    // 在母版页中替换形状样式
                    if (scope.IncludeMasterSlides)
                    {
                        processedCount += ProcessShapeStyleReplacementInMasterSlides(slideRange.MasterSlides, rule);
                    }

                    // 在版式页中替换形状样式
                    if (scope.IncludeLayoutSlides)
                    {
                        processedCount += ProcessShapeStyleReplacementInLayoutSlides(slideRange.LayoutSlides, rule);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"形状样式替换规则 {rule.RuleName} 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 在普通幻灯片页中处理形状样式替换
        /// </summary>
        private int ProcessShapeStyleReplacementInSlides(List<ISlide> slides, ShapeStyleReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var slide in slides)
            {
                processedCount += ProcessShapeStyleReplacementInShapes(slide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在母版页中处理形状样式替换
        /// </summary>
        private int ProcessShapeStyleReplacementInMasterSlides(List<IMasterSlide> masterSlides, ShapeStyleReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var masterSlide in masterSlides)
            {
                processedCount += ProcessShapeStyleReplacementInShapes(masterSlide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在版式页中处理形状样式替换
        /// </summary>
        private int ProcessShapeStyleReplacementInLayoutSlides(List<ILayoutSlide> layoutSlides, ShapeStyleReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var layoutSlide in layoutSlides)
            {
                processedCount += ProcessShapeStyleReplacementInShapes(layoutSlide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在形状集合中处理形状样式替换
        /// </summary>
        private int ProcessShapeStyleReplacementInShapes(IShapeCollection shapes, ShapeStyleReplacementRule rule)
        {
            var processedCount = 0;

            foreach (IShape shape in shapes)
            {
                if (IsShapeTypeMatch(shape, rule.SourceShapeType))
                {
                    // 应用新的形状样式
                    ApplyShapeStyle(shape, rule);
                    processedCount++;
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 应用形状样式替换 - 兼容旧版本的方法签名
        /// </summary>
        private async Task<int> ApplyShapeStyleReplacementAsync(IPresentation presentation, List<ShapeStyleReplacementRule> rules)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    foreach (ISlide slide in presentation.Slides)
                    {
                        foreach (IShape shape in slide.Shapes)
                        {
                            if (IsShapeTypeMatch(shape, rule.SourceShapeType))
                            {
                                // 应用新的形状样式
                                ApplyShapeStyle(shape, rule);
                                processedCount++;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"形状样式替换规则 {rule.RuleName} 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 判断形状类型是否匹配
        /// </summary>
        private bool IsShapeTypeMatch(IShape shape, string targetShapeType)
        {
            if (shape is IAutoShape autoShape)
            {
                return targetShapeType switch
                {
                    "矩形" => autoShape.ShapeType == ShapeType.Rectangle,
                    "圆形" => autoShape.ShapeType == ShapeType.Ellipse,
                    "椭圆" => autoShape.ShapeType == ShapeType.Ellipse,
                    "三角形" => autoShape.ShapeType == ShapeType.Triangle,
                    "菱形" => autoShape.ShapeType == ShapeType.Diamond,
                    "五角星" => autoShape.ShapeType == ShapeType.FivePointedStar,
                    "箭头" => autoShape.ShapeType == ShapeType.RightArrow,
                    "线条" => autoShape.ShapeType == ShapeType.Line,
                    _ => false
                };
            }
            return false;
        }

        /// <summary>
        /// 应用形状样式
        /// </summary>
        private void ApplyShapeStyle(IShape shape, ShapeStyleReplacementRule rule)
        {
            if (shape.FillFormat != null)
            {
                if (rule.ReplaceFillColor && !string.IsNullOrEmpty(rule.TargetFillColor))
                {
                    try
                    {
                        var targetColor = ColorTranslator.FromHtml(rule.TargetFillColor);
                        shape.FillFormat.FillType = FillType.Solid;
                        shape.FillFormat.SolidFillColor.Color = targetColor;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置填充颜色失败: {ex.Message}");
                    }
                }

                if (rule.ReplaceBorderColor && shape.LineFormat != null && !string.IsNullOrEmpty(rule.TargetBorderColor))
                {
                    try
                    {
                        var targetColor = ColorTranslator.FromHtml(rule.TargetBorderColor);
                        shape.LineFormat.FillFormat.FillType = FillType.Solid;
                        shape.LineFormat.FillFormat.SolidFillColor.Color = targetColor;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置边框颜色失败: {ex.Message}");
                    }
                }

                if (rule.ReplaceBorderWidth && shape.LineFormat != null)
                {
                    try
                    {
                        shape.LineFormat.Width = rule.TargetBorderWidth;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置边框宽度失败: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 应用字体样式替换 - 根据替换范围设置进行字体样式替换
        /// </summary>
        private async Task<int> ApplyFontStyleReplacementAsync(SlideRangeResult slideRange, List<FontStyleReplacementRule> rules, ReplacementScope scope)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    // 在普通幻灯片页中替换字体样式
                    if (scope.IncludeNormalSlides)
                    {
                        processedCount += ProcessFontStyleReplacementInSlides(slideRange.NormalSlides, rule);
                    }

                    // 在母版页中替换字体样式
                    if (scope.IncludeMasterSlides)
                    {
                        processedCount += ProcessFontStyleReplacementInMasterSlides(slideRange.MasterSlides, rule);
                    }

                    // 在版式页中替换字体样式
                    if (scope.IncludeLayoutSlides)
                    {
                        processedCount += ProcessFontStyleReplacementInLayoutSlides(slideRange.LayoutSlides, rule);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"字体样式替换规则 {rule.RuleName} 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 在普通幻灯片页中处理字体样式替换
        /// </summary>
        private int ProcessFontStyleReplacementInSlides(List<ISlide> slides, FontStyleReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var slide in slides)
            {
                processedCount += ProcessFontStyleReplacementInShapes(slide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在母版页中处理字体样式替换
        /// </summary>
        private int ProcessFontStyleReplacementInMasterSlides(List<IMasterSlide> masterSlides, FontStyleReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var masterSlide in masterSlides)
            {
                processedCount += ProcessFontStyleReplacementInShapes(masterSlide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在版式页中处理字体样式替换
        /// </summary>
        private int ProcessFontStyleReplacementInLayoutSlides(List<ILayoutSlide> layoutSlides, FontStyleReplacementRule rule)
        {
            var processedCount = 0;
            foreach (var layoutSlide in layoutSlides)
            {
                processedCount += ProcessFontStyleReplacementInShapes(layoutSlide.Shapes, rule);
            }
            return processedCount;
        }

        /// <summary>
        /// 在形状集合中处理字体样式替换
        /// </summary>
        private int ProcessFontStyleReplacementInShapes(IShapeCollection shapes, FontStyleReplacementRule rule)
        {
            var processedCount = 0;

            foreach (IShape shape in shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                    {
                        foreach (IPortion portion in paragraph.Portions)
                        {
                            var portionFormat = portion.PortionFormat;

                            // 检查是否匹配源字体
                            if (portionFormat.LatinFont?.FontName == rule.SourceFontName)
                            {
                                // 应用新的字体样式
                                if (!string.IsNullOrEmpty(rule.TargetFontName))
                                {
                                    portionFormat.LatinFont = new FontData(rule.TargetFontName);
                                }

                                // 应用详细的字体样式属性
                                ApplyDetailedFontStyleProperties(portionFormat, rule);
                                processedCount++;
                            }
                        }
                    }
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 应用字体样式替换 - 兼容旧版本的方法签名
        /// </summary>
        private async Task<int> ApplyFontStyleReplacementAsync(IPresentation presentation, List<FontStyleReplacementRule> rules)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    foreach (ISlide slide in presentation.Slides)
                    {
                        foreach (IShape shape in slide.Shapes)
                        {
                            if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                            {
                                foreach (IParagraph paragraph in autoShape.TextFrame.Paragraphs)
                                {
                                    foreach (IPortion portion in paragraph.Portions)
                                    {
                                        var portionFormat = portion.PortionFormat;

                                        // 检查是否匹配源字体
                                        if (portionFormat.LatinFont?.FontName == rule.SourceFontName)
                                        {
                                            // 应用新的字体样式
                                            if (!string.IsNullOrEmpty(rule.TargetFontName))
                                            {
                                                portionFormat.LatinFont = new FontData(rule.TargetFontName);
                                            }

                                            // 应用详细的字体样式属性
                                            ApplyDetailedFontStyleProperties(portionFormat, rule);
                                            processedCount++;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"字体样式替换规则 {rule.RuleName} 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 应用详细的字体样式属性
        /// </summary>
        private void ApplyDetailedFontStyleProperties(IPortionFormat portionFormat, FontStyleReplacementRule rule)
        {
            try
            {
                // 应用字体大小
                if (rule.ReplaceFontSize)
                {
                    portionFormat.FontHeight = rule.TargetFontSize;
                }

                // 应用粗体样式
                if (rule.ReplaceBoldStyle)
                {
                    portionFormat.FontBold = rule.TargetBoldStyle ? NullableBool.True : NullableBool.False;
                }

                // 应用斜体样式
                if (rule.ReplaceItalicStyle)
                {
                    portionFormat.FontItalic = rule.TargetItalicStyle ? NullableBool.True : NullableBool.False;
                }

                // 应用下划线样式
                if (rule.ReplaceUnderlineStyle)
                {
                    portionFormat.FontUnderline = rule.TargetUnderlineStyle ?
                        TextUnderlineType.Single : TextUnderlineType.None;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用字体样式属性失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用字体样式属性（兼容旧版本）
        /// </summary>
        private void ApplyFontStyleProperties(IPortionFormat portionFormat, string replaceContent)
        {
            if (string.IsNullOrEmpty(replaceContent))
                return;

            var properties = replaceContent.Split(',', StringSplitOptions.RemoveEmptyEntries);

            foreach (var property in properties)
            {
                var prop = property.Trim();
                switch (prop.ToLower())
                {
                    case "粗体":
                        portionFormat.FontBold = NullableBool.True;
                        break;
                    case "斜体":
                        portionFormat.FontItalic = NullableBool.True;
                        break;
                    case "下划线":
                        portionFormat.FontUnderline = TextUnderlineType.Single;
                        break;
                    default:
                        // 可以处理其他样式属性
                        break;
                }
            }
        }

        /// <summary>
        /// 应用字体嵌入
        /// </summary>
        private async Task ApplyFontEmbeddingAsync(IPresentation presentation, FontEmbeddingSettings settings)
        {
            try
            {
                if (settings.EmbedAllFonts)
                {
                    // 获取演示文稿中使用的所有字体
                    var allFonts = presentation.FontsManager.GetFonts();
                    foreach (var font in allFonts)
                    {
                        try
                        {
                            presentation.FontsManager.AddEmbeddedFont(font, Aspose.Slides.Export.EmbedFontCharacters.All);
                        }
                        catch
                        {
                            // 某些系统字体可能无法嵌入，忽略错误
                        }
                    }
                }
                else if (settings.FontsToEmbed?.Count > 0)
                {
                    // 嵌入指定的字体
                    foreach (var fontName in settings.FontsToEmbed)
                    {
                        try
                        {
                            var fontData = new FontData(fontName);
                            var embedRule = settings.EmbedCharacters == FontEmbedCharacters.OnlyUsed ?
                                Aspose.Slides.Export.EmbedFontCharacters.OnlyUsed :
                                Aspose.Slides.Export.EmbedFontCharacters.All;

                            presentation.FontsManager.AddEmbeddedFont(fontData, embedRule);
                        }
                        catch
                        {
                            // 字体可能不存在或无法嵌入，忽略错误
                        }
                    }
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"字体嵌入处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用自定义颜色替换 - 根据替换范围设置进行颜色替换
        /// </summary>
        private async Task<int> ApplyCustomColorReplacementAsync(SlideRangeResult slideRange, List<CustomColorReplacementRule> rules, ReplacementScope scope)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    var sourceColor = ColorTranslator.FromHtml(rule.SourceColor);
                    var targetColor = ColorTranslator.FromHtml(rule.TargetColor);

                    // 在普通幻灯片页中替换颜色
                    if (scope.IncludeNormalSlides)
                    {
                        processedCount += ProcessColorReplacementInSlides(slideRange.NormalSlides, rule, sourceColor, targetColor);
                    }

                    // 在母版页中替换颜色
                    if (scope.IncludeMasterSlides)
                    {
                        processedCount += ProcessColorReplacementInMasterSlides(slideRange.MasterSlides, rule, sourceColor, targetColor);
                    }

                    // 在版式页中替换颜色
                    if (scope.IncludeLayoutSlides)
                    {
                        processedCount += ProcessColorReplacementInLayoutSlides(slideRange.LayoutSlides, rule, sourceColor, targetColor);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"自定义颜色替换规则 {rule.RuleName} 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 在普通幻灯片页中处理颜色替换
        /// </summary>
        private int ProcessColorReplacementInSlides(List<ISlide> slides, CustomColorReplacementRule rule, Color sourceColor, Color targetColor)
        {
            var processedCount = 0;
            foreach (var slide in slides)
            {
                processedCount += ProcessColorReplacementInShapes(slide.Shapes, rule, sourceColor, targetColor);
            }
            return processedCount;
        }

        /// <summary>
        /// 在母版页中处理颜色替换
        /// </summary>
        private int ProcessColorReplacementInMasterSlides(List<IMasterSlide> masterSlides, CustomColorReplacementRule rule, Color sourceColor, Color targetColor)
        {
            var processedCount = 0;
            foreach (var masterSlide in masterSlides)
            {
                processedCount += ProcessColorReplacementInShapes(masterSlide.Shapes, rule, sourceColor, targetColor);
            }
            return processedCount;
        }

        /// <summary>
        /// 在版式页中处理颜色替换
        /// </summary>
        private int ProcessColorReplacementInLayoutSlides(List<ILayoutSlide> layoutSlides, CustomColorReplacementRule rule, Color sourceColor, Color targetColor)
        {
            var processedCount = 0;
            foreach (var layoutSlide in layoutSlides)
            {
                processedCount += ProcessColorReplacementInShapes(layoutSlide.Shapes, rule, sourceColor, targetColor);
            }
            return processedCount;
        }

        /// <summary>
        /// 在形状集合中处理颜色替换
        /// </summary>
        private int ProcessColorReplacementInShapes(IShapeCollection shapes, CustomColorReplacementRule rule, Color sourceColor, Color targetColor)
        {
            var processedCount = 0;

            foreach (IShape shape in shapes)
            {
                // 替换填充颜色
                if (rule.ApplyToFillColor && shape.FillFormat != null)
                {
                    if (ReplaceShapeFillColor(shape, sourceColor, targetColor))
                    {
                        processedCount++;
                    }
                }

                // 替换文本颜色
                if (rule.ApplyToTextColor && shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    if (ReplaceTextColor(autoShape.TextFrame, sourceColor, targetColor))
                    {
                        processedCount++;
                    }
                }
            }

            return processedCount;
        }

        /// <summary>
        /// 应用自定义颜色替换 - 兼容旧版本的方法签名
        /// </summary>
        private async Task<int> ApplyCustomColorReplacementAsync(IPresentation presentation, List<CustomColorReplacementRule> rules)
        {
            var processedCount = 0;

            foreach (var rule in rules.Where(r => r.IsEnabled))
            {
                try
                {
                    var sourceColor = ColorTranslator.FromHtml(rule.SourceColor);
                    var targetColor = ColorTranslator.FromHtml(rule.TargetColor);

                    foreach (ISlide slide in presentation.Slides)
                    {
                        foreach (IShape shape in slide.Shapes)
                        {
                            // 替换填充颜色
                            if (rule.ApplyToFillColor && shape.FillFormat != null)
                            {
                                if (ReplaceShapeFillColor(shape, sourceColor, targetColor))
                                {
                                    processedCount++;
                                }
                            }

                            // 替换文本颜色
                            if (rule.ApplyToTextColor && shape is IAutoShape autoShape && autoShape.TextFrame != null)
                            {
                                if (ReplaceTextColor(autoShape.TextFrame, sourceColor, targetColor))
                                {
                                    processedCount++;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"自定义颜色替换规则 {rule.RuleName} 处理失败: {ex.Message}");
                }
            }

            await Task.CompletedTask;
            return processedCount;
        }

        /// <summary>
        /// 替换形状填充颜色
        /// </summary>
        private bool ReplaceShapeFillColor(IShape shape, Color sourceColor, Color targetColor)
        {
            try
            {
                if (shape.FillFormat?.FillType == FillType.Solid)
                {
                    var currentColor = shape.FillFormat.SolidFillColor.Color;
                    if (ColorsAreEqual(currentColor, sourceColor))
                    {
                        shape.FillFormat.SolidFillColor.Color = targetColor;
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 替换文本颜色
        /// </summary>
        private bool ReplaceTextColor(ITextFrame textFrame, Color sourceColor, Color targetColor)
        {
            var replaced = false;
            try
            {
                foreach (IParagraph paragraph in textFrame.Paragraphs)
                {
                    foreach (IPortion portion in paragraph.Portions)
                    {
                        var currentColor = portion.PortionFormat.FillFormat.SolidFillColor.Color;
                        if (ColorsAreEqual(currentColor, sourceColor))
                        {
                            portion.PortionFormat.FillFormat.FillType = FillType.Solid;
                            portion.PortionFormat.FillFormat.SolidFillColor.Color = targetColor;
                            replaced = true;
                        }
                    }
                }
                return replaced;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 比较两个颜色是否相等（考虑容差）
        /// </summary>
        private bool ColorsAreEqual(Color color1, Color color2, int tolerance = 10)
        {
            return Math.Abs(color1.R - color2.R) <= tolerance &&
                   Math.Abs(color1.G - color2.G) <= tolerance &&
                   Math.Abs(color1.B - color2.B) <= tolerance;
        }
    }
}
