using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 统一日志服务 - 根据日志设置进行日志记录和管理
    /// </summary>
    public class LogService
    {
        #region 单例模式

        private static LogService? _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 获取日志服务实例
        /// </summary>
        public static LogService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new LogService();
                        }
                    }
                }
                return _instance;
            }
        }

        #endregion

        #region 私有字段

        private readonly string _logDirectory;
        private readonly object _logLock = new object();

        // 日志缓存和定时写入相关字段
        private readonly List<LogEntry> _logBuffer = new List<LogEntry>(); // 日志缓存队列
        private readonly object _bufferLock = new object(); // 缓存队列锁
        private readonly System.Windows.Forms.Timer _flushTimer; // 定时写入定时器
        private const int FLUSH_INTERVAL_SECONDS = 10; // 每10秒写入一次

        #endregion

        #region 构造函数

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private LogService()
        {
            _logDirectory = Path.Combine(Application.StartupPath, "Log");

            // 确保日志目录存在
            if (!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }

            // 初始化定时写入定时器 - 每10秒将缓存的日志写入文件
            _flushTimer = new System.Windows.Forms.Timer();
            _flushTimer.Interval = FLUSH_INTERVAL_SECONDS * 1000; // 转换为毫秒
            _flushTimer.Tick += FlushTimer_Tick;
            _flushTimer.Start();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 记录处理开始日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="filePath">文件路径（可选）</param>
        public void LogProcessStart(string message, string? filePath = null)
        {
            WriteLog("处理开始", LogLevel.Info, message, filePath);
        }

        /// <summary>
        /// 记录处理完成日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="filePath">文件路径（可选）</param>
        public void LogProcessComplete(string message, string? filePath = null)
        {
            WriteLog("处理完成", LogLevel.Info, message, filePath);
        }

        /// <summary>
        /// 记录处理错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息（可选）</param>
        /// <param name="filePath">文件路径（可选）</param>
        public void LogProcessError(string message, Exception? exception = null, string? filePath = null)
        {
            var fullMessage = exception != null ? $"{message}\n异常详情: {exception}" : message;
            WriteLog("处理错误", LogLevel.Error, fullMessage, filePath);
        }

        /// <summary>
        /// 记录文件操作日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="filePath">文件路径（可选）</param>
        public void LogFileOperation(string message, string? filePath = null)
        {
            WriteLog("文件操作", LogLevel.Info, message, filePath);
        }

        /// <summary>
        /// 记录配置变更日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogConfigChange(string message)
        {
            WriteLog("配置变更", LogLevel.Info, message);
        }

        /// <summary>
        /// 记录调试信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogDebugInfo(string message)
        {
            WriteLog("调试信息", LogLevel.Debug, message);
        }

        /// <summary>
        /// 记录性能监控日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogPerformanceMonitor(string message)
        {
            WriteLog("性能监控", LogLevel.Info, message);
        }

        /// <summary>
        /// 记录用户操作日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogUserOperation(string message)
        {
            WriteLog("用户操作", LogLevel.Info, message);
        }

        /// <summary>
        /// 记录系统状态日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogSystemStatus(string message)
        {
            WriteLog("系统状态", LogLevel.Info, message);
        }

        /// <summary>
        /// 记录网络通信日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogNetworkCommunication(string message)
        {
            WriteLog("网络通信", LogLevel.Info, message);
        }

        /// <summary>
        /// 记录程序初始化日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogApplicationInit(string message)
        {
            WriteLog("程序初始化", LogLevel.Info, message);
        }

        /// <summary>
        /// 记录许可证相关日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="level">日志级别</param>
        public void LogLicense(string message, LogLevel level = LogLevel.Info)
        {
            WriteLog("许可证", level, message);
        }

        /// <summary>
        /// 记录文件处理详细信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="level">日志级别</param>
        public void LogFileProcessDetail(string message, string? filePath = null, LogLevel level = LogLevel.Info)
        {
            WriteLog("文件处理详情", level, message, filePath);
        }

        /// <summary>
        /// 记录配置加载日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="level">日志级别</param>
        public void LogConfigLoad(string message, LogLevel level = LogLevel.Info)
        {
            WriteLog("配置加载", level, message);
        }

        /// <summary>
        /// 记录内容删除操作详情
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="filePath">文件路径</param>
        public void LogContentDeletion(string message, string? filePath = null)
        {
            WriteLog("内容删除", LogLevel.Info, message, filePath);
        }

        /// <summary>
        /// 记录性能统计信息
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogPerformanceStats(string message)
        {
            WriteLog("性能统计", LogLevel.Info, message);
        }

        /// <summary>
        /// 记录异常详细信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        /// <param name="filePath">相关文件路径</param>
        public void LogException(string message, Exception exception, string? filePath = null)
        {
            var detailedMessage = $"{message}\n" +
                                $"异常类型: {exception.GetType().Name}\n" +
                                $"异常消息: {exception.Message}\n" +
                                $"堆栈跟踪: {exception.StackTrace}";

            if (exception.InnerException != null)
            {
                detailedMessage += $"\n内部异常: {exception.InnerException.Message}";
            }

            WriteLog("异常详情", LogLevel.Error, detailedMessage, filePath);
        }

        /// <summary>
        /// 清理过期日志文件
        /// </summary>
        public void CleanupOldLogs()
        {
            try
            {
                var logSettings = ConfigService.Instance.GetConfig().LogSettings;
                if (logSettings.LogRetentionDays <= 0) return;

                var cutoffDate = DateTime.Now.AddDays(-logSettings.LogRetentionDays);
                var logFiles = Directory.GetFiles(_logDirectory, "*.log", SearchOption.AllDirectories);

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(logFile);
                        }
                        catch
                        {
                            // 忽略删除失败的文件
                        }
                    }
                }
            }
            catch
            {
                // 忽略清理过程中的错误
            }
        }

        /// <summary>
        /// 手动刷新日志缓存 - 立即将所有缓存的日志写入文件
        /// </summary>
        public void FlushLogs()
        {
            FlushLogBuffer();
        }

        /// <summary>
        /// 释放资源 - 停止定时器并刷新剩余日志
        /// </summary>
        public void Dispose()
        {
            try
            {
                _flushTimer?.Stop();
                _flushTimer?.Dispose();
                FlushLogBuffer(); // 确保所有缓存的日志都被写入
            }
            catch
            {
                // 忽略释放过程中的错误
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 写入日志的核心方法 - 使用缓存机制，每10秒批量写入文件
        /// </summary>
        /// <param name="logType">日志类型</param>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="filePath">文件路径（可选）</param>
        private void WriteLog(string logType, LogLevel level, string message, string? filePath = null)
        {
            try
            {
                var logSettings = ConfigService.Instance.GetConfig().LogSettings;

                // 检查总开关
                if (!logSettings.EnableLogging)
                    return;

                // 检查日志级别
                if (level < logSettings.LogLevel)
                    return;

                // 检查日志类型是否启用
                if (!logSettings.EnabledLogTypes.ContainsKey(logType) || !logSettings.EnabledLogTypes[logType])
                    return;

                // 创建日志条目并添加到缓存队列
                var logEntry = new LogEntry
                {
                    LogType = logType,
                    Level = level,
                    Message = message,
                    FilePath = filePath,
                    Timestamp = DateTime.Now
                };

                lock (_bufferLock)
                {
                    _logBuffer.Add(logEntry);
                }
            }
            catch
            {
                // 忽略日志记录错误，避免影响主程序运行
            }
        }

        /// <summary>
        /// 定时器事件处理 - 每10秒将缓存的日志批量写入文件
        /// </summary>
        private void FlushTimer_Tick(object? sender, EventArgs e)
        {
            FlushLogBuffer();
        }

        /// <summary>
        /// 将缓存的日志批量写入文件
        /// </summary>
        private void FlushLogBuffer()
        {
            List<LogEntry> entriesToFlush;

            // 获取当前缓存的所有日志条目
            lock (_bufferLock)
            {
                if (_logBuffer.Count == 0)
                    return;

                entriesToFlush = new List<LogEntry>(_logBuffer);
                _logBuffer.Clear();
            }

            // 按日志类型分组批量写入
            var groupedEntries = entriesToFlush.GroupBy(e => e.LogType);

            lock (_logLock)
            {
                foreach (var group in groupedEntries)
                {
                    try
                    {
                        WriteLogGroup(group.Key, group.ToList());
                    }
                    catch
                    {
                        // 忽略写入错误，避免影响主程序运行
                    }
                }
            }
        }

        /// <summary>
        /// 写入同类型的日志组
        /// </summary>
        /// <param name="logType">日志类型</param>
        /// <param name="entries">日志条目列表</param>
        private void WriteLogGroup(string logType, List<LogEntry> entries)
        {
            var logSettings = ConfigService.Instance.GetConfig().LogSettings;

            // 确定日志文件路径
            var logFileName = $"{logType}_{DateTime.Now:yyyyMMdd}.log";
            var logFilePath = Path.Combine(_logDirectory, logFileName);

            // 检查文件大小限制
            if (File.Exists(logFilePath))
            {
                var fileInfo = new FileInfo(logFilePath);
                var maxSizeBytes = logSettings.MaxLogFileSizeMB * 1024 * 1024;

                if (fileInfo.Length > maxSizeBytes)
                {
                    // 创建新的日志文件（添加时间戳）
                    logFileName = $"{logType}_{DateTime.Now:yyyyMMdd_HHmmss}.log";
                    logFilePath = Path.Combine(_logDirectory, logFileName);
                }
            }

            // 构建所有日志条目的文本
            var logText = string.Join("", entries.Select(entry => BuildLogEntry(entry)));

            // 一次性写入所有日志条目
            File.AppendAllText(logFilePath, logText);
        }

        /// <summary>
        /// 构建日志条目
        /// </summary>
        /// <param name="entry">日志条目对象</param>
        /// <returns>格式化的日志条目</returns>
        private string BuildLogEntry(LogEntry entry)
        {
            var timestamp = entry.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var levelText = GetLogLevelText(entry.Level);

            var logEntry = $"[{timestamp}] [{levelText}] [{entry.LogType}] {entry.Message}";

            if (!string.IsNullOrEmpty(entry.FilePath))
            {
                logEntry += $" | 文件: {entry.FilePath}";
            }

            return logEntry + Environment.NewLine;
        }

        /// <summary>
        /// 构建日志条目（兼容性方法）
        /// </summary>
        /// <param name="logType">日志类型</param>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="filePath">文件路径（可选）</param>
        /// <returns>格式化的日志条目</returns>
        private string BuildLogEntry(string logType, LogLevel level, string message, string? filePath)
        {
            var entry = new LogEntry
            {
                LogType = logType,
                Level = level,
                Message = message,
                FilePath = filePath,
                Timestamp = DateTime.Now
            };
            return BuildLogEntry(entry);
        }

        /// <summary>
        /// 获取日志级别文本
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>日志级别文本</returns>
        private string GetLogLevelText(LogLevel level)
        {
            return level switch
            {
                LogLevel.Debug => "调试",
                LogLevel.Info => "信息",
                LogLevel.Warning => "警告",
                LogLevel.Error => "错误",
                _ => "未知"
            };
        }

        #endregion
    }

    /// <summary>
    /// 日志条目类 - 用于缓存日志信息
    /// </summary>
    internal class LogEntry
    {
        /// <summary>
        /// 日志类型
        /// </summary>
        public string LogType { get; set; } = string.Empty;

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; }

        /// <summary>
        /// 日志消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 文件路径（可选）
        /// </summary>
        public string? FilePath { get; set; }

        /// <summary>
        /// 日志时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
