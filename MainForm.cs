// PPT批量处理工具主窗体
// 功能：提供PPT批量处理的主界面，包含9个核心功能模块的入口
// 作者：PPT批量处理工具开发团队
// 创建时间：2025年
// 最后修改：2025年

using System;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli
{
    public partial class MainForm : Form
    {
        // 功能按钮数组 - 存储9个主要功能的复选框和按钮控件
        private CheckBox[] functionCheckBoxes = null!;  // 功能启用开关复选框数组
        private Button[] functionButtons = null!;       // 功能设置按钮数组

        // 操作按钮数组 - 存储9个操作控制按钮
        private Button[] actionButtons = null!;         // 操作控制按钮数组

        // 任务运行状态管理 - 控制文件处理任务的执行状态
        private bool _isProcessing = false;             // 是否正在处理文件的标志
        private bool _configResetExecuted = false;      // 是否已执行配置重置，防止关闭时覆盖默认配置
        private FileProcessingService? _fileProcessingService;  // 文件处理服务实例

        // 定时处理服务 - 管理定时任务的执行
        private ScheduleProcessingService? _scheduleProcessingService;  // 定时处理服务实例

        // 统计信息更新定时器 - 定期更新处理进度和统计信息
        private System.Windows.Forms.Timer? _statsUpdateTimer;  // 统计信息更新定时器
        private ProcessingStats? _currentStats;                 // 当前处理统计数据

        // UI性能监控 - 检测UI更新卡顿
        private DateTime _lastUIUpdateTime = DateTime.Now;      // 上次UI更新时间
        private int _uiUpdateCount = 0;                         // UI更新计数器

        // 统计标签数组 - 用于精确控制边距和布局
        private Label[]? _statsLabels;      // 统计信息标签数组（总文件数、成功数、失败数等）
        private Label[]? _progressLabels;   // 进度信息标签数组（处理速度、耗时、定时状态等）

        // 功能名称数组 - 定义9个主要功能模块的名称
        private readonly string[] functionNames = {
            "页面设置",      // 0 - 设置PPT页面尺寸、方向、背景等
            "内容删除设置",  // 1 - 删除PPT中的指定内容、格式等
            "内容替换设置",  // 2 - 替换PPT中的文本、图片、格式等
            "PPT格式设置",   // 3 - 全局格式化PPT样式和布局
            "匹配段落格式",  // 4 - 根据条件匹配和设置段落格式
            "PPT页脚设置",   // 5 - 设置PPT页眉页脚内容
            "文档属性",      // 6 - 设置PPT文档属性信息
            "文件名替换",    // 7 - 批量重命名PPT文件
            "PPT格式转换"    // 8 - 转换PPT为其他格式（PDF、图片等）
        };

        // 操作按钮名称数组 - 定义10个操作控制按钮的名称
        private readonly string[] actionNames = {
            "开始处理",      // 0 - 开始批量处理PPT文件
            "定时处理",      // 1 - 设置和启动定时处理任务
            "停止处理",      // 2 - 停止当前处理任务
            "日志设置",      // 3 - 配置日志记录选项
            "清空日志",      // 4 - 清空所有日志文件
            "导出配置",      // 5 - 导出当前配置到文件
            "导入配置",      // 6 - 从文件导入配置
            "重置配置",      // 7 - 重置所有配置为默认值
            "打开源目录",    // 8 - 在资源管理器中打开源目录
            "打开输出目录"   // 9 - 在资源管理器中打开输出目录
        };

        public MainForm()
        {
            InitializeComponent();
            InitializeCustomControls();
            InitializeStatsTimer();
            InitializeScheduleService();
            InitializeStatsLabels();
            InitializeLogService();
            LoadConfiguration();
        }

        /// <summary>
        /// 初始化自定义控件
        /// </summary>
        private void InitializeCustomControls()
        {
            InitializeFunctionButtons();
            InitializeActionButtons();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化功能按钮
        /// </summary>
        private void InitializeFunctionButtons()
        {
            functionCheckBoxes = new CheckBox[9];
            functionButtons = new Button[9];

            for (int i = 0; i < 9; i++)
            {
                int row = i / 3;
                int col = i % 3;

                // 创建复选框
                var checkBox = new CheckBox
                {
                    Name = $"chkFunction{i}",
                    Text = "",
                    Size = new Size(20, 20),
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Checked = false
                };

                // 创建功能按钮
                var button = new Button
                {
                    Name = $"btnFunction{i}",
                    Text = functionNames[i],
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    UseVisualStyleBackColor = true,
                    Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                    FlatStyle = FlatStyle.Standard
                };

                // 创建面板容器，设置边距
                var panel = new Panel
                {
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    Margin = new Padding(5, 3, 5, 3),
                    Padding = new Padding(5, 5, 5, 5)
                };

                // 添加控件到面板
                panel.Controls.Add(checkBox);
                panel.Controls.Add(button);

                // 定义Resize事件处理器
                EventHandler resizeHandler = (s, e) =>
                {
                    var panelHeight = panel.ClientSize.Height;
                    var panelWidth = panel.ClientSize.Width;

                    // 复选框垂直居中
                    var checkBoxY = (panelHeight - checkBox.Height) / 2;
                    checkBox.Location = new Point(5, checkBoxY);

                    // 按钮位置和大小
                    button.Location = new Point(30, 5);
                    button.Size = new Size(panelWidth - 40, panelHeight - 10);
                };

                // 设置面板的Resize事件
                panel.Resize += resizeHandler;

                tableLayoutPanelFunctions.Controls.Add(panel, col, row);

                functionCheckBoxes[i] = checkBox;
                functionButtons[i] = button;

                // 绑定事件
                int index = i; // 闭包变量
                button.Click += (s, e) => OnFunctionButtonClick(index);

                // 延迟触发Resize事件以确保初始布局正确
                var initTimer = new System.Windows.Forms.Timer
                {
                    Interval = 50
                };
                initTimer.Tick += (s, e) =>
                {
                    initTimer.Stop();
                    initTimer.Dispose();
                    // 手动调用Resize事件处理器来设置初始位置
                    resizeHandler(panel, EventArgs.Empty);
                };
                initTimer.Start();
            }
        }

        /// <summary>
        /// 初始化操作按钮
        /// </summary>
        private void InitializeActionButtons()
        {
            actionButtons = new Button[9];

            for (int i = 0; i < 9; i++)
            {
                int row = i / 3;
                int col = i % 3;

                var button = new Button
                {
                    Name = $"btnAction{i}",
                    Text = actionNames[i],
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    UseVisualStyleBackColor = true,
                    Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                    FlatStyle = FlatStyle.Standard,
                    Margin = new Padding(8, 5, 8, 5)
                };

                tableLayoutPanelActions.Controls.Add(button, col, row);
                actionButtons[i] = button;

                // 绑定事件
                int index = i; // 闭包变量
                button.Click += (s, e) => OnActionButtonClick(index);
            }

            // 设置停止处理按钮初始状态为禁用
            actionButtons[2].Enabled = false; // 停止处理
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 路径浏览按钮
            btnBrowseSource.Click += BtnBrowseSource_Click;
            btnBrowseOutput.Click += BtnBrowseOutput_Click;

            // 全选/取消全选按钮
            btnSelectAll.Click += BtnSelectAll_Click;
            btnDeselectAll.Click += BtnDeselectAll_Click;

            // 支持格式设定按钮
            btnSupportedFormats.Click += BtnSupportedFormats_Click;

            // 设置拖放功能
            SetupDragDropForPaths();

            // 路径文本框变更事件
            txtSourcePath.TextChanged += TxtSourcePath_TextChanged;
            txtOutputPath.TextChanged += TxtOutputPath_TextChanged;

            // 设置处理设置区域的事件处理
            SetupProcessingSettingsEvents();

            // 设置NumericUpDown控件的文字居中对齐
            SetupNumericUpDownAlignment();

            // 设置冲突处理下拉框文字居中
            SetupConflictHandlingComboBox();

            // 窗体关闭事件
            FormClosing += MainForm_FormClosing;
        }

        /// <summary>
        /// 功能按钮点击事件
        /// </summary>
        private void OnFunctionButtonClick(int index)
        {
            try
            {
                switch (index)
                {
                    case 0: // 页面设置
                        OpenPageSetupForm();
                        break;
                    case 1: // 内容删除设置
                        OpenContentDeletionForm();
                        break;
                    case 2: // 内容替换设置
                        OpenContentReplacementForm();
                        break;
                    case 3: // PPT格式设置
                        OpenPPTFormatSettingsForm();
                        break;
                    case 4: // 匹配段落格式
                        OpenParagraphFormatMatchingForm();
                        break;
                    case 5: // PPT页脚设置
                        OpenHeaderFooterSettingsForm();
                        break;
                    case 6: // 文档属性
                        OpenDocumentPropertiesSettingsForm();
                        break;
                    case 7: // 文件名替换
                        OpenFilenameReplacementForm();
                        break;
                    case 8: // PPT格式转换
                        OpenPPTFormatConversionForm();
                        break;
                    default:
                        MessageBox.Show($"未知功能: {functionNames[index]}", "错误",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开{functionNames[index]}设置窗口时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开页面设置窗体
        /// </summary>
        private void OpenPageSetupForm()
        {
            try
            {
                using var pageSetupForm = new Forms.PageSetupForm();
                var result = pageSetupForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    var settings = pageSetupForm.GetCurrentSettings();
                    ShowPathStatusMessage($"页面设置已更新: {settings.GetSizeDescription()}", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开页面设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开内容删除设置窗体
        /// </summary>
        private void OpenContentDeletionForm()
        {
            try
            {
                using var contentDeletionForm = new Forms.ContentDeletionForm();
                var result = contentDeletionForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    var settings = contentDeletionForm.GetCurrentSettings();
                    ShowPathStatusMessage("内容删除设置已更新", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开内容删除设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开内容替换设置窗体
        /// </summary>
        private void OpenContentReplacementForm()
        {
            try
            {
                using var contentReplacementForm = new Forms.ContentReplacementForm();
                var result = contentReplacementForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    ShowPathStatusMessage("内容替换设置已更新", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开内容替换设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开PPT格式设置窗体
        /// </summary>
        private void OpenPPTFormatSettingsForm()
        {
            try
            {
                using var pptFormatSettingsForm = new Forms.PPTFormatSettingsForm();
                var result = pptFormatSettingsForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    ShowPathStatusMessage("PPT格式设置已更新", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开PPT格式设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开匹配段落格式设置窗体
        /// </summary>
        private void OpenParagraphFormatMatchingForm()
        {
            try
            {
                using var paragraphFormatMatchingForm = new Forms.ParagraphFormatMatchingForm();
                var result = paragraphFormatMatchingForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    var settings = paragraphFormatMatchingForm.GetCurrentSettings();
                    var ruleCount = settings.MatchingRules.Count;
                    var enabledCount = settings.MatchingRules.Count(r => r.IsEnabled);
                    ShowPathStatusMessage($"匹配段落格式设置已更新: 共{ruleCount}个规则，{enabledCount}个已启用", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开匹配段落格式设置窗体时发生错误: {ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开PPT页脚设置窗体
        /// </summary>
        private void OpenHeaderFooterSettingsForm()
        {
            try
            {
                // 获取当前配置中的PPT页脚设置
                var config = ConfigService.Instance.GetConfig();
                using var headerFooterForm = new Forms.HeaderFooterSettingsForm(config.HeaderFooterSettings);
                var result = headerFooterForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    // 更新配置中的PPT页脚设置
                    config.HeaderFooterSettings = headerFooterForm.Settings;
                    ConfigService.Instance.UpdateConfig(config);

                    // 显示成功消息
                    var enabledFeatures = new List<string>();
                    if (config.HeaderFooterSettings.PresentationSettings.IsEnabled)
                        enabledFeatures.Add("演示文稿级别");
                    if (config.HeaderFooterSettings.SlideSettings.IsEnabled)
                        enabledFeatures.Add("普通幻灯片");
                    if (config.HeaderFooterSettings.MasterSlideSettings.IsEnabled)
                        enabledFeatures.Add("母版幻灯片");
                    if (config.HeaderFooterSettings.LayoutSlideSettings.IsEnabled)
                        enabledFeatures.Add("布局幻灯片");
                    if (config.HeaderFooterSettings.NotesSlideSettings.IsEnabled)
                        enabledFeatures.Add("备注幻灯片");

                    var message = enabledFeatures.Count > 0
                        ? $"PPT页脚设置已更新: 已启用 {string.Join("、", enabledFeatures)}"
                        : "PPT页脚设置已更新";

                    ShowPathStatusMessage(message, false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开PPT页脚设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开文档属性设置窗体
        /// </summary>
        private void OpenDocumentPropertiesSettingsForm()
        {
            try
            {
                // 获取当前配置中的文档属性设置
                var config = ConfigService.Instance.GetConfig();
                using var documentPropertiesForm = new Forms.DocumentPropertiesSettingsForm(config.DocumentPropertiesSettings);
                var result = documentPropertiesForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    // 更新配置中的文档属性设置
                    config.DocumentPropertiesSettings = documentPropertiesForm.Settings;
                    ConfigService.Instance.UpdateConfig(config);

                    // 显示成功消息
                    var enabledFeatures = new List<string>();
                    if (config.DocumentPropertiesSettings.BasicProperties.IsEnabled)
                        enabledFeatures.Add("基本信息属性");
                    if (config.DocumentPropertiesSettings.StatisticsProperties.IsEnabled)
                        enabledFeatures.Add("统计属性");
                    if (config.DocumentPropertiesSettings.TimeProperties.IsEnabled)
                        enabledFeatures.Add("时间属性");
                    if (config.DocumentPropertiesSettings.CustomProperties.IsEnabled)
                        enabledFeatures.Add("自定义属性");

                    var message = enabledFeatures.Count > 0
                        ? $"文档属性设置已更新: 已启用 {string.Join("、", enabledFeatures)}"
                        : "文档属性设置已更新";

                    ShowPathStatusMessage(message, false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文档属性设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开文件名替换设置窗体
        /// </summary>
        private void OpenFilenameReplacementForm()
        {
            try
            {
                // 获取当前配置中的文件名替换设置
                var config = ConfigService.Instance.GetConfig();
                using var filenameReplacementForm = new Forms.FilenameReplacementForm(config.FilenameReplacementSettings);
                var result = filenameReplacementForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    // 更新配置中的文件名替换设置
                    config.FilenameReplacementSettings = filenameReplacementForm.Settings;
                    ConfigService.Instance.UpdateConfig(config);

                    // 显示成功消息
                    var enabledFeatures = new List<string>();
                    if (config.FilenameReplacementSettings.PatternReplacement.IsEnabled)
                        enabledFeatures.Add("文件名模式替换");

                    var message = enabledFeatures.Count > 0
                        ? $"文件名替换设置已更新: 已启用 {string.Join("、", enabledFeatures)}"
                        : "文件名替换设置已更新";

                    ShowPathStatusMessage(message, false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件名替换设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开PPT格式转换窗体
        /// </summary>
        private void OpenPPTFormatConversionForm()
        {
            try
            {
                // 获取当前配置中的PPT格式转换设置
                var config = ConfigService.Instance.GetConfig();
                using var pptFormatConversionForm = new Forms.PPTFormatConversionForm(config.PPTFormatConversionSettings);
                var result = pptFormatConversionForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    var settings = pptFormatConversionForm.GetCurrentSettings();
                    config.PPTFormatConversionSettings = settings;
                    ConfigService.Instance.UpdateConfig(config);

                    // 显示状态消息
                    var enabledFeatures = new List<string>();
                    if (settings.PDFSettings.EnablePDFConversion) enabledFeatures.Add("PDF转换");
                    if (settings.ImageSettings.EnablePNGConversion || settings.ImageSettings.EnableJPEGConversion ||
                        settings.ImageSettings.EnableBMPConversion || settings.ImageSettings.EnableTIFFConversion ||
                        settings.ImageSettings.EnableSVGConversion) enabledFeatures.Add("图片转换");
                    if (settings.WebSettings.EnableHTMLConversion || settings.WebSettings.EnableXAMLConversion) enabledFeatures.Add("Web转换");
                    if (settings.OtherSettings.EnableThumbnailGeneration) enabledFeatures.Add("其他格式转换");

                    var message = enabledFeatures.Count > 0
                        ? $"PPT格式转换设置已更新: 已启用 {string.Join("、", enabledFeatures)}"
                        : "PPT格式转换设置已更新";

                    ShowPathStatusMessage(message, false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开PPT格式转换设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 操作按钮点击事件
        /// </summary>
        private void OnActionButtonClick(int index)
        {
            switch (index)
            {
                case 0: // 开始处理
                    StartProcessing();
                    break;
                case 1: // 定时处理
                    ShowScheduleDialog();
                    break;
                case 2: // 停止处理
                    StopProcessing();
                    break;
                case 3: // 日志设置
                    ShowLogSettings();
                    break;
                case 4: // 清空日志
                    ClearLogs();
                    break;
                case 5: // 导出配置
                    ExportConfiguration();
                    break;
                case 6: // 导入配置
                    ImportConfiguration();
                    break;
                case 7: // 重置配置
                    ResetConfiguration();
                    break;
                case 8: // 打开源目录
                    OpenSourceDirectory();
                    break;
                case 9: // 打开输出目录
                    OpenOutputDirectory();
                    break;
            }
        }

        /// <summary>
        /// 浏览源目录
        /// </summary>
        private void BtnBrowseSource_Click(object? sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择源目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtSourcePath.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// 浏览输出目录
        /// </summary>
        private void BtnBrowseOutput_Click(object? sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择输出目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtOutputPath.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// 全选功能
        /// </summary>
        private void BtnSelectAll_Click(object? sender, EventArgs e)
        {
            foreach (var checkBox in functionCheckBoxes)
            {
                checkBox.Checked = true;
            }
        }

        /// <summary>
        /// 取消全选功能
        /// </summary>
        private void BtnDeselectAll_Click(object? sender, EventArgs e)
        {
            foreach (var checkBox in functionCheckBoxes)
            {
                checkBox.Checked = false;
            }
        }

        /// <summary>
        /// 支持格式设定按钮点击事件
        /// </summary>
        private void BtnSupportedFormats_Click(object? sender, EventArgs e)
        {
            try
            {
                using var formatsForm = new Forms.SupportedFormatsForm();
                var result = formatsForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    // 格式设置已保存，可以在这里添加其他处理逻辑
                    ShowPathStatusMessage("支持格式设置已更新", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开支持格式设定窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置处理设置区域的事件处理
        /// </summary>
        private void SetupProcessingSettingsEvents()
        {
            // 线程数变更事件
            numThreadCount.ValueChanged += NumThreadCount_ValueChanged;

            // 重试次数变更事件
            numRetryCount.ValueChanged += NumRetryCount_ValueChanged;

            // 批处理数量变更事件
            numBatchSize.ValueChanged += NumBatchSize_ValueChanged;

            // 冲突处理选项变更事件
            radioCopy.CheckedChanged += RadioCopy_CheckedChanged;
            radioMove.CheckedChanged += RadioMove_CheckedChanged;
            chkDeleteSource.CheckedChanged += ChkDeleteSource_CheckedChanged;
            cmbConflictHandling.SelectedIndexChanged += CmbConflictHandling_SelectedIndexChanged;

            // 路径选项变更事件
            chkIncludeSubfolders.CheckedChanged += ChkIncludeSubfolders_CheckedChanged;
            chkKeepStructure.CheckedChanged += ChkKeepStructure_CheckedChanged;
        }

        /// <summary>
        /// 设置NumericUpDown控件的文字居中对齐
        /// </summary>
        private void SetupNumericUpDownAlignment()
        {
            // 延迟设置，确保控件已完全初始化
            Load += (s, e) =>
            {
                // 设置线程数输入框文字居中
                SetNumericUpDownTextAlign(numThreadCount);

                // 设置重试次数输入框文字居中
                SetNumericUpDownTextAlign(numRetryCount);

                // 设置批处理数量输入框文字居中
                SetNumericUpDownTextAlign(numBatchSize);
            };
        }

        /// <summary>
        /// 初始化统计更新定时器 - 优化更新频率避免UI卡顿
        /// </summary>
        private void InitializeStatsTimer()
        {
            _statsUpdateTimer = new System.Windows.Forms.Timer
            {
                Interval = 2000 // 每2秒更新一次，减少UI更新频率避免卡顿
            };
            _statsUpdateTimer.Tick += StatsUpdateTimer_Tick;
        }

        /// <summary>
        /// 初始化定时处理服务
        /// </summary>
        private void InitializeScheduleService()
        {
            _scheduleProcessingService = ScheduleProcessingService.Instance;
            _scheduleProcessingService.SetMainForm(this);

            // 订阅定时处理服务事件
            _scheduleProcessingService.StatusChanged += OnScheduleStatusChanged;
            _scheduleProcessingService.ExecutionStarted += OnScheduleExecutionStarted;
            _scheduleProcessingService.ExecutionCompleted += OnScheduleExecutionCompleted;
        }

        /// <summary>
        /// 初始化日志服务
        /// </summary>
        private static void InitializeLogService()
        {
            try
            {
                // 初始化日志服务实例
                var logService = LogService.Instance;

                // 记录程序启动日志
                logService.LogSystemStatus("PPT批量处理工具启动");

                // 清理过期日志文件
                logService.CleanupOldLogs();
            }
            catch (Exception ex)
            {
                // 如果日志服务初始化失败，不影响程序运行
                MessageBox.Show($"日志服务初始化失败: {ex.Message}", "警告",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 初始化统计标签 - 使用多个Label实现精确边距控制
        /// </summary>
        private void InitializeStatsLabels()
        {
            try
            {
                // 隐藏原有的统计标签
                lblStats.Visible = false;
                lblProgress.Visible = false;

                // 创建统计标签数组
                _statsLabels = new Label[5]; // 总文件数、成功处理、处理失败、开始时间、预计结束时间
                _progressLabels = new Label[3]; // 处理速度、处理耗时、定时处理状态

                // 初始化统计标签
                InitializeStatsRow();

                // 初始化进度标签
                InitializeProgressRow();

                // 调试信息：记录初始化完成（仅在调试模式下显示）
                #if DEBUG
                Console.WriteLine($"统计标签初始化完成 - 统计标签: {_statsLabels?.Length ?? 0} 个, 进度标签: {_progressLabels?.Length ?? 0} 个");
                #endif
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化统计标签时发生错误: {ex.Message}\n\n堆栈跟踪:\n{ex.StackTrace}",
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化统计行标签
        /// </summary>
        private void InitializeStatsRow()
        {
            // 确保数组已正确初始化
            if (_statsLabels == null || _statsLabels.Length != 5)
            {
                MessageBox.Show("统计标签数组未正确初始化", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var statsTexts = new string[] { "总文件数: 0", "成功处理: 0 (0.0%)", "处理失败: 0 (0.0%)", "开始时间: --", "预计结束时间: --" };
            var startX = 6; // 起始X位置
            var currentX = startX;
            var y = 41; // Y位置（与原lblStats相同）
            var margin = 80; // 标签间距（像素）

            for (int i = 0; i < _statsLabels.Length; i++)
            {
                _statsLabels[i] = new Label
                {
                    Text = statsTexts[i],
                    AutoSize = true,
                    Location = new Point(currentX, y),
                    Font = lblStats.Font,
                    ForeColor = lblStats.ForeColor,
                    BackColor = Color.Transparent,
                    Visible = true // 确保标签可见
                };

                groupBoxStats.Controls.Add(_statsLabels[i]);

                // 计算下一个标签的X位置
                currentX += _statsLabels[i].PreferredWidth + margin;
            }
        }

        /// <summary>
        /// 初始化进度行标签
        /// </summary>
        private void InitializeProgressRow()
        {
            // 确保数组已正确初始化
            if (_progressLabels == null || _progressLabels.Length != 3)
            {
                MessageBox.Show("进度标签数组未正确初始化", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var progressTexts = new string[] { "处理速度: 0 文件/分钟", "处理耗时: 00:00:00", "定时处理: 未运行" };
            var startX = 6; // 起始X位置
            var currentX = startX;
            var y = 130; // Y位置（进度条在51+23=74，所以85应该在进度条下方）
            var margin = 80; // 标签间距（像素）

            // 先扩大groupBoxStats的高度以确保有足够空间
            if (groupBoxStats.Height < 140)
            {
                groupBoxStats.Height = 140;
            }

            for (int i = 0; i < _progressLabels.Length; i++)
            {
                _progressLabels[i] = new Label
                {
                    Text = progressTexts[i],
                    AutoSize = true,
                    Location = new Point(currentX, y),
                    Font = lblProgress.Font, // 使用正常字体
                    ForeColor = lblProgress.ForeColor, // 使用正常颜色
                    BackColor = Color.Transparent, // 使用透明背景
                    Visible = true // 确保标签可见
                };

                groupBoxStats.Controls.Add(_progressLabels[i]);

                #if DEBUG
                Console.WriteLine($"进度标签{i}: \"{progressTexts[i]}\" 位置({currentX}, {y})");
                #endif

                // 计算下一个标签的X位置
                currentX += _progressLabels[i].PreferredWidth + margin;
            }
        }

        /// <summary>
        /// 统计更新定时器事件
        /// </summary>
        private void StatsUpdateTimer_Tick(object? sender, EventArgs e)
        {
            if (_currentStats != null && _isProcessing)
            {
                UpdateStatsDisplay(_currentStats);
            }
        }

        #region 定时处理服务事件处理

        /// <summary>
        /// 定时处理状态变更事件
        /// </summary>
        private void OnScheduleStatusChanged(object? sender, ScheduleStatusChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnScheduleStatusChanged(sender, e)));
                return;
            }

            // 更新定时处理状态显示
            UpdateScheduleStatusDisplay();

            // 更新按钮状态
            UpdateActionButtonStates();

            // 显示状态消息
            if (!string.IsNullOrEmpty(e.Message))
            {
                ShowPathStatusMessage(e.Message, e.IsError);
            }
        }

        /// <summary>
        /// 定时处理执行开始事件
        /// </summary>
        private void OnScheduleExecutionStarted(object? sender, ScheduleExecutionEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnScheduleExecutionStarted(sender, e)));
                return;
            }

            ShowPathStatusMessage($"定时处理 - {e.TriggerType} 第{e.RunCount}次执行开始", false);
        }

        /// <summary>
        /// 定时处理执行完成事件
        /// </summary>
        private void OnScheduleExecutionCompleted(object? sender, ScheduleExecutionEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnScheduleExecutionCompleted(sender, e)));
                return;
            }

            var status = e.Success ? "成功" : "失败";
            ShowPathStatusMessage($"定时处理 - {e.TriggerType} 第{e.RunCount}次执行{status}", !e.Success);
        }

        #endregion

        /// <summary>
        /// 更新统计信息显示 - 优化性能避免UI卡顿
        /// </summary>
        private void UpdateStatsDisplay(ProcessingStats stats)
        {
            try
            {
                if (InvokeRequired)
                {
                    // 使用BeginInvoke异步更新，避免阻塞调用线程
                    BeginInvoke(new Action(() => UpdateStatsDisplay(stats)));
                    return;
                }

                // UI性能监控 - 记录更新时间和频率
                var currentTime = DateTime.Now;
                var timeSinceLastUpdate = currentTime - _lastUIUpdateTime;
                _uiUpdateCount++;

                // 如果UI更新过于频繁（小于100ms间隔），跳过本次更新避免卡顿
                if (timeSinceLastUpdate.TotalMilliseconds < 100 && _uiUpdateCount > 1)
                {
                    return;
                }

                _lastUIUpdateTime = currentTime;

                // 计算统计数据
                var processedCount = stats.SuccessCount + stats.FailureCount;
                var successRate = processedCount > 0 ? (double)stats.SuccessCount / processedCount * 100 : 0;
                var failureRate = processedCount > 0 ? (double)stats.FailureCount / processedCount * 100 : 0;
                var elapsedTime = stats.ElapsedTime;
                var processingSpeed = stats.ProcessingSpeed;

                // 计算预计结束时间
                var estimatedEndTime = CalculateEstimatedEndTime(stats);

                // 更新统计标签（使用新的标签数组）
                if (_statsLabels != null && _statsLabels.Length >= 5)
                {
                    _statsLabels[0].Text = $"总文件数: {stats.TotalFiles}";
                    _statsLabels[1].Text = $"成功处理: {stats.SuccessCount} ({successRate:F1}%)";
                    _statsLabels[2].Text = $"处理失败: {stats.FailureCount} ({failureRate:F1}%)";
                    _statsLabels[3].Text = $"开始时间: {stats.StartTime:HH:mm:ss}";
                    _statsLabels[4].Text = $"预计结束时间: {estimatedEndTime}";
                }
                else
                {
                    // 回退到原有标签
                    lblStats.Text = $"总文件数: {stats.TotalFiles}    " +
                                   $"成功处理: {stats.SuccessCount} ({successRate:F1}%)    " +
                                   $"处理失败: {stats.FailureCount} ({failureRate:F1}%)    " +
                                   $"开始时间: {stats.StartTime:HH:mm:ss}    " +
                                   $"预计结束时间: {estimatedEndTime}";
                }

                // 更新进度标签（使用新的标签数组）
                var scheduleStatus = GetScheduleStatusText();
                if (_progressLabels != null && _progressLabels.Length >= 3)
                {
                    _progressLabels[0].Text = $"处理速度: {processingSpeed:F1} 文件/分钟";
                    _progressLabels[1].Text = $"处理耗时: {FormatTimeSpan(elapsedTime)}";
                    _progressLabels[2].Text = scheduleStatus;
                }
                else
                {
                    // 回退到原有标签
                    lblProgress.Text = $"处理速度: {processingSpeed:F1} 文件/分钟    " +
                                      $"处理耗时: {FormatTimeSpan(elapsedTime)}    " +
                                      $"{scheduleStatus}";
                }

                // 更新进度条
                var progressPercentage = stats.TotalFiles > 0 ?
                    (int)((double)processedCount / stats.TotalFiles * 100) : 0;
                progressBar.Value = Math.Min(progressPercentage, 100);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"更新统计显示时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 计算预计结束时间
        /// </summary>
        private static string CalculateEstimatedEndTime(ProcessingStats stats)
        {
            try
            {
                var processedCount = stats.SuccessCount + stats.FailureCount;
                var remainingCount = stats.TotalFiles - processedCount;

                if (remainingCount <= 0)
                {
                    return stats.EndTime?.ToString("HH:mm:ss") ?? "已完成";
                }

                if (processedCount == 0 || stats.ProcessingSpeed <= 0)
                {
                    return "--";
                }

                var remainingMinutes = remainingCount / stats.ProcessingSpeed;
                var estimatedEndTime = DateTime.Now.AddMinutes(remainingMinutes);
                return estimatedEndTime.ToString("HH:mm:ss");
            }
            catch
            {
                return "--";
            }
        }

        /// <summary>
        /// 格式化时间跨度显示
        /// </summary>
        private static string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
            {
                return $"{(int)timeSpan.TotalDays}天 {timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
            else
            {
                return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
        }

        /// <summary>
        /// 重置统计信息显示
        /// </summary>
        private void ResetStatsDisplay()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(ResetStatsDisplay));
                    return;
                }

                // 重置统计标签（使用新的标签数组）
                if (_statsLabels != null && _statsLabels.Length >= 5)
                {
                    _statsLabels[0].Text = "总文件数: 0";
                    _statsLabels[1].Text = "成功处理: 0 (0.0%)";
                    _statsLabels[2].Text = "处理失败: 0 (0.0%)";
                    _statsLabels[3].Text = "开始时间: --";
                    _statsLabels[4].Text = "预计结束时间: --";
                }
                else
                {
                    // 回退到原有标签
                    lblStats.Text = "总文件数: 0    成功处理: 0 (0.0%)    处理失败: 0 (0.0%)    开始时间: --    预计结束时间: --";
                }

                // 重置进度标签（使用新的标签数组）
                var scheduleStatus = GetScheduleStatusText();
                if (_progressLabels != null && _progressLabels.Length >= 3)
                {
                    _progressLabels[0].Text = "处理速度: 0 文件/分钟";
                    _progressLabels[1].Text = "处理耗时: 00:00:00";
                    _progressLabels[2].Text = scheduleStatus;
                }
                else
                {
                    // 回退到原有标签
                    lblProgress.Text = $"处理速度: 0 文件/分钟    处理耗时: 00:00:00    {scheduleStatus}";
                }

                // 重置进度条
                progressBar.Value = 0;

                // 清空当前统计数据
                _currentStats = null;
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"重置统计显示时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 获取定时处理状态文本
        /// </summary>
        private string GetScheduleStatusText()
        {
            try
            {
                if (_scheduleProcessingService?.IsRunning == true)
                {
                    return $"定时处理: {_scheduleProcessingService.GetCurrentStatusDescription()}";
                }
                else
                {
                    return "定时处理: 未运行";
                }
            }
            catch
            {
                return "定时处理: 状态未知";
            }
        }

        /// <summary>
        /// 更新定时处理状态显示
        /// </summary>
        private void UpdateScheduleStatusDisplay()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(UpdateScheduleStatusDisplay));
                    return;
                }

                // 如果当前正在处理文件，更新完整的统计显示
                if (_currentStats != null && _isProcessing)
                {
                    UpdateStatsDisplay(_currentStats);
                }
                else
                {
                    // 否则只更新进度标签中的定时处理状态
                    var scheduleStatus = GetScheduleStatusText();

                    if (_progressLabels != null && _progressLabels.Length >= 3)
                    {
                        // 使用新的标签数组，只更新定时处理状态
                        _progressLabels[2].Text = scheduleStatus;
                    }
                    else
                    {
                        // 回退到原有标签逻辑
                        var currentText = lblProgress.Text;

                        // 查找定时处理状态的位置并替换
                        var parts = currentText.Split(new string[] { "定时处理:" }, StringSplitOptions.None);
                        if (parts.Length > 1)
                        {
                            lblProgress.Text = parts[0] + scheduleStatus;
                        }
                        else
                        {
                            lblProgress.Text = currentText + "    " + scheduleStatus;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"更新定时处理状态显示时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 设置冲突处理下拉框
        /// </summary>
        private void SetupConflictHandlingComboBox()
        {
            // 设置下拉框文字居中显示
            cmbConflictHandling.DrawMode = DrawMode.OwnerDrawFixed;
            cmbConflictHandling.DrawItem += CmbConflictHandling_DrawItem;
        }

        /// <summary>
        /// 冲突处理下拉框绘制事件
        /// </summary>
        private void CmbConflictHandling_DrawItem(object? sender, DrawItemEventArgs e)
        {
            if (e.Index < 0) return;

            if (sender is not ComboBox comboBox) return;

            e.DrawBackground();

            var text = comboBox.Items[e.Index].ToString();
            if (!string.IsNullOrEmpty(text))
            {
                var textBounds = e.Bounds;
                var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;

                // 使用系统颜色
                var textColor = (e.State & DrawItemState.Selected) != 0
                    ? SystemColors.HighlightText
                    : SystemColors.ControlText;

                TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, textColor, textFlags);
            }

            e.DrawFocusRectangle();
        }

        /// <summary>
        /// 设置NumericUpDown控件的文字居中对齐
        /// </summary>
        private static void SetNumericUpDownTextAlign(NumericUpDown numericUpDown)
        {
            try
            {
                // 方法1：通过反射获取内部TextBox控件
                var textBoxField = typeof(NumericUpDown).GetField("upDownEdit",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (textBoxField?.GetValue(numericUpDown) is TextBox textBox)
                {
                    textBox.TextAlign = HorizontalAlignment.Center;
                    return;
                }

                // 方法2：遍历子控件查找TextBox
                foreach (Control control in numericUpDown.Controls)
                {
                    if (control is TextBox tb)
                    {
                        tb.TextAlign = HorizontalAlignment.Center;
                        return;
                    }
                }

                // 方法3：使用Timer延迟设置
                var timer = new System.Windows.Forms.Timer
                {
                    Interval = 100
                };
                timer.Tick += (s, e) =>
                {
                    timer.Stop();
                    timer.Dispose();

                    foreach (Control control in numericUpDown.Controls)
                    {
                        if (control is TextBox tb2)
                        {
                            tb2.TextAlign = HorizontalAlignment.Center;
                            break;
                        }
                    }
                };
                timer.Start();
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置数值框文字居中时出错: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 设置路径文本框的拖放功能
        /// </summary>
        private void SetupDragDropForPaths()
        {
            // 启用源路径文本框的拖放功能
            txtSourcePath.AllowDrop = true;
            txtSourcePath.DragEnter += TxtSourcePath_DragEnter;
            txtSourcePath.DragDrop += TxtSourcePath_DragDrop;
            txtSourcePath.DragOver += TxtPath_DragOver;
            txtSourcePath.DragLeave += TxtPath_DragLeave;

            // 启用输出路径文本框的拖放功能
            txtOutputPath.AllowDrop = true;
            txtOutputPath.DragEnter += TxtOutputPath_DragEnter;
            txtOutputPath.DragDrop += TxtOutputPath_DragDrop;
            txtOutputPath.DragOver += TxtPath_DragOver;
            txtOutputPath.DragLeave += TxtPath_DragLeave;
        }

        /// <summary>
        /// 源路径拖拽进入事件
        /// </summary>
        private void TxtSourcePath_DragEnter(object? sender, DragEventArgs e)
        {
            HandleDragEnter(e);
        }

        /// <summary>
        /// 输出路径拖拽进入事件
        /// </summary>
        private void TxtOutputPath_DragEnter(object? sender, DragEventArgs e)
        {
            HandleDragEnter(e);
        }

        /// <summary>
        /// 拖拽悬停事件处理
        /// </summary>
        private void TxtPath_DragOver(object? sender, DragEventArgs e)
        {
            // 检查是否为文件夹拖拽
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                var files = (string[]?)e.Data.GetData(DataFormats.FileDrop);
                if (files != null && files.Length > 0 && Directory.Exists(files[0]))
                {
                    e.Effect = DragDropEffects.Copy;

                    // 改变文本框背景色以提供视觉反馈
                    if (sender is TextBox textBox)
                    {
                        textBox.BackColor = Color.LightBlue;
                    }
                }
                else
                {
                    e.Effect = DragDropEffects.None;
                }
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        /// <summary>
        /// 拖拽离开事件处理
        /// </summary>
        private void TxtPath_DragLeave(object? sender, EventArgs e)
        {
            // 恢复文本框背景色
            if (sender is TextBox textBox)
            {
                textBox.BackColor = SystemColors.Window;
            }
        }

        /// <summary>
        /// 处理拖拽进入事件
        /// </summary>
        private static void HandleDragEnter(DragEventArgs e)
        {
            // 检查拖拽的数据是否为文件
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                var files = (string[]?)e.Data.GetData(DataFormats.FileDrop);
                if (files != null && files.Length > 0)
                {
                    // 只允许拖拽文件夹
                    if (Directory.Exists(files[0]))
                    {
                        e.Effect = DragDropEffects.Copy;
                    }
                    else
                    {
                        e.Effect = DragDropEffects.None;
                    }
                }
                else
                {
                    e.Effect = DragDropEffects.None;
                }
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        /// <summary>
        /// 源路径拖放完成事件
        /// </summary>
        private void TxtSourcePath_DragDrop(object? sender, DragEventArgs e)
        {
            HandleDragDrop(sender, e, "源目录");
        }

        /// <summary>
        /// 输出路径拖放完成事件
        /// </summary>
        private void TxtOutputPath_DragDrop(object? sender, DragEventArgs e)
        {
            HandleDragDrop(sender, e, "输出目录");
        }

        /// <summary>
        /// 处理拖放完成事件
        /// </summary>
        private static void HandleDragDrop(object? sender, DragEventArgs e, string pathType)
        {
            try
            {
                if (sender is TextBox textBox)
                {
                    // 恢复文本框背景色
                    textBox.BackColor = SystemColors.Window;

                    if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
                    {
                        var files = (string[]?)e.Data.GetData(DataFormats.FileDrop);
                        if (files != null && files.Length > 0)
                        {
                            var folderPath = files[0];
                            if (Directory.Exists(folderPath))
                            {
                                // 验证路径有效性
                                if (ValidatePath(folderPath, pathType))
                                {
                                    textBox.Text = folderPath;

                                    // 显示成功提示
                                    ShowPathStatusMessage($"{pathType}设置成功: {folderPath}", false);
                                }
                            }
                            else
                            {
                                ShowPathStatusMessage($"拖放的不是有效的文件夹: {folderPath}", true);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置{pathType}时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 源路径文本变更事件
        /// </summary>
        private void TxtSourcePath_TextChanged(object? sender, EventArgs e)
        {
            ValidateAndUpdatePathStatus(txtSourcePath.Text, "源目录");
        }

        /// <summary>
        /// 输出路径文本变更事件
        /// </summary>
        private void TxtOutputPath_TextChanged(object? sender, EventArgs e)
        {
            ValidateAndUpdatePathStatus(txtOutputPath.Text, "输出目录");
        }

        /// <summary>
        /// 验证并更新路径状态
        /// </summary>
        private static void ValidateAndUpdatePathStatus(string path, string pathType)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                return; // 空路径不显示错误
            }

            if (!ValidatePath(path, pathType))
            {
                // 路径无效时的处理已在ValidatePath中完成
                return;
            }

            // 路径有效，可以进行其他处理
            // 例如：自动保存配置、更新UI状态等
        }

        /// <summary>
        /// 验证路径有效性
        /// </summary>
        private static bool ValidatePath(string path, string pathType)
        {
            try
            {
                // 检查路径格式是否有效
                if (!Path.IsPathRooted(path))
                {
                    ShowPathStatusMessage($"{pathType}必须是绝对路径", true);
                    return false;
                }

                // 检查路径是否存在
                if (!Directory.Exists(path))
                {
                    ShowPathStatusMessage($"{pathType}不存在: {path}", true);
                    return false;
                }

                // 检查路径访问权限
                try
                {
                    var testPath = Path.Combine(path, "test_access_" + Guid.NewGuid().ToString("N")[..8]);
                    Directory.CreateDirectory(testPath);
                    Directory.Delete(testPath);
                }
                catch (UnauthorizedAccessException)
                {
                    ShowPathStatusMessage($"没有访问{pathType}的权限: {path}", true);
                    return false;
                }
                catch (Exception ex)
                {
                    ShowPathStatusMessage($"无法访问{pathType}: {ex.Message}", true);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"验证{pathType}时发生错误: {ex.Message}", true);
                return false;
            }
        }

        /// <summary>
        /// 显示路径状态消息
        /// </summary>
        private static void ShowPathStatusMessage(string message, bool isError)
        {
            // 在状态栏或其他位置显示消息
            // 这里暂时使用控制台输出，后续可以改为状态栏显示
            if (isError)
            {
                Console.WriteLine($"[错误] {message}");
                // 可以考虑在UI上显示错误状态
            }
            else
            {
                Console.WriteLine($"[信息] {message}");
                // 可以考虑在UI上显示成功状态
            }
        }

        /// <summary>
        /// 线程数变更事件
        /// </summary>
        private void NumThreadCount_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                // 验证线程数范围
                if (numThreadCount.Value < 1 || numThreadCount.Value > 16)
                {
                    ShowPathStatusMessage($"线程数应在1-16之间，当前值: {numThreadCount.Value}", true);
                    return;
                }

                // 自动保存配置
                SaveProcessingSettings();
                ShowPathStatusMessage($"线程数已设置为: {numThreadCount.Value}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置线程数时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 重试次数变更事件
        /// </summary>
        private void NumRetryCount_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                // 验证重试次数范围
                if (numRetryCount.Value < 0 || numRetryCount.Value > 10)
                {
                    ShowPathStatusMessage($"重试次数应在0-10之间，当前值: {numRetryCount.Value}", true);
                    return;
                }

                // 自动保存配置
                SaveProcessingSettings();
                ShowPathStatusMessage($"重试次数已设置为: {numRetryCount.Value}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置重试次数时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 批处理数量变更事件
        /// </summary>
        private void NumBatchSize_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                // 验证批处理数量范围
                if (numBatchSize.Value < 1 || numBatchSize.Value > 1000)
                {
                    ShowPathStatusMessage($"批处理数量应在1-1000之间，当前值: {numBatchSize.Value}", true);
                    return;
                }

                // 自动保存配置
                SaveProcessingSettings();
                ShowPathStatusMessage($"批处理数量已设置为: {numBatchSize.Value}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置批处理数量时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 复制文件选项变更事件
        /// </summary>
        private void RadioCopy_CheckedChanged(object? sender, EventArgs e)
        {
            if (radioCopy.Checked)
            {
                try
                {
                    SaveProcessingSettings();
                    ShowPathStatusMessage("已设置为复制文件模式", false);
                }
                catch (Exception ex)
                {
                    ShowPathStatusMessage($"设置复制模式时发生错误: {ex.Message}", true);
                }
            }
        }

        /// <summary>
        /// 移动文件选项变更事件
        /// </summary>
        private void RadioMove_CheckedChanged(object? sender, EventArgs e)
        {
            if (radioMove.Checked)
            {
                try
                {
                    SaveProcessingSettings();
                    ShowPathStatusMessage("已设置为移动文件模式", false);
                }
                catch (Exception ex)
                {
                    ShowPathStatusMessage($"设置移动模式时发生错误: {ex.Message}", true);
                }
            }
        }

        /// <summary>
        /// 直接处理源文件选项变更事件
        /// </summary>
        private void ChkDeleteSource_CheckedChanged(object? sender, EventArgs e)
        {
            try
            {
                SaveProcessingSettings();
                var mode = chkDeleteSource.Checked ? "直接处理源文件" : "处理副本文件";
                ShowPathStatusMessage($"已设置为: {mode}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置处理模式时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 冲突处理方式变更事件
        /// </summary>
        private void CmbConflictHandling_SelectedIndexChanged(object? sender, EventArgs e)
        {
            try
            {
                SaveProcessingSettings();
                var conflictTypes = new[] { "自动重命名", "覆盖现有文件", "跳过冲突文件", "询问用户" };
                if (cmbConflictHandling.SelectedIndex >= 0 && cmbConflictHandling.SelectedIndex < conflictTypes.Length)
                {
                    ShowPathStatusMessage($"冲突处理已设置为: {conflictTypes[cmbConflictHandling.SelectedIndex]}", false);
                }
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置冲突处理方式时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 包含子目录选项变更事件
        /// </summary>
        private void ChkIncludeSubfolders_CheckedChanged(object? sender, EventArgs e)
        {
            try
            {
                SaveProcessingSettings();
                var mode = chkIncludeSubfolders.Checked ? "包含子目录" : "仅处理当前目录";
                ShowPathStatusMessage($"已设置为: {mode}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置子目录处理模式时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 保持目录结构选项变更事件
        /// </summary>
        private void ChkKeepStructure_CheckedChanged(object? sender, EventArgs e)
        {
            try
            {
                SaveProcessingSettings();
                var mode = chkKeepStructure.Checked ? "保持原目录结构" : "平铺到输出目录";
                ShowPathStatusMessage($"已设置为: {mode}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置目录结构模式时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 保存处理设置（仅保存处理相关设置，不保存窗体设置）
        /// </summary>
        private void SaveProcessingSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();

                // 保存路径设置
                config.PathSettings.SourcePath = txtSourcePath.Text;
                config.PathSettings.OutputPath = txtOutputPath.Text;
                config.PathSettings.IncludeSubfolders = chkIncludeSubfolders.Checked;
                config.PathSettings.KeepDirectoryStructure = chkKeepStructure.Checked;

                // 保存处理设置
                config.ProcessSettings.CopyFiles = radioCopy.Checked;
                config.ProcessSettings.ProcessSourceDirectly = chkDeleteSource.Checked;
                config.ProcessSettings.ThreadCount = (int)numThreadCount.Value;
                config.ProcessSettings.RetryCount = (int)numRetryCount.Value;
                config.ProcessSettings.BatchSize = (int)numBatchSize.Value;

                // 保存全局冲突处理设置
                if (cmbConflictHandling.SelectedIndex >= 0)
                {
                    config.ProcessSettings.GlobalConflictHandling = (FilenameConflictHandlingType)cmbConflictHandling.SelectedIndex;
                }

                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"保存处理设置失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 开始处理
        /// </summary>
        private async void StartProcessing()
        {
            try
            {
                // 记录用户操作日志
                Services.LogService.Instance.LogFileProcessDetail("=== 用户点击开始处理按钮 ===");
                Services.LogService.Instance.LogFileProcessDetail($"源路径: {txtSourcePath.Text}");
                Services.LogService.Instance.LogFileProcessDetail($"输出路径: {txtOutputPath.Text}");
                Services.LogService.Instance.LogFileProcessDetail($"包含子文件夹: {chkIncludeSubfolders.Checked}");
                Services.LogService.Instance.LogFileProcessDetail($"保持目录结构: {chkKeepStructure.Checked}");
                Services.LogService.Instance.LogFileProcessDetail($"处理模式: {(chkDeleteSource.Checked ? "直接处理源文件" : radioCopy.Checked ? "复制后处理" : "移动后处理")}");
                Services.LogService.Instance.LogFileProcessDetail($"线程数: {numThreadCount.Value}");
                Services.LogService.Instance.LogFileProcessDetail($"重试次数: {numRetryCount.Value}");
                Services.LogService.Instance.LogFileProcessDetail($"批处理大小: {numBatchSize.Value}");

                // 检查是否已在处理中
                if (_isProcessing)
                {
                    MessageBox.Show("已有处理任务在进行中！", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 验证路径设置
                if (string.IsNullOrWhiteSpace(txtSourcePath.Text))
                {
                    MessageBox.Show("请选择源目录", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtOutputPath.Text))
                {
                    MessageBox.Show("请选择输出目录", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 验证路径有效性
                if (!ValidatePath(txtSourcePath.Text, "源目录") ||
                    !ValidatePath(txtOutputPath.Text, "输出目录"))
                {
                    MessageBox.Show("请检查路径设置是否正确", "路径验证失败",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 检查是否有启用的功能
                bool hasEnabledFunction = false;
                for (int i = 0; i < functionCheckBoxes.Length; i++)
                {
                    if (functionCheckBoxes[i].Checked)
                    {
                        hasEnabledFunction = true;
                        break;
                    }
                }

                if (!hasEnabledFunction)
                {
                    MessageBox.Show("请至少选择一个处理功能", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 直接开始处理，不需要确认（频繁操作无需确认）

                // 重置统计显示
                ResetStatsDisplay();

                // 更新处理状态
                _isProcessing = true;
                UpdateActionButtonStates();

                // 启动统计更新定时器
                _statsUpdateTimer?.Start();

                // 保存当前配置
                SaveConfiguration();

                // 创建处理选项
                var options = CreateProcessingOptions();

                // 获取文件处理服务实例
                _fileProcessingService = FileProcessingService.Instance;

                // 先取消之前的订阅，避免重复订阅导致事件被多次触发
                UnsubscribeFromFileProcessingEvents();

                // 订阅事件
                _fileProcessingService.ProgressChanged += OnProcessingProgressChanged;
                _fileProcessingService.FileProcessed += OnFileProcessed;
                _fileProcessingService.ProcessCompleted += OnProcessingCompleted;

                // 显示状态消息
                ShowPathStatusMessage("开始处理文件...", false);

                // 开始异步处理
                await _fileProcessingService.StartProcessingAsync(options);
            }
            catch (Exception ex)
            {
                // 停止统计定时器
                _statsUpdateTimer?.Stop();

                _isProcessing = false;
                // 取消事件订阅
                UnsubscribeFromFileProcessingEvents();
                UpdateActionButtonStates();
                MessageBox.Show($"开始处理时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示定时处理对话框
        /// </summary>
        private void ShowScheduleDialog()
        {
            try
            {
                // 创建默认设置或从现有配置加载
                var settings = new ScheduleSettings
                {
                    Enabled = false,
                    ScheduleMode = 1, // 默认指定时间启动
                    RunFrequency = 2, // 默认每天
                    Hour = DateTime.Now.Hour,
                    Minute = DateTime.Now.Minute,
                    Second = 0
                };

                // 显示设置窗体
                using var settingsForm = new Forms.ScheduleSettingsForm(settings);
                if (settingsForm.ShowDialog(this) == DialogResult.OK)
                {
                    // 获取用户设置
                    var userSettings = settingsForm.ScheduleSettings;

                    if (userSettings.Enabled)
                    {
                        // 转换为处理配置
                        var config = userSettings.ToScheduleProcessingConfig();

                        // 启动定时处理
                        if (_scheduleProcessingService?.StartScheduledProcessing(config) == true)
                        {
                            MessageBox.Show("定时处理已启动！", "成功",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("启动定时处理失败！", "错误",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        // 如果禁用，停止现有的定时处理
                        if (_scheduleProcessingService?.IsRunning == true)
                        {
                            _scheduleProcessingService.StopScheduledProcessing();
                            MessageBox.Show("定时处理已停止！", "提示",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开定时处理设置时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 停止处理
        /// </summary>
        private void StopProcessing()
        {
            try
            {
                var isScheduleRunning = _scheduleProcessingService?.IsRunning == true;

                if (!_isProcessing && !isScheduleRunning)
                {
                    MessageBox.Show("当前没有正在运行的处理任务或定时处理", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 构建确认消息
                var message = "";
                if (_isProcessing && isScheduleRunning)
                {
                    message = "确定要停止当前处理任务和定时处理吗？";
                }
                else if (_isProcessing)
                {
                    message = "确定要停止当前处理任务吗？";
                }
                else if (isScheduleRunning)
                {
                    message = "确定要停止定时处理吗？";
                }

                var result = MessageBox.Show(message, "确认",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 停止文件处理服务
                    if (_isProcessing)
                    {
                        _fileProcessingService?.StopProcessing();
                        _statsUpdateTimer?.Stop();
                        _isProcessing = false;
                        // 取消事件订阅
                        UnsubscribeFromFileProcessingEvents();
                        ShowPathStatusMessage("手动处理已停止", false);
                    }

                    // 停止定时处理服务
                    if (isScheduleRunning)
                    {
                        _scheduleProcessingService?.StopScheduledProcessing();
                        ShowPathStatusMessage("定时处理已停止", false);
                    }

                    // 更新按钮状态
                    UpdateActionButtonStates();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"停止处理时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新操作按钮状态
        /// </summary>
        private void UpdateActionButtonStates()
        {
            if (actionButtons != null && actionButtons.Length > 2)
            {
                // 开始处理按钮：任务运行时或定时处理运行时不可用
                actionButtons[0].Enabled = !_isProcessing; // 开始处理

                // 定时处理按钮：任务运行时不可用
                actionButtons[1].Enabled = !_isProcessing; // 定时处理

                // 停止处理按钮：任务运行时或定时处理运行时可用
                var isScheduleRunning = _scheduleProcessingService?.IsRunning == true;
                actionButtons[2].Enabled = _isProcessing || isScheduleRunning;  // 停止处理
            }

            // 同时更新菜单状态
            UpdateMenuStates();
        }

        /// <summary>
        /// 更新菜单项状态
        /// </summary>
        private void UpdateMenuStates()
        {
            try
            {
                // 操作菜单状态更新
                开始处理ToolStripMenuItem.Enabled = !_isProcessing;
                停止处理ToolStripMenuItem.Enabled = _isProcessing;
                导出配置ToolStripMenuItem.Enabled = !_isProcessing;
                导入配置ToolStripMenuItem.Enabled = !_isProcessing;
                重置配置ToolStripMenuItem.Enabled = !_isProcessing;

                // 设置菜单在处理过程中部分禁用
                定时设置ToolStripMenuItem.Enabled = !_isProcessing;
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"更新菜单状态时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 创建处理选项
        /// </summary>
        private ProcessingOptions CreateProcessingOptions()
        {
            var config = ConfigService.Instance.GetConfig();

            return new ProcessingOptions
            {
                SourcePath = txtSourcePath.Text,
                OutputPath = txtOutputPath.Text,
                IncludeSubfolders = chkIncludeSubfolders.Checked,
                KeepDirectoryStructure = chkKeepStructure.Checked,
                CopyFiles = radioCopy.Checked,
                ProcessSourceDirectly = chkDeleteSource.Checked,
                ConflictHandling = config.ProcessSettings.GlobalConflictHandling,
                ThreadCount = (int)numThreadCount.Value,
                RetryCount = (int)numRetryCount.Value,
                BatchSize = (int)numBatchSize.Value,
                SupportedFormats = config.ProcessSettings.SupportedFormats
            };
        }

        /// <summary>
        /// 处理进度变更事件 - 优化更新频率避免UI卡顿
        /// </summary>
        private void OnProcessingProgressChanged(object? sender, ProcessProgressEventArgs e)
        {
            if (InvokeRequired)
            {
                // 使用BeginInvoke异步调用，避免阻塞工作线程
                BeginInvoke(new Action(() => OnProcessingProgressChanged(sender, e)));
                return;
            }

            // 更新当前统计数据
            _currentStats = e.Stats;

            // 不在此处立即更新统计显示，由定时器统一更新，避免频繁UI刷新
            // UpdateStatsDisplay(e.Stats); // 注释掉，改由定时器更新

            // 显示简要进度信息（降低更新频率）
            var processedCount = e.Stats.SuccessCount + e.Stats.FailureCount;
            if (processedCount % 5 == 0 || e.ProgressPercentage >= 100) // 每处理5个文件或完成时才更新状态
            {
                ShowPathStatusMessage($"处理进度: {e.ProgressPercentage}% ({processedCount}/{e.Stats.TotalFiles})", false);
            }
        }

        /// <summary>
        /// 文件处理完成事件
        /// </summary>
        private void OnFileProcessed(object? sender, FileProcessedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnFileProcessed(sender, e)));
                return;
            }

            string message = e.IsSuccess ? $"已处理: {e.FileName}" : $"处理失败: {e.FileName} - {e.ErrorMessage}";
            ShowPathStatusMessage(message, !e.IsSuccess);
        }

        /// <summary>
        /// 处理完成事件
        /// </summary>
        private void OnProcessingCompleted(object? sender, ProcessCompletedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnProcessingCompleted(sender, e)));
                return;
            }

            // 记录处理完成日志
            var successCount = e.Stats.SuccessCount;
            var failureCount = e.Stats.FailureCount;
            var totalCount = e.Stats.TotalFiles;
            var processingTime = e.Stats.ElapsedTime;

            LogService.Instance.LogProcessComplete(
                $"批量处理完成 - 总计: {totalCount} 个文件, 成功: {successCount} 个, 失败: {failureCount} 个, 耗时: {processingTime:hh\\:mm\\:ss}");

            // 停止统计更新定时器
            _statsUpdateTimer?.Stop();

            // 更新处理状态
            _isProcessing = false;
            UpdateActionButtonStates();

            // 取消事件订阅，避免重复触发
            UnsubscribeFromFileProcessingEvents();

            // 更新最终统计数据
            _currentStats = e.Stats;
            UpdateStatsDisplay(e.Stats);

            // 显示完成对话框
            var stats = e.Stats;
            var endTime = stats.EndTime ?? DateTime.Now;
            var elapsedTime = endTime - stats.StartTime;
            var successRate = stats.TotalFiles > 0 ? (double)stats.SuccessCount / stats.TotalFiles * 100 : 0;

            var message = $"处理完成！\n\n" +
                         $"总文件数: {stats.TotalFiles}\n" +
                         $"成功处理: {stats.SuccessCount} ({successRate:F1}%)\n" +
                         $"处理失败: {stats.FailureCount}\n" +
                         $"总耗时: {FormatTimeSpan(elapsedTime)}\n" +
                         $"平均速度: {stats.ProcessingSpeed:F1} 文件/分钟";

            MessageBox.Show(message, "处理完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            ShowPathStatusMessage("处理完成", false);
        }

        /// <summary>
        /// 取消文件处理服务事件订阅
        /// </summary>
        private void UnsubscribeFromFileProcessingEvents()
        {
            if (_fileProcessingService != null)
            {
                _fileProcessingService.ProgressChanged -= OnProcessingProgressChanged;
                _fileProcessingService.FileProcessed -= OnFileProcessed;
                _fileProcessingService.ProcessCompleted -= OnProcessingCompleted;
            }
        }

        /// <summary>
        /// 显示日志设置
        /// </summary>
        private void ShowLogSettings()
        {
            try
            {
                // 获取当前配置中的日志设置
                var config = ConfigService.Instance.GetConfig();
                using var logSettingsForm = new Forms.LogSettingsForm(config.LogSettings);
                var result = logSettingsForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    // 更新配置
                    config.LogSettings = logSettingsForm.LogSettings;
                    ConfigService.Instance.UpdateConfig(config);

                    // 显示状态消息
                    var enabledCount = config.LogSettings.EnabledLogTypes.Count(kvp => kvp.Value);
                    var message = config.LogSettings.EnableLogging
                        ? $"日志设置已更新: 总开关已启用，已启用 {enabledCount} 种日志类型"
                        : "日志设置已更新: 总开关已禁用";

                    ShowPathStatusMessage(message, false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开日志设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void ClearLogs()
        {
            var result = MessageBox.Show("确定要清空所有日志文件吗？\n\n此操作将删除Log目录下的所有日志文件，且无法恢复。", "确认",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    string logDirectory = Path.Combine(Application.StartupPath, "Log");

                    if (Directory.Exists(logDirectory))
                    {
                        // 获取所有日志文件
                        var logFiles = Directory.GetFiles(logDirectory, "*.log", SearchOption.AllDirectories);
                        int deletedCount = 0;
                        int failedCount = 0;

                        foreach (string logFile in logFiles)
                        {
                            try
                            {
                                File.Delete(logFile);
                                deletedCount++;
                            }
                            catch (Exception ex)
                            {
                                failedCount++;
                                // 记录删除失败的文件，但继续删除其他文件
                                Console.WriteLine($"删除日志文件失败: {logFile}, 错误: {ex.Message}");
                            }
                        }

                        // 尝试删除空的子目录
                        try
                        {
                            var subDirectories = Directory.GetDirectories(logDirectory);
                            foreach (string subDir in subDirectories)
                            {
                                if (Directory.GetFiles(subDir).Length == 0 && Directory.GetDirectories(subDir).Length == 0)
                                {
                                    Directory.Delete(subDir);
                                }
                            }
                        }
                        catch
                        {
                            // 忽略删除目录时的错误
                        }

                        string message = $"日志清空完成！\n\n成功删除: {deletedCount} 个文件";
                        if (failedCount > 0)
                        {
                            message += $"\n删除失败: {failedCount} 个文件";
                        }

                        MessageBox.Show(message, "清空完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ShowPathStatusMessage($"已清空 {deletedCount} 个日志文件", false);
                    }
                    else
                    {
                        MessageBox.Show("Log目录不存在，无需清空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"清空日志时发生错误：{ex.Message}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 导出配置
        /// </summary>
        private void ExportConfiguration()
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择配置导出目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                // 先保存当前配置
                SaveConfiguration();

                if (ConfigService.Instance.ExportConfig(dialog.SelectedPath))
                {
                    MessageBox.Show("配置导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("配置导出失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 导入配置
        /// </summary>
        private void ImportConfiguration()
        {
            // 询问用户选择导入方式
            var importChoice = MessageBox.Show("请选择导入方式：\n\n是(Y) - 从压缩包导入\n否(N) - 从文件夹导入",
                "选择导入方式", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

            if (importChoice == DialogResult.Cancel)
                return;

            string importPath = "";
            bool isZipFile = false;

            if (importChoice == DialogResult.Yes)
            {
                // 从压缩包导入
                using var fileDialog = new OpenFileDialog();
                fileDialog.Title = "选择配置压缩包";
                fileDialog.Filter = "压缩包文件 (*.zip)|*.zip|所有文件 (*.*)|*.*";
                fileDialog.FilterIndex = 1;

                if (fileDialog.ShowDialog() == DialogResult.OK)
                {
                    importPath = fileDialog.FileName;
                    isZipFile = true;
                }
                else
                {
                    return;
                }
            }
            else
            {
                // 从文件夹导入
                using var folderDialog = new FolderBrowserDialog();
                folderDialog.Description = "选择配置导入目录";
                folderDialog.UseDescriptionForTitle = true;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    importPath = folderDialog.SelectedPath;
                }
                else
                {
                    return;
                }
            }

            // 确认导入
            var confirmMessage = isZipFile ?
                $"确定要从压缩包导入配置吗？\n\n文件：{Path.GetFileName(importPath)}\n\n导入配置将覆盖当前设置，是否继续？" :
                $"确定要从文件夹导入配置吗？\n\n目录：{importPath}\n\n导入配置将覆盖当前设置，是否继续？";

            var result = MessageBox.Show(confirmMessage, "确认导入",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    if (ConfigService.Instance.ImportConfig(importPath))
                    {
                        MessageBox.Show("配置导入成功！正在重新加载界面设置...", "导入成功",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadConfiguration();
                        ShowPathStatusMessage("配置导入成功", false);
                    }
                    else
                    {
                        MessageBox.Show("配置导入失败！请检查文件格式是否正确。", "导入失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导入配置时发生错误：{ex.Message}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 重置配置
        /// </summary>
        private void ResetConfiguration()
        {
            // 确认重置
            var result = MessageBox.Show("确定要重置所有配置为默认值吗？\n\n此操作将清除所有自定义设置，包括：\n• 路径设置\n• 处理设置\n• 功能配置\n• 所有模块的详细设置\n\n重置后软件将自动关闭，下次启动时将使用默认配置，是否继续？",
                "确认重置配置", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    // 调用配置服务的重置方法
                    if (ConfigService.Instance.ResetAllConfigs())
                    {
                        // 设置配置重置标志，防止关闭时覆盖默认配置
                        _configResetExecuted = true;

                        MessageBox.Show("配置重置成功！\n\n软件将自动关闭，请重新启动软件以使用默认配置。", "重置成功",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 记录重置操作日志
                        LogService.Instance.LogConfigChange("用户执行配置重置操作，软件即将关闭");

                        // 关闭软件
                        Application.Exit();
                    }
                    else
                    {
                        MessageBox.Show("配置重置失败！请检查文件权限或重新启动软件。", "重置失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"重置配置时发生错误：{ex.Message}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 打开源目录
        /// </summary>
        private void OpenSourceDirectory()
        {
            if (!string.IsNullOrWhiteSpace(txtSourcePath.Text) && Directory.Exists(txtSourcePath.Text))
            {
                System.Diagnostics.Process.Start("explorer.exe", txtSourcePath.Text);
            }
            else
            {
                MessageBox.Show("源目录不存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 打开输出目录
        /// </summary>
        private void OpenOutputDirectory()
        {
            if (!string.IsNullOrWhiteSpace(txtOutputPath.Text) && Directory.Exists(txtOutputPath.Text))
            {
                System.Diagnostics.Process.Start("explorer.exe", txtOutputPath.Text);
            }
            else
            {
                MessageBox.Show("输出目录不存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();

                // 加载路径设置
                txtSourcePath.Text = config.PathSettings.SourcePath;
                txtOutputPath.Text = config.PathSettings.OutputPath;
                chkIncludeSubfolders.Checked = config.PathSettings.IncludeSubfolders;
                chkKeepStructure.Checked = config.PathSettings.KeepDirectoryStructure;

                // 加载处理设置
                radioCopy.Checked = config.ProcessSettings.CopyFiles;
                radioMove.Checked = !config.ProcessSettings.CopyFiles;
                chkDeleteSource.Checked = config.ProcessSettings.ProcessSourceDirectly;
                numThreadCount.Value = config.ProcessSettings.ThreadCount;
                numRetryCount.Value = config.ProcessSettings.RetryCount;
                numBatchSize.Value = config.ProcessSettings.BatchSize;

                // 加载全局冲突处理设置
                cmbConflictHandling.SelectedIndex = (int)config.ProcessSettings.GlobalConflictHandling;

                // 加载功能启用状态
                for (int i = 0; i < functionNames.Length; i++)
                {
                    if (config.FunctionEnabled.ContainsKey(functionNames[i]))
                    {
                        functionCheckBoxes[i].Checked = config.FunctionEnabled[functionNames[i]];
                    }
                }

                // 加载窗体设置
                if (config.FormSettings.LocationX >= 0 && config.FormSettings.LocationY >= 0)
                {
                    StartPosition = FormStartPosition.Manual;
                    Location = new Point(config.FormSettings.LocationX, config.FormSettings.LocationY);
                }

                Size = new Size(config.FormSettings.Width, config.FormSettings.Height);
                WindowState = (FormWindowState)config.FormSettings.WindowState;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"配置加载失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveConfiguration()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();

                // 保存路径设置
                config.PathSettings.SourcePath = txtSourcePath.Text;
                config.PathSettings.OutputPath = txtOutputPath.Text;
                config.PathSettings.IncludeSubfolders = chkIncludeSubfolders.Checked;
                config.PathSettings.KeepDirectoryStructure = chkKeepStructure.Checked;

                // 保存处理设置
                config.ProcessSettings.CopyFiles = radioCopy.Checked;
                config.ProcessSettings.ProcessSourceDirectly = chkDeleteSource.Checked;
                config.ProcessSettings.ThreadCount = (int)numThreadCount.Value;
                config.ProcessSettings.RetryCount = (int)numRetryCount.Value;
                config.ProcessSettings.BatchSize = (int)numBatchSize.Value;

                // 保存全局冲突处理设置
                if (cmbConflictHandling.SelectedIndex >= 0)
                {
                    config.ProcessSettings.GlobalConflictHandling = (FilenameConflictHandlingType)cmbConflictHandling.SelectedIndex;
                }

                // 保存功能启用状态
                for (int i = 0; i < functionNames.Length; i++)
                {
                    config.FunctionEnabled[functionNames[i]] = functionCheckBoxes[i].Checked;
                }

                // 保存窗体设置
                if (WindowState == FormWindowState.Normal)
                {
                    config.FormSettings.LocationX = Location.X;
                    config.FormSettings.LocationY = Location.Y;
                    config.FormSettings.Width = Size.Width;
                    config.FormSettings.Height = Size.Height;
                }
                config.FormSettings.WindowState = (int)WindowState;

                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"配置保存失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void MainForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            try
            {
                // 记录程序关闭日志
                LogService.Instance.LogSystemStatus("PPT批量处理工具正在关闭");

                // 停止统计更新定时器
                _statsUpdateTimer?.Stop();
                _statsUpdateTimer?.Dispose();

                // 停止文件处理服务
                if (_isProcessing)
                {
                    _fileProcessingService?.StopProcessing();
                    // 取消事件订阅
                    UnsubscribeFromFileProcessingEvents();
                }

                // 停止定时处理服务
                _scheduleProcessingService?.StopScheduledProcessing();
                _scheduleProcessingService?.Dispose();

                // 保存配置（如果没有执行配置重置）
                if (!_configResetExecuted)
                {
                    SaveConfiguration();
                }
                else
                {
                    LogService.Instance.LogConfigChange("配置重置后关闭软件，跳过配置保存以保持默认配置");
                }

                // 释放日志服务资源并刷新剩余日志
                LogService.Instance.Dispose();
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止窗体关闭
                ShowPathStatusMessage($"关闭窗体时发生错误: {ex.Message}", true);
            }
        }

        #region 菜单事件处理方法

        #region 文件菜单事件











        /// <summary>
        /// 退出菜单点击事件
        /// </summary>
        private void 退出_Click(object? sender, EventArgs e)
        {
            try
            {
                // 关闭应用程序
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"退出程序时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开源目录菜单点击事件
        /// </summary>
        private void 打开源目录_Click(object? sender, EventArgs e)
        {
            OpenSourceDirectory();
        }

        /// <summary>
        /// 打开输出目录菜单点击事件
        /// </summary>
        private void 打开输出目录_Click(object? sender, EventArgs e)
        {
            OpenOutputDirectory();
        }

        /// <summary>
        /// 打开日志目录菜单点击事件
        /// </summary>
        private void 打开日志目录_Click(object? sender, EventArgs e)
        {
            try
            {
                var logDirectory = Path.Combine(Application.StartupPath, "Log");

                // 确保日志目录存在
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // 打开日志目录
                System.Diagnostics.Process.Start("explorer.exe", logDirectory);
                ShowPathStatusMessage($"已打开日志目录: {logDirectory}", false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开日志目录时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 操作菜单事件

        /// <summary>
        /// 开始处理菜单点击事件
        /// </summary>
        private void 开始处理_Click(object? sender, EventArgs e)
        {
            StartProcessing();
        }

        /// <summary>
        /// 停止处理菜单点击事件
        /// </summary>
        private void 停止处理_Click(object? sender, EventArgs e)
        {
            StopProcessing();
        }

        /// <summary>
        /// 导出配置菜单点击事件
        /// </summary>
        private void 导出配置_Click(object? sender, EventArgs e)
        {
            ExportConfiguration();
        }

        /// <summary>
        /// 导入配置菜单点击事件
        /// </summary>
        private void 导入配置_Click(object? sender, EventArgs e)
        {
            ImportConfiguration();
        }

        /// <summary>
        /// 重置配置菜单点击事件
        /// </summary>
        private void 重置配置_Click(object? sender, EventArgs e)
        {
            ResetConfiguration();
        }

        #endregion

        #region 设置菜单事件

        /// <summary>
        /// 日志设置菜单点击事件
        /// </summary>
        private void 日志设置_Click(object? sender, EventArgs e)
        {
            ShowLogSettings();
        }

        /// <summary>
        /// 定时设置菜单点击事件
        /// </summary>
        private void 定时设置_Click(object? sender, EventArgs e)
        {
            ShowScheduleDialog();
        }



        #endregion

        #region 帮助菜单事件

        /// <summary>
        /// 使用帮助菜单点击事件
        /// </summary>
        private void 使用帮助_Click(object? sender, EventArgs e)
        {
            try
            {
                var helpContent = @"PPT批量处理工具使用帮助

主要功能：
1. 页面设置 - 设置幻灯片尺寸和方向
2. 内容删除 - 删除指定内容和格式
3. 内容替换 - 替换文本、形状、字体等
4. PPT格式设置 - 全局格式化设置
5. 匹配段落格式 - 根据条件格式化段落
6. PPT页脚设置 - 设置页眉页脚内容
7. 文档属性 - 设置文档属性信息
8. 文件名替换 - 批量重命名文件
9. PPT格式转换 - 转换为PDF、图片等格式

快捷键：
- F1: 显示帮助
- F5: 开始处理
- Ctrl+F5: 停止处理
- Alt+F4: 退出程序

使用步骤：
1. 选择源目录和输出目录
2. 配置处理设置（线程数、重试次数等）
3. 选择需要的功能并进行详细配置
4. 点击开始处理按钮开始批量处理";

                MessageBox.Show(helpContent, "使用帮助", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示帮助时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 快捷键说明菜单点击事件
        /// </summary>
        private void 快捷键说明_Click(object? sender, EventArgs e)
        {
            try
            {
                var shortcutContent = @"快捷键说明

处理操作：
- F5: 开始处理
- Ctrl+F5: 停止处理

系统操作：
- Alt+F4: 退出程序

帮助：
- F1: 显示使用帮助

注意：
- 快捷键在主窗口获得焦点时有效
- 某些快捷键在处理过程中可能被禁用";

                MessageBox.Show(shortcutContent, "快捷键说明", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示快捷键说明时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 检查更新菜单点击事件 - 跳转到更新网站
        /// </summary>
        private void 检查更新_Click(object? sender, EventArgs e)
        {
            try
            {
                // 打开更新网站
                var updateUrl = "https://www.yzwk.com/";
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = updateUrl,
                    UseShellExecute = true // 使用系统默认浏览器打开
                });

                ShowPathStatusMessage("已打开更新网站", false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开更新网站时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 关于软件菜单点击事件
        /// </summary>
        private void 关于软件_Click(object? sender, EventArgs e)
        {
            try
            {
                var aboutContent = @"PPT批量处理工具

版本: 1.0.0
开发框架: .NET 6.0
UI框架: Windows Forms

功能特点:
• 支持多种PPT格式处理
• 批量文档内容编辑
• 格式转换和优化
• 多线程并行处理
• 定时任务调度
• 详细日志记录

技术支持:
如有问题或建议，请联系开发团队。
QQ15503397 WWW.YZWK.COM

版权所有 © 2025";

                MessageBox.Show(aboutContent, "关于PPT批量处理工具", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示关于信息时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #endregion


    }
}
