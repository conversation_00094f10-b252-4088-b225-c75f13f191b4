/// <summary>
/// PPT格式转换设置窗体 - 设计器生成代码部分
/// 定义窗体的UI布局、控件属性和基本样式设置
/// 包含标签页控件、按钮控件的位置、大小、颜色等视觉属性配置
/// 支持响应式布局和现代化UI设计风格
/// </summary>

namespace PPTPiliangChuli.Forms
{
    partial class PPTFormatConversionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(PPTFormatConversionForm));
            tabControlMain = new TabControl();
            tabPagePDF = new TabPage();
            tabPageImage = new TabPage();
            tabPageWeb = new TabPage();
            tabPageOther = new TabPage();
            btnOK = new Button();
            btnCancel = new Button();
            tabControlMain.SuspendLayout();
            SuspendLayout();
            // 
            // tabControlMain
            // 
            tabControlMain.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            tabControlMain.Controls.Add(tabPagePDF);
            tabControlMain.Controls.Add(tabPageImage);
            tabControlMain.Controls.Add(tabPageWeb);
            tabControlMain.Controls.Add(tabPageOther);
            tabControlMain.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            tabControlMain.Location = new Point(20, 20);
            tabControlMain.Name = "tabControlMain";
            tabControlMain.SelectedIndex = 0;
            tabControlMain.Size = new Size(1340, 800);
            tabControlMain.TabIndex = 0;
            // 
            // tabPagePDF
            // 
            tabPagePDF.BackColor = Color.FromArgb(252, 252, 252);
            tabPagePDF.Location = new Point(4, 28);
            tabPagePDF.Name = "tabPagePDF";
            tabPagePDF.Padding = new Padding(5);
            tabPagePDF.Size = new Size(1332, 768);
            tabPagePDF.TabIndex = 0;
            tabPagePDF.Text = "转PDF";
            // 
            // tabPageImage
            // 
            tabPageImage.BackColor = Color.FromArgb(252, 252, 252);
            tabPageImage.Location = new Point(4, 28);
            tabPageImage.Name = "tabPageImage";
            tabPageImage.Padding = new Padding(5);
            tabPageImage.Size = new Size(1332, 768);
            tabPageImage.TabIndex = 1;
            tabPageImage.Text = "转图片";
            // 
            // tabPageWeb
            // 
            tabPageWeb.BackColor = Color.FromArgb(252, 252, 252);
            tabPageWeb.Location = new Point(4, 28);
            tabPageWeb.Name = "tabPageWeb";
            tabPageWeb.Padding = new Padding(5);
            tabPageWeb.Size = new Size(1332, 768);
            tabPageWeb.TabIndex = 2;
            tabPageWeb.Text = "转Web";
            // 
            // tabPageOther
            // 
            tabPageOther.BackColor = Color.FromArgb(252, 252, 252);
            tabPageOther.Location = new Point(4, 28);
            tabPageOther.Name = "tabPageOther";
            tabPageOther.Padding = new Padding(5);
            tabPageOther.Size = new Size(1332, 768);
            tabPageOther.TabIndex = 3;
            tabPageOther.Text = "转其他";
            // 
            // btnOK
            // 
            btnOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnOK.BackColor = Color.FromArgb(0, 120, 215);
            btnOK.FlatStyle = FlatStyle.Flat;
            btnOK.Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular, GraphicsUnit.Point);
            btnOK.ForeColor = Color.White;
            btnOK.Location = new Point(1100, 840);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(100, 40);
            btnOK.TabIndex = 1;
            btnOK.Text = "确定";
            btnOK.UseVisualStyleBackColor = false;
            // 
            // btnCancel
            // 
            btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnCancel.BackColor = Color.FromArgb(240, 240, 240);
            btnCancel.DialogResult = DialogResult.Cancel;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Regular, GraphicsUnit.Point);
            btnCancel.ForeColor = Color.FromArgb(60, 60, 60);
            btnCancel.Location = new Point(1210, 840);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(100, 40);
            btnCancel.TabIndex = 2;
            btnCancel.Text = "取消";
            btnCancel.UseVisualStyleBackColor = false;

            // 
            // PPTFormatConversionForm
            // 
            AcceptButton = btnOK;
            AutoScaleDimensions = new SizeF(8F, 19F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(245, 245, 245);
            CancelButton = btnCancel;
            ClientSize = new Size(1400, 900);
            Controls.Add(btnCancel);
            Controls.Add(btnOK);
            Controls.Add(tabControlMain);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "PPTFormatConversionForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "PPT格式转换设置";
            tabControlMain.ResumeLayout(false);
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControlMain;
        private System.Windows.Forms.TabPage tabPagePDF;
        private System.Windows.Forms.TabPage tabPageImage;
        private System.Windows.Forms.TabPage tabPageWeb;
        private System.Windows.Forms.TabPage tabPageOther;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
    }
}
