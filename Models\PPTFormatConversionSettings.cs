/// <summary>
/// PPT格式转换设置模型类
/// 定义PPT文件转换为各种格式的配置参数和选项
/// 包含PDF转换、图片转换、Web转换、其他格式转换的完整设置结构
/// 符合Aspose.Slides API规范，支持批量处理和高级转换选项
/// 提供详细的格式控制、质量设置、布局选项等功能配置
/// </summary>

using System;
using System.Collections.Generic;

namespace PPTPiliangChuli.Models
{
    /// <summary>
    /// PPT格式转换设置 - 主配置类
    /// 包含所有转换格式的设置选项
    /// </summary>
    public class PPTFormatConversionSettings
    {
        /// <summary>
        /// 转PDF设置
        /// </summary>
        public PDFConversionSettings PDFSettings { get; set; } = new();

        /// <summary>
        /// 转图片设置
        /// </summary>
        public ImageConversionSettings ImageSettings { get; set; } = new();

        /// <summary>
        /// 转Web设置
        /// </summary>
        public WebConversionSettings WebSettings { get; set; } = new();

        /// <summary>
        /// 转其他格式设置
        /// </summary>
        public OtherConversionSettings OtherSettings { get; set; } = new();
    }

    /// <summary>
    /// PDF转换设置
    /// </summary>
    public class PDFConversionSettings
    {
        // 基本设置
        public bool EnablePDFConversion { get; set; } = false;
        public bool BatchConversion { get; set; } = true;
        public bool KeepOriginalFormat { get; set; } = true;

        // 转换选项
        public bool UsePageRange { get; set; } = false;
        public int StartPage { get; set; } = 1;
        public int EndPage { get; set; } = 1;
        public int ImageQuality { get; set; } = 90; // 1-100
        public string CompressionLevel { get; set; } = "标准"; // 无压缩、标准、高压缩
        public bool PasswordProtection { get; set; } = false;
        public string Password { get; set; } = "";

        // 布局选项
        public string SlideLayout { get; set; } = "幻灯片布局"; // 幻灯片布局、讲义布局、备注布局、大纲布局
        public bool HandoutMultipleSlides { get; set; } = false;
        public int HandoutSlidesPerPage { get; set; } = 6; // 2,3,4,6,9
        public bool IncludeNotes { get; set; } = false;
        public string NotesPosition { get; set; } = "底部"; // 顶部、底部、左侧、右侧

        // 高级选项
        public bool EmbedFonts { get; set; } = true;
        public bool CompressImages { get; set; } = true;
        public bool PreserveMetadata { get; set; } = true;
        public bool GenerateBookmarks { get; set; } = false;
        public string PDFCompliance { get; set; } = "PDF 1.5"; // PDF 1.4, PDF 1.5, PDF/A-1a, PDF/A-1b
    }

    /// <summary>
    /// 图片转换设置
    /// </summary>
    public class ImageConversionSettings
    {
        // 格式选择
        public bool EnablePNGConversion { get; set; } = false;
        public bool EnableJPEGConversion { get; set; } = false;
        public bool EnableBMPConversion { get; set; } = false;
        public bool EnableTIFFConversion { get; set; } = false;
        public bool EnableSVGConversion { get; set; } = false;

        // 图片质量设置
        public int ImageWidth { get; set; } = 1920;
        public int ImageHeight { get; set; } = 1080;
        public int DPI { get; set; } = 300;
        public int JPEGQuality { get; set; } = 90; // 1-100
        public bool MaintainAspectRatio { get; set; } = true;

        // 输出选项
        public bool ConvertAllSlides { get; set; } = true;
        public bool UseSlideRange { get; set; } = false;
        public int StartSlide { get; set; } = 1;
        public int EndSlide { get; set; } = 1;
        public string NamingPattern { get; set; } = "幻灯片{0}"; // {0}为幻灯片编号
    }

    /// <summary>
    /// Web转换设置
    /// </summary>
    public class WebConversionSettings
    {
        // HTML转换
        public bool EnableHTMLConversion { get; set; } = false;
        public bool EmbedImages { get; set; } = true;
        public bool EmbedCSS { get; set; } = true;
        public bool EmbedJavaScript { get; set; } = true;
        public string HTMLVersion { get; set; } = "HTML5"; // HTML4, HTML5, XHTML
        public bool ResponsiveDesign { get; set; } = true;

        // XAML转换
        public bool EnableXAMLConversion { get; set; } = false;
        public bool IncludeAnimations { get; set; } = false;
        public bool OptimizeForSilverlight { get; set; } = false;
    }

    /// <summary>
    /// 其他格式转换设置
    /// </summary>
    public class OtherConversionSettings
    {
        // 缩略图生成
        public bool EnableThumbnailGeneration { get; set; } = false;
        public int ThumbnailWidth { get; set; } = 200;
        public int ThumbnailHeight { get; set; } = 150;
        public string ThumbnailFormat { get; set; } = "PNG"; // PNG, JPEG, BMP
        public bool GenerateForAllSlides { get; set; } = true;
    }
}
