using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 标题幻灯片布局设置窗体
    /// </summary>
    public partial class TitleSlideLayoutSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 标题幻灯片布局设置
        /// </summary>
        public TitleSlideLayoutSettings Settings { get; private set; } = new TitleSlideLayoutSettings();

        #region 控件字段
        private ComboBox? _cmbTitlePosition, _cmbSubtitlePosition, _cmbTitleAlignment, _cmbSubtitleAlignment;
        private NumericUpDown? _nudTitleFontSize, _nudSubtitleFontSize;
        private CheckBox? _chkShowSubtitle, _chkTitleBold, _chkSubtitleBold, _chkAutoResize;
        private ComboBox? _cmbTitleFontFamily, _cmbSubtitleFontFamily, _cmbLayoutTemplate;
        private Button? _btnTitleFontColor, _btnSubtitleFontColor, _btnBackgroundColor;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;

        // 颜色存储
        private Color _titleFontColor = Color.Black;
        private Color _subtitleFontColor = Color.DarkGray;
        private Color _backgroundColor = Color.White;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TitleSlideLayoutSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TitleSlideLayoutSettingsForm));
            SuspendLayout();
            // 
            // TitleSlideLayoutSettingsForm
            // 
            ClientSize = new Size(778, 664);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            MinimumSize = new Size(800, 720);
            Name = "TitleSlideLayoutSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "标题幻灯片布局设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateLayoutTemplateGroup();
            CreateTitleSettingsGroup();
            CreateSubtitleSettingsGroup();
            CreateBackgroundGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建布局模板组
        /// </summary>
        private void CreateLayoutTemplateGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "布局模板选择",
                Location = new Point(30, 30),
                Size = new Size(740, 100),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            var lblTemplate = new Label
            {
                Text = "模板类型:",
                Location = new Point(30, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbLayoutTemplate = new ComboBox
            {
                Location = new Point(130, 38),
                Size = new Size(200, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 8,
                DropDownHeight = 160
            };

            _btnPreview = new Button
            {
                Text = "预览效果(&P)",
                Location = new Point(350, 38),
                Size = new Size(120, 28),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnPreview.Click += BtnPreview_Click;

            groupBox.Controls.AddRange(new Control[] { lblTemplate, _cmbLayoutTemplate, _btnPreview });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建标题设置组
        /// </summary>
        private void CreateTitleSettingsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "主标题设置",
                Location = new Point(30, 150),
                Size = new Size(740, 200),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            // 第一行：位置和对齐
            var lblPosition = new Label
            {
                Text = "标题位置:",
                Location = new Point(30, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitlePosition = new ComboBox
            {
                Location = new Point(130, 38),
                Size = new Size(140, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            var lblAlignment = new Label
            {
                Text = "文字对齐:",
                Location = new Point(290, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitleAlignment = new ComboBox
            {
                Location = new Point(390, 38),
                Size = new Size(120, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            // 第二行：字体设置
            var lblFont = new Label
            {
                Text = "字体:",
                Location = new Point(30, 80),
                Size = new Size(60, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitleFontFamily = new ComboBox
            {
                Location = new Point(100, 78),
                Size = new Size(140, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 10,
                DropDownHeight = 200
            };

            var lblSize = new Label
            {
                Text = "大小:",
                Location = new Point(260, 80),
                Size = new Size(50, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudTitleFontSize = new NumericUpDown
            {
                Location = new Point(320, 78),
                Size = new Size(80, 28),
                Minimum = 12,
                Maximum = 72,
                Value = 36,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblColor = new Label
            {
                Text = "颜色:",
                Location = new Point(420, 80),
                Size = new Size(50, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnTitleFontColor = new Button
            {
                Location = new Point(480, 78),
                Size = new Size(100, 28),
                BackColor = _titleFontColor,
                Text = "选择颜色",
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnTitleFontColor.Click += BtnTitleFontColor_Click;

            _chkTitleBold = new CheckBox
            {
                Text = "粗体",
                Location = new Point(600, 80),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblPosition, _cmbTitlePosition, lblAlignment, _cmbTitleAlignment,
                lblFont, _cmbTitleFontFamily, lblSize, _nudTitleFontSize,
                lblColor, _btnTitleFontColor, _chkTitleBold
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建副标题设置组
        /// </summary>
        private void CreateSubtitleSettingsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "副标题设置",
                Location = new Point(30, 370),
                Size = new Size(740, 180),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            // 第一行：显示副标题和位置
            _chkShowSubtitle = new CheckBox
            {
                Text = "显示副标题",
                Location = new Point(30, 40),
                Size = new Size(120, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblPosition = new Label
            {
                Text = "副标题位置:",
                Location = new Point(170, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSubtitlePosition = new ComboBox
            {
                Location = new Point(270, 38),
                Size = new Size(140, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            var lblAlignment = new Label
            {
                Text = "文字对齐:",
                Location = new Point(430, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSubtitleAlignment = new ComboBox
            {
                Location = new Point(530, 38),
                Size = new Size(120, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 6,
                DropDownHeight = 120
            };

            // 第二行：字体设置
            var lblFont = new Label
            {
                Text = "字体:",
                Location = new Point(30, 80),
                Size = new Size(60, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbSubtitleFontFamily = new ComboBox
            {
                Location = new Point(100, 78),
                Size = new Size(140, 28),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 10F),
                MaxDropDownItems = 10,
                DropDownHeight = 200
            };

            var lblSize = new Label
            {
                Text = "大小:",
                Location = new Point(260, 80),
                Size = new Size(50, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudSubtitleFontSize = new NumericUpDown
            {
                Location = new Point(320, 78),
                Size = new Size(80, 28),
                Minimum = 10,
                Maximum = 48,
                Value = 18,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            var lblColor = new Label
            {
                Text = "颜色:",
                Location = new Point(420, 80),
                Size = new Size(50, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnSubtitleFontColor = new Button
            {
                Location = new Point(480, 78),
                Size = new Size(100, 28),
                BackColor = _subtitleFontColor,
                Text = "选择颜色",
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnSubtitleFontColor.Click += BtnSubtitleFontColor_Click;

            _chkSubtitleBold = new CheckBox
            {
                Text = "粗体",
                Location = new Point(600, 80),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] {
                _chkShowSubtitle, lblPosition, _cmbSubtitlePosition, lblAlignment, _cmbSubtitleAlignment,
                lblFont, _cmbSubtitleFontFamily, lblSize, _nudSubtitleFontSize,
                lblColor, _btnSubtitleFontColor, _chkSubtitleBold
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建背景设置组
        /// </summary>
        private void CreateBackgroundGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "背景设置",
                Location = new Point(30, 570),
                Size = new Size(740, 80),
                Font = new Font("Microsoft YaHei UI", 11F, FontStyle.Bold)
            };

            var lblBackground = new Label
            {
                Text = "背景颜色:",
                Location = new Point(30, 40),
                Size = new Size(90, 25),
                Font = new Font("Microsoft YaHei UI", 10F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnBackgroundColor = new Button
            {
                Location = new Point(130, 38),
                Size = new Size(120, 28),
                BackColor = _backgroundColor,
                Text = "选择背景色",
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnBackgroundColor.Click += BtnBackgroundColor_Click;

            _chkAutoResize = new CheckBox
            {
                Text = "自动调整文字大小",
                Location = new Point(270, 40),
                Size = new Size(180, 25),
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            groupBox.Controls.AddRange(new Control[] { lblBackground, _btnBackgroundColor, _chkAutoResize });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button
            {
                Text = "确定(&O)",
                Location = new Point(480, 670),
                Size = new Size(90, 35),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消(&C)",
                Location = new Point(585, 670),
                Size = new Size(90, 35),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 10F)
            };

            _btnReset = new Button
            {
                Text = "重置(&R)",
                Location = new Point(690, 670),
                Size = new Size(80, 35),
                Font = new Font("Microsoft YaHei UI", 10F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 布局模板
            var templates = new string[] { "居中标题布局", "左对齐标题布局", "右对齐标题布局", "顶部标题布局", "底部标题布局" };
            _cmbLayoutTemplate?.Items.AddRange(templates);
            if (_cmbLayoutTemplate != null) _cmbLayoutTemplate.SelectedItem = "居中标题布局";

            // 标题位置
            var titlePositions = new string[] { "顶部", "中部", "底部", "自定义" };
            _cmbTitlePosition?.Items.AddRange(titlePositions);
            if (_cmbTitlePosition != null) _cmbTitlePosition.SelectedItem = "中部";

            // 副标题位置
            var subtitlePositions = new string[] { "标题下方", "标题上方", "页面底部", "自定义" };
            _cmbSubtitlePosition?.Items.AddRange(subtitlePositions);
            if (_cmbSubtitlePosition != null) _cmbSubtitlePosition.SelectedItem = "标题下方";

            // 对齐方式
            var alignments = new string[] { "左对齐", "居中对齐", "右对齐" };
            _cmbTitleAlignment?.Items.AddRange(alignments);
            _cmbSubtitleAlignment?.Items.AddRange(alignments);
            if (_cmbTitleAlignment != null) _cmbTitleAlignment.SelectedItem = "居中对齐";
            if (_cmbSubtitleAlignment != null) _cmbSubtitleAlignment.SelectedItem = "居中对齐";

            // 字体
            var fonts = new string[] { "微软雅黑", "宋体", "黑体", "楷体", "仿宋", "Arial", "Times New Roman", "Calibri" };
            _cmbTitleFontFamily?.Items.AddRange(fonts);
            _cmbSubtitleFontFamily?.Items.AddRange(fonts);
            if (_cmbTitleFontFamily != null) _cmbTitleFontFamily.SelectedItem = "微软雅黑";
            if (_cmbSubtitleFontFamily != null) _cmbSubtitleFontFamily.SelectedItem = "微软雅黑";

            // 默认设置
            if (_chkShowSubtitle != null) _chkShowSubtitle.Checked = true;
            if (_chkTitleBold != null) _chkTitleBold.Checked = true;
            if (_chkSubtitleBold != null) _chkSubtitleBold.Checked = false;
            if (_chkAutoResize != null) _chkAutoResize.Checked = false;
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 标题字体颜色按钮点击事件
        /// </summary>
        private void BtnTitleFontColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _titleFontColor };
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                _titleFontColor = colorDialog.Color;
                if (_btnTitleFontColor != null)
                    _btnTitleFontColor.BackColor = _titleFontColor;
            }
        }

        /// <summary>
        /// 副标题字体颜色按钮点击事件
        /// </summary>
        private void BtnSubtitleFontColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _subtitleFontColor };
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                _subtitleFontColor = colorDialog.Color;
                if (_btnSubtitleFontColor != null)
                    _btnSubtitleFontColor.BackColor = _subtitleFontColor;
            }
        }

        /// <summary>
        /// 背景颜色按钮点击事件
        /// </summary>
        private void BtnBackgroundColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _backgroundColor };
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                _backgroundColor = colorDialog.Color;
                if (_btnBackgroundColor != null)
                    _btnBackgroundColor.BackColor = _backgroundColor;
            }
        }

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能显示当前标题布局设置的效果。\n\n" +
                           "在实际应用中，这里会显示布局预览图。", "布局预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new TitleSlideLayoutSettings
                {
                    LayoutTemplate = _cmbLayoutTemplate?.SelectedItem?.ToString() ?? "居中标题布局",
                    TitlePosition = _cmbTitlePosition?.SelectedItem?.ToString() ?? "中部",
                    TitleAlignment = _cmbTitleAlignment?.SelectedItem?.ToString() ?? "居中对齐",
                    TitleFontFamily = _cmbTitleFontFamily?.SelectedItem?.ToString() ?? "微软雅黑",
                    TitleFontSize = (int)(_nudTitleFontSize?.Value ?? 36),
                    TitleFontColor = _titleFontColor,
                    TitleBold = _chkTitleBold?.Checked ?? true,
                    ShowSubtitle = _chkShowSubtitle?.Checked ?? true,
                    SubtitlePosition = _cmbSubtitlePosition?.SelectedItem?.ToString() ?? "标题下方",
                    SubtitleAlignment = _cmbSubtitleAlignment?.SelectedItem?.ToString() ?? "居中对齐",
                    SubtitleFontFamily = _cmbSubtitleFontFamily?.SelectedItem?.ToString() ?? "微软雅黑",
                    SubtitleFontSize = (int)(_nudSubtitleFontSize?.Value ?? 18),
                    SubtitleFontColor = _subtitleFontColor,
                    SubtitleBold = _chkSubtitleBold?.Checked ?? false,
                    BackgroundColor = _backgroundColor,
                    AutoResize = _chkAutoResize?.Checked ?? false
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();

            // 重置数值控件
            if (_nudTitleFontSize != null) _nudTitleFontSize.Value = 36;
            if (_nudSubtitleFontSize != null) _nudSubtitleFontSize.Value = 18;

            // 重置颜色
            _titleFontColor = Color.Black;
            _subtitleFontColor = Color.DarkGray;
            _backgroundColor = Color.White;

            if (_btnTitleFontColor != null) _btnTitleFontColor.BackColor = _titleFontColor;
            if (_btnSubtitleFontColor != null) _btnSubtitleFontColor.BackColor = _subtitleFontColor;
            if (_btnBackgroundColor != null) _btnBackgroundColor.BackColor = _backgroundColor;
        }

        #endregion
    }

    /// <summary>
    /// 标题幻灯片布局设置类
    /// </summary>
    public class TitleSlideLayoutSettings
    {
        public string LayoutTemplate { get; set; } = "居中标题布局";
        public string TitlePosition { get; set; } = "中部";
        public string TitleAlignment { get; set; } = "居中对齐";
        public string TitleFontFamily { get; set; } = "微软雅黑";
        public int TitleFontSize { get; set; } = 36;
        public Color TitleFontColor { get; set; } = Color.Black;
        public bool TitleBold { get; set; } = true;
        public bool ShowSubtitle { get; set; } = true;
        public string SubtitlePosition { get; set; } = "标题下方";
        public string SubtitleAlignment { get; set; } = "居中对齐";
        public string SubtitleFontFamily { get; set; } = "微软雅黑";
        public int SubtitleFontSize { get; set; } = 18;
        public Color SubtitleFontColor { get; set; } = Color.DarkGray;
        public bool SubtitleBold { get; set; } = false;
        public Color BackgroundColor { get; set; } = Color.White;
        public bool AutoResize { get; set; } = false;
    }
}
