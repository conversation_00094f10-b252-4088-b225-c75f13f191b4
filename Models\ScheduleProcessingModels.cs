using System;

namespace PPTPiliangChuli.Models
{
    /// <summary>
    /// 定时处理配置
    /// </summary>
    public class ScheduleProcessingConfig
    {
        /// <summary>
        /// 定时处理类型
        /// </summary>
        public ScheduleType ScheduleType { get; set; } = ScheduleType.OneTime;

        #region 一次性启动设置

        /// <summary>
        /// 一次性执行时间
        /// </summary>
        public DateTime OneTimeExecutionTime { get; set; } = DateTime.Now.AddHours(1);

        #endregion

        #region 指定时间启动设置

        /// <summary>
        /// 指定时间启动类型
        /// </summary>
        public RecurringType RecurringType { get; set; } = RecurringType.Daily;

        /// <summary>
        /// 月份（每年启动时使用）
        /// </summary>
        public int Month { get; set; } = 1;

        /// <summary>
        /// 日期（每年、每月启动时使用）
        /// </summary>
        public int Day { get; set; } = 1;

        /// <summary>
        /// 小时（每年、每月、每天启动时使用）
        /// </summary>
        public int Hour { get; set; } = 0;

        /// <summary>
        /// 分钟（每年、每月、每天、每时启动时使用）
        /// </summary>
        public int Minute { get; set; } = 0;

        /// <summary>
        /// 秒（所有指定时间启动时使用）
        /// </summary>
        public int Second { get; set; } = 0;

        #endregion

        #region 倒计时启动设置

        /// <summary>
        /// 倒计时第一次执行时间
        /// </summary>
        public DateTime CountdownFirstExecutionTime { get; set; } = DateTime.Now.AddHours(1);

        /// <summary>
        /// 倒计时间隔
        /// </summary>
        public TimeSpan CountdownInterval { get; set; } = TimeSpan.FromHours(1);

        #endregion

        #region 高级设置

        /// <summary>
        /// 限制类型
        /// </summary>
        public LimitationType LimitationType { get; set; } = LimitationType.Unlimited;

        /// <summary>
        /// 限制次数
        /// </summary>
        public int LimitCount { get; set; } = 1;

        /// <summary>
        /// 限制时间
        /// </summary>
        public DateTime LimitTime { get; set; } = DateTime.Now.AddDays(1);

        #endregion
    }

    /// <summary>
    /// 定时处理类型
    /// </summary>
    public enum ScheduleType
    {
        /// <summary>
        /// 一次性启动
        /// </summary>
        OneTime = 0,

        /// <summary>
        /// 指定时间启动
        /// </summary>
        Recurring = 1,

        /// <summary>
        /// 倒计时启动
        /// </summary>
        Countdown = 2
    }

    /// <summary>
    /// 指定时间启动类型
    /// </summary>
    public enum RecurringType
    {
        /// <summary>
        /// 每年启动
        /// </summary>
        Yearly = 0,

        /// <summary>
        /// 每月启动
        /// </summary>
        Monthly = 1,

        /// <summary>
        /// 每天启动
        /// </summary>
        Daily = 2,

        /// <summary>
        /// 每时启动
        /// </summary>
        Hourly = 3
    }

    /// <summary>
    /// 限制类型
    /// </summary>
    public enum LimitationType
    {
        /// <summary>
        /// 无限制
        /// </summary>
        Unlimited = 0,

        /// <summary>
        /// 次数限制
        /// </summary>
        Count = 1,

        /// <summary>
        /// 时间限制
        /// </summary>
        Time = 2
    }

    /// <summary>
    /// 定时处理状态变更事件参数
    /// </summary>
    public class ScheduleStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 状态消息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 是否为错误
        /// </summary>
        public bool IsError { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        public ScheduleStatusChangedEventArgs(string message, bool isError)
        {
            Message = message;
            IsError = isError;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 定时处理执行事件参数
    /// </summary>
    public class ScheduleExecutionEventArgs : EventArgs
    {
        /// <summary>
        /// 触发类型
        /// </summary>
        public string TriggerType { get; }

        /// <summary>
        /// 运行次数
        /// </summary>
        public int RunCount { get; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        public ScheduleExecutionEventArgs(string triggerType, int runCount, bool success)
        {
            TriggerType = triggerType;
            RunCount = runCount;
            Success = success;
            Timestamp = DateTime.Now;
        }
    }
}
