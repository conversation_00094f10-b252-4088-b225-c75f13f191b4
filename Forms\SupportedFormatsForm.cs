using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 支持格式设置窗体 - 用于配置PPT批量处理软件支持的PowerPoint文件格式
    /// 此窗体允许用户选择要处理的文件格式，符合Aspose.Slides API规范
    /// </summary>
    public partial class SupportedFormatsForm : Form
    {
        private readonly List<FormatInfo> _formats;
        private readonly Dictionary<string, CheckBox> _formatCheckBoxes;

        public SupportedFormatsForm()
        {
            InitializeComponent();
            _formats = GetSupportedFormats();
            _formatCheckBoxes = new Dictionary<string, CheckBox>();
            InitializeFormatControls();
            LoadCurrentSettings();
            ApplyModernStyle();
        }

        /// <summary>
        /// 获取支持的格式列表 - 基于Aspose.Slides SaveFormat枚举定义的支持格式
        /// 包含PowerPoint的主要文件格式，确保与API规范一致
        /// </summary>
        private List<FormatInfo> GetSupportedFormats()
        {
            return new List<FormatInfo>
            {
                new FormatInfo { Extension = ".ppt", Description = "PowerPoint 97-2003 演示文稿", IsDefault = true },
                new FormatInfo { Extension = ".pptx", Description = "PowerPoint 演示文稿", IsDefault = true },
                new FormatInfo { Extension = ".pptm", Description = "PowerPoint 启用宏的演示文稿", IsDefault = true },
                new FormatInfo { Extension = ".ppsx", Description = "PowerPoint 幻灯片放映", IsDefault = false },
                new FormatInfo { Extension = ".ppsm", Description = "PowerPoint 启用宏的幻灯片放映", IsDefault = false },
                new FormatInfo { Extension = ".potx", Description = "PowerPoint 模板", IsDefault = false },
                new FormatInfo { Extension = ".potm", Description = "PowerPoint 启用宏的模板", IsDefault = false },
                new FormatInfo { Extension = ".odp", Description = "OpenDocument 演示文稿", IsDefault = false },
                new FormatInfo { Extension = ".otp", Description = "OpenDocument 演示文稿模板", IsDefault = false }
            };
        }

        /// <summary>
        /// 初始化格式控件 - 创建格式选择复选框，确保UI符合规范要求
        /// 复选框文字垂直居中，字体大小符合最小10F要求，行高不少于40px
        /// </summary>
        private void InitializeFormatControls()
        {
            int yPosition = 15;
            const int itemHeight = 40; // 最小40px行高要求
            const int leftMargin = 20;

            foreach (var format in _formats)
            {
                var checkBox = new CheckBox
                {
                    Name = $"chk{format.Extension.Replace(".", "")}",
                    Text = $"{format.Extension.ToUpper()} - {format.Description}",
                    Location = new Point(leftMargin, yPosition),
                    Size = new Size(420, 30), // 增加高度确保文字完全显示
                    Checked = format.IsDefault,
                    Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular), // 最小10F字体
                    ForeColor = Color.FromArgb(64, 64, 64),
                    AutoSize = false, // 禁用自动大小以精确控制布局
                    TextAlign = ContentAlignment.MiddleLeft // 复选框文字垂直居中
                };

                panelFormats.Controls.Add(checkBox);
                _formatCheckBoxes[format.Extension] = checkBox;
                yPosition += itemHeight;
            }

            // 使用设计器中设置的固定高度，确保布局稳定
        }

        /// <summary>
        /// 加载当前设置 - 从配置文件读取已选择的支持格式
        /// 确保UI状态与配置文件保持一致
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                var supportedFormats = config.ProcessSettings.SupportedFormats;

                foreach (var kvp in _formatCheckBoxes)
                {
                    kvp.Value.Checked = supportedFormats.Contains(kvp.Key);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 保存设置 - 将用户选择的格式保存到配置文件
        /// 自动更新配置，无需额外确认
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                config.ProcessSettings.SupportedFormats.Clear();

                foreach (var kvp in _formatCheckBoxes)
                {
                    if (kvp.Value.Checked)
                    {
                        config.ProcessSettings.SupportedFormats.Add(kvp.Key);
                    }
                }

                ConfigService.Instance.UpdateConfig(config);
                // 移除多余的确认提示框，按照用户要求减少频繁弹窗
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 应用现代化样式 - 设置窗体和控件的视觉样式
        /// 确保字体大小符合最小10F要求
        /// </summary>
        private void ApplyModernStyle()
        {
            // 窗体样式，使用现代化配色
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular); // 最小10F字体

            // 面板样式，提供清晰的视觉边界
            panelFormats.BackColor = Color.White;
            panelFormats.BorderStyle = BorderStyle.FixedSingle;

            // 按钮样式，保持一致的现代化外观
            btnOK.BackColor = Color.FromArgb(0, 123, 255);
            btnOK.ForeColor = Color.White;
            btnOK.FlatStyle = FlatStyle.Flat;
            btnOK.FlatAppearance.BorderSize = 0;

            btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;

            btnSelectAll.BackColor = Color.FromArgb(40, 167, 69);
            btnSelectAll.ForeColor = Color.White;
            btnSelectAll.FlatStyle = FlatStyle.Flat;
            btnSelectAll.FlatAppearance.BorderSize = 0;

            btnDeselectAll.BackColor = Color.FromArgb(220, 53, 69);
            btnDeselectAll.ForeColor = Color.White;
            btnDeselectAll.FlatStyle = FlatStyle.Flat;
            btnDeselectAll.FlatAppearance.BorderSize = 0;
        }

        /// <summary>
        /// 全选按钮点击事件 - 选择所有支持的文件格式
        /// 提供快速选择功能，提升用户体验
        /// </summary>
        private void BtnSelectAll_Click(object sender, EventArgs e)
        {
            foreach (var checkBox in _formatCheckBoxes.Values)
            {
                checkBox.Checked = true;
            }
        }

        /// <summary>
        /// 取消全选按钮点击事件 - 取消选择所有文件格式
        /// 提供快速清除选择功能
        /// </summary>
        private void BtnDeselectAll_Click(object sender, EventArgs e)
        {
            foreach (var checkBox in _formatCheckBoxes.Values)
            {
                checkBox.Checked = false;
            }
        }

        /// <summary>
        /// 确定按钮点击事件 - 保存设置并关闭窗口
        /// 点击确定按钮自动保存配置文件，符合用户要求
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            // 验证至少选择一种格式，确保功能可用性
            if (!_formatCheckBoxes.Values.Any(cb => cb.Checked))
            {
                MessageBox.Show("请至少选择一种支持的文件格式！", "提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            SaveSettings(); // 自动保存配置
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>
        /// 取消按钮点击事件 - 不保存设置直接关闭窗口
        /// 点击取消按钮不更新配置文件，符合用户要求
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }

    /// <summary>
    /// 格式信息类 - 定义PowerPoint文件格式的基本信息
    /// 包含扩展名、描述和默认选择状态
    /// </summary>
    public class FormatInfo
    {
        /// <summary>
        /// 文件扩展名 - PowerPoint文件的扩展名（如.ppt, .pptx等）
        /// </summary>
        public string Extension { get; set; } = string.Empty;

        /// <summary>
        /// 格式描述 - 文件格式的中文描述信息
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否默认选择 - 该格式是否在初始化时默认被选中
        /// </summary>
        public bool IsDefault { get; set; } = false;
    }
}
