{
  // 文档删除功能示例配置 - 展示如何为最小和最大文件大小设置不同的单位
  
  // 文档删除设置 - 根据文件属性条件删除整个PPT文档
  "DocumentDeletion": {
    // 启用文档删除功能开关 - 是否根据文件条件删除整个PPT文档
    "EnableDocumentDeletion": true,

    // 删除范围 - 指定删除操作的作用范围（1=普通幻灯片页，2=母版，4=版式页，可组合使用）
    "DeletionScope": 7,

    // 启用文件名长度检查 - 是否根据文件名长度删除文档
    "EnableFileNameLengthCheck": true,
    // 文件名最小长度 - 文件名字符数下限
    "FileNameMinLength": 3,
    // 文件名最大长度 - 文件名字符数上限
    "FileNameMaxLength": 50,

    // 启用文件大小检查 - 是否根据文件大小删除文档
    "EnableFileSizeCheck": true,
    // 文件最小大小 - 文件大小下限
    "FileMinSize": 100,
    // 文件最小大小单位 - B、KB、MB、GB
    "FileMinSizeUnit": "KB",
    // 文件最大大小 - 文件大小上限
    "FileMaxSize": 50,
    // 文件最大大小单位 - B、KB、MB、GB
    "FileMaxSizeUnit": "MB",
    // 文件大小单位 - 保留用于向后兼容（建议使用上面的分别设置）
    "FileSizeUnit": "KB",

    // 启用内容字符数检查 - 是否根据PPT内容字符数删除文档
    "EnableContentCharCountCheck": true,
    // 内容最小字符数 - PPT内容字符数下限
    "ContentMinCharCount": 50,
    // 内容最大字符数 - PPT内容字符数上限
    "ContentMaxCharCount": 10000,

    // 启用页面数检查 - 是否根据PPT页面数删除文档
    "EnablePageCountCheck": true,
    // 最小页面数 - PPT页面数下限
    "MinPageCount": 2,
    // 最大页面数 - PPT页面数上限
    "MaxPageCount": 100,

    // 启用文件名违禁词检查 - 是否根据文件名包含违禁词删除文档
    "EnableFileNameIllegalWordsCheck": true,
    // 文件名违禁词列表 - 包含这些词的文件将被删除
    "FileNameIllegalWords": [
      "测试",
      "临时",
      "草稿"
    ],

    // 启用内容违禁词检查 - 是否根据PPT内容包含违禁词删除文档
    "EnableContentIllegalWordsCheck": true,
    // 内容违禁词列表 - 包含这些词的PPT将被删除
    "ContentIllegalWords": [
      "机密信息",
      "内部资料",
      "禁止外传"
    ]
  }
}

// 使用说明：
// 1. 文件大小检查现在支持为最小和最大大小分别设置单位
// 2. 在上面的示例中：
//    - 最小文件大小：100 KB
//    - 最大文件大小：50 MB
// 3. 这样可以更灵活地设置文件大小范围，比如：
//    - 最小：500 B，最大：10 MB
//    - 最小：1 KB，最大：100 MB
//    - 最小：1 MB，最大：1 GB
// 4. 如果没有设置新的单位属性，程序会自动使用旧的 FileSizeUnit 属性保持向后兼容
