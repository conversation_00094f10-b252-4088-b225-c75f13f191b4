using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 匹配段落格式设置窗体 - 用于配置段落格式匹配规则，支持根据文本内容条件自动应用段落和字体格式
    /// </summary>
    public partial class ParagraphFormatMatchingForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 当前设置
        /// </summary>
        private ParagraphFormatMatchingSettings _currentSettings = null!;

        /// <summary>
        /// 规则列表视图
        /// </summary>
        private ListView _listViewRules = null!;

        /// <summary>
        /// 添加规则按钮
        /// </summary>
        private Button _btnAddRule = null!;

        /// <summary>
        /// 编辑规则按钮
        /// </summary>
        private Button _btnEditRule = null!;

        /// <summary>
        /// 删除规则按钮
        /// </summary>
        private Button _btnDeleteRule = null!;

        /// <summary>
        /// 复制规则按钮
        /// </summary>
        private Button _btnCopyRule = null!;

        /// <summary>
        /// 上移按钮
        /// </summary>
        private Button _btnMoveUp = null!;

        /// <summary>
        /// 下移按钮
        /// </summary>
        private Button _btnMoveDown = null!;



        /// <summary>
        /// 确定按钮
        /// </summary>
        private Button _btnOK = null!;

        /// <summary>
        /// 取消按钮
        /// </summary>
        private Button _btnCancel = null!;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ParagraphFormatMatchingForm()
        {
            InitializeComponent();
            LoadSettings();
            InitializeControls();
            SetupEventHandlers();
            LoadRulesList();
            UpdateUI();

            // 设置窗体关闭事件，确保配置正确保存
            FormClosing += ParagraphFormatMatchingForm_FormClosing;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取当前设置
        /// </summary>
        public ParagraphFormatMatchingSettings GetCurrentSettings()
        {
            SaveCurrentSettings();
            return _currentSettings;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                _currentSettings = config.ParagraphFormatMatchingSettings ?? new ParagraphFormatMatchingSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _currentSettings = new ParagraphFormatMatchingSettings();
            }
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 设置窗体属性
            Text = "匹配段落格式设置";
            Size = new Size(1050, 800);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            ShowInTaskbar = false;

            // 创建主面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2, // 减少为2行：规则列表区域和按钮区域
                Padding = new Padding(15)
            };

            // 设置行高
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 规则列表区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 70)); // 按钮区域

            // 创建规则列表区域
            CreateRuleListArea(mainPanel);

            // 创建按钮区域
            CreateButtonArea(mainPanel);

            Controls.Add(mainPanel);
        }



        /// <summary>
        /// 创建规则列表区域
        /// </summary>
        private void CreateRuleListArea(TableLayoutPanel parent)
        {
            var groupBox = new GroupBox
            {
                Text = "段落格式匹配规则",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Padding = new Padding(15, 25, 15, 15)
            };

            var tableLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(10)
            };

            // 设置行高 - 列表区域占大部分空间，按钮区域固定高度
            tableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 列表区域
            tableLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 120)); // 按钮区域

            // 创建规则列表
            CreateRulesList(tableLayout);

            // 创建操作按钮
            CreateRuleButtons(tableLayout);

            groupBox.Controls.Add(tableLayout);
            parent.Controls.Add(groupBox, 0, 0);
        }

        /// <summary>
        /// 创建规则列表
        /// </summary>
        private void CreateRulesList(TableLayoutPanel parent)
        {
            _listViewRules = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(5)
            };

            // 添加列 - 设置列头文字居中显示，表格内容也将居中显示
            // 总宽度约920像素（考虑滚动条和边距），合理分配各列宽度
            var colRuleName = _listViewRules.Columns.Add("规则名称", 180);
            colRuleName.TextAlign = HorizontalAlignment.Center;

            var colMatchCondition = _listViewRules.Columns.Add("匹配条件", 240);
            colMatchCondition.TextAlign = HorizontalAlignment.Center;

            var colReplacementScope = _listViewRules.Columns.Add("替换范围", 260);
            colReplacementScope.TextAlign = HorizontalAlignment.Center;

            var colParagraphFormat = _listViewRules.Columns.Add("段落格式", 120);
            colParagraphFormat.TextAlign = HorizontalAlignment.Center;

            var colFontFormat = _listViewRules.Columns.Add("字体格式", 120);
            colFontFormat.TextAlign = HorizontalAlignment.Center;

            parent.Controls.Add(_listViewRules, 0, 0);
        }

        /// <summary>
        /// 创建规则操作按钮
        /// </summary>
        private void CreateRuleButtons(TableLayoutPanel parent)
        {
            var buttonPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 8,
                RowCount = 2,
                Padding = new Padding(10)
            };

            // 设置列宽 - 前4个按钮等宽，分隔线，后2个按钮等宽，最后空白区域
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 添加规则
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 编辑规则
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 删除规则
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 复制规则
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 30));  // 分隔空间
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));  // 上移
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));  // 下移
            buttonPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));  // 空白区域

            // 设置行高
            buttonPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40)); // 按钮行
            buttonPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 空白区域

            // 创建按钮
            _btnAddRule = new Button
            {
                Text = "添加规则(&A)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            _btnEditRule = new Button
            {
                Text = "编辑规则(&E)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            _btnDeleteRule = new Button
            {
                Text = "删除规则(&D)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            _btnCopyRule = new Button
            {
                Text = "复制规则(&C)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            _btnMoveUp = new Button
            {
                Text = "上移(&U)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            _btnMoveDown = new Button
            {
                Text = "下移(&N)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 9F),
                Margin = new Padding(3)
            };

            // 添加按钮到面板 - 第一行水平排列
            buttonPanel.Controls.Add(_btnAddRule, 0, 0);
            buttonPanel.Controls.Add(_btnEditRule, 1, 0);
            buttonPanel.Controls.Add(_btnDeleteRule, 2, 0);
            buttonPanel.Controls.Add(_btnCopyRule, 3, 0);
            buttonPanel.Controls.Add(_btnMoveUp, 5, 0);
            buttonPanel.Controls.Add(_btnMoveDown, 6, 0);

            parent.Controls.Add(buttonPanel, 0, 1);
        }

        /// <summary>
        /// 创建按钮区域
        /// </summary>
        private void CreateButtonArea(TableLayoutPanel parent)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(15, 10, 15, 15) // 增加左右边距，优化视觉效果
            };

            var buttonLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 6,
                RowCount = 1,
                Height = 50 // 固定按钮区域高度
            };

            // 优化列宽设置 - 更合理的按钮布局
            buttonLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 左侧空白区域
            buttonLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 确定按钮
            buttonLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 15));  // 按钮间距
            buttonLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 取消按钮

            _btnOK = new Button
            {
                Text = "确定(&O)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 10F), // 增大字体
                UseVisualStyleBackColor = true,
                FlatStyle = FlatStyle.Standard,
                Height = 35, // 固定按钮高度
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                DialogResult = DialogResult.OK
            };

            _btnCancel = new Button
            {
                Text = "取消(&C)",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei UI", 10F), // 增大字体
                UseVisualStyleBackColor = true,
                FlatStyle = FlatStyle.Standard,
                Height = 35, // 固定按钮高度
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                DialogResult = DialogResult.Cancel
            };

            // 添加按钮到布局，使用间距列分隔
            buttonLayout.Controls.Add(_btnOK, 1, 0);
            buttonLayout.Controls.Add(_btnCancel, 3, 0);

            panel.Controls.Add(buttonLayout);
            parent.Controls.Add(panel, 0, 1);

            // 设置默认按钮
            AcceptButton = _btnOK;
            CancelButton = _btnCancel;
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 规则列表事件
            _listViewRules.SelectedIndexChanged += ListViewRules_SelectedIndexChanged;
            _listViewRules.ItemChecked += ListViewRules_ItemChecked;
            _listViewRules.DoubleClick += ListViewRules_DoubleClick;

            // 按钮事件
            _btnAddRule.Click += BtnAddRule_Click;
            _btnEditRule.Click += BtnEditRule_Click;
            _btnDeleteRule.Click += BtnDeleteRule_Click;
            _btnCopyRule.Click += BtnCopyRule_Click;
            _btnMoveUp.Click += BtnMoveUp_Click;
            _btnMoveDown.Click += BtnMoveDown_Click;

            _btnOK.Click += BtnOK_Click;
            _btnCancel.Click += BtnCancel_Click;
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            // 规则列表始终启用
            _listViewRules.Enabled = true;
            _btnAddRule.Enabled = true;

            // 更新按钮状态
            UpdateButtonStates();
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        private void UpdateButtonStates()
        {
            var hasSelection = _listViewRules.SelectedItems.Count > 0;

            _btnEditRule.Enabled = hasSelection;
            _btnDeleteRule.Enabled = hasSelection;
            _btnCopyRule.Enabled = hasSelection;

            // 修复上移下移按钮状态判断逻辑
            if (hasSelection)
            {
                var selectedIndex = _listViewRules.SelectedIndices[0];
                _btnMoveUp.Enabled = selectedIndex > 0;
                _btnMoveDown.Enabled = selectedIndex < _listViewRules.Items.Count - 1;
            }
            else
            {
                _btnMoveUp.Enabled = false;
                _btnMoveDown.Enabled = false;
            }
        }

        /// <summary>
        /// 加载规则列表
        /// </summary>
        private void LoadRulesList()
        {
            _listViewRules.Items.Clear();

            foreach (var rule in _currentSettings.MatchingRules)
            {
                var item = new ListViewItem(rule.RuleName)
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                // 匹配条件描述
                var conditionDesc = GetConditionDescription(rule.MatchingConditions);
                item.SubItems.Add(conditionDesc);

                // 替换范围描述
                var scopeDesc = GetReplacementScopeDescription(rule.ReplacementScope);
                item.SubItems.Add(scopeDesc);

                // 段落格式描述
                var paragraphDesc = rule.ParagraphFormat.EnableParagraphFormat ? "已设置" : "未设置";
                item.SubItems.Add(paragraphDesc);

                // 字体格式描述
                var fontDesc = rule.FontFormat.EnableFontFormat ? "已设置" : "未设置";
                item.SubItems.Add(fontDesc);

                _listViewRules.Items.Add(item);
            }
        }

        /// <summary>
        /// 获取匹配条件描述
        /// </summary>
        private static string GetConditionDescription(ParagraphMatchingConditions conditions)
        {
            var descriptions = new List<string>();

            if (conditions.EnableStartsWith && !string.IsNullOrEmpty(conditions.StartsWithText))
                descriptions.Add($"开头:{conditions.StartsWithText}");

            if (conditions.EnableContains && conditions.ContainsKeywords.Count > 0)
                descriptions.Add($"包含:{string.Join(",", conditions.ContainsKeywords.Take(2))}{(conditions.ContainsKeywords.Count > 2 ? "..." : "")}");

            if (conditions.EnableEndsWith && !string.IsNullOrEmpty(conditions.EndsWithText))
                descriptions.Add($"结尾:{conditions.EndsWithText}");

            if (conditions.EnableRegex && !string.IsNullOrEmpty(conditions.RegexPattern))
                descriptions.Add("正则表达式");

            if (conditions.EnableCharacterCountLimit)
                descriptions.Add($"字符数:{conditions.MinCharacterCount}-{conditions.MaxCharacterCount}");

            return descriptions.Count > 0 ? string.Join("; ", descriptions) : "无条件";
        }

        /// <summary>
        /// 获取替换范围描述
        /// </summary>
        private static string GetReplacementScopeDescription(Models.ReplacementScope scope)
        {
            var descriptions = new List<string>();

            if (scope.IncludeNormalSlides)
                descriptions.Add("普通页");

            if (scope.IncludeMasterSlides)
                descriptions.Add("母版页");

            if (scope.IncludeLayoutSlides)
                descriptions.Add("版式页");

            return descriptions.Count > 0 ? string.Join(", ", descriptions) : "无范围";
        }

        /// <summary>
        /// 保存当前设置
        /// </summary>
        private void SaveCurrentSettings()
        {
            // 如果有规则且至少有一个启用，则启用功能；否则禁用功能
            _currentSettings.EnableParagraphFormatMatching = _currentSettings.MatchingRules.Any(r => r.IsEnabled);

            // 更新规则启用状态
            for (int i = 0; i < _listViewRules.Items.Count && i < _currentSettings.MatchingRules.Count; i++)
            {
                _currentSettings.MatchingRules[i].IsEnabled = _listViewRules.Items[i].Checked;
            }
        }

        /// <summary>
        /// 保存设置到配置文件
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                SaveCurrentSettings();
                // 使用专门的段落格式配置保存方法，确保配置正确保存
                ConfigService.Instance.SaveParagraphFormatSettings(_currentSettings);
                LogService.Instance.LogConfigChange("段落格式匹配设置已保存");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogService.Instance.LogProcessError($"保存段落格式匹配设置失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 事件处理器



        /// <summary>
        /// 规则列表选择变更事件
        /// </summary>
        private void ListViewRules_SelectedIndexChanged(object? sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        /// <summary>
        /// 规则项复选框变更事件
        /// </summary>
        private void ListViewRules_ItemChecked(object? sender, ItemCheckedEventArgs e)
        {
            if (e.Item.Tag is ParagraphFormatMatchingRule rule)
            {
                rule.IsEnabled = e.Item.Checked;
            }
        }

        /// <summary>
        /// 规则列表双击事件
        /// </summary>
        private void ListViewRules_DoubleClick(object? sender, EventArgs e)
        {
            if (_listViewRules.SelectedItems.Count > 0)
            {
                EditSelectedRule();
            }
        }

        /// <summary>
        /// 添加规则按钮点击事件
        /// </summary>
        private void BtnAddRule_Click(object? sender, EventArgs e)
        {
            AddNewRule();
        }

        /// <summary>
        /// 编辑规则按钮点击事件
        /// </summary>
        private void BtnEditRule_Click(object? sender, EventArgs e)
        {
            EditSelectedRule();
        }

        /// <summary>
        /// 删除规则按钮点击事件
        /// </summary>
        private void BtnDeleteRule_Click(object? sender, EventArgs e)
        {
            DeleteSelectedRule();
        }

        /// <summary>
        /// 复制规则按钮点击事件
        /// </summary>
        private void BtnCopyRule_Click(object? sender, EventArgs e)
        {
            CopySelectedRule();
        }

        /// <summary>
        /// 上移按钮点击事件
        /// </summary>
        private void BtnMoveUp_Click(object? sender, EventArgs e)
        {
            MoveSelectedRule(-1);
        }

        /// <summary>
        /// 下移按钮点击事件
        /// </summary>
        private void BtnMoveDown_Click(object? sender, EventArgs e)
        {
            MoveSelectedRule(1);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            SaveSettings();
            DialogResult = DialogResult.OK;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }



        /// <summary>
        /// 窗体关闭事件 - 确保配置正确保存
        /// </summary>
        private void ParagraphFormatMatchingForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            // 如果是通过确定按钮或直接关闭窗口，自动保存配置
            if (DialogResult != DialogResult.Cancel)
            {
                SaveSettings();
            }
            // 如果是取消按钮，不保存配置
        }

        #endregion

        #region 规则操作方法

        /// <summary>
        /// 添加新规则
        /// </summary>
        private void AddNewRule()
        {
            var newRule = new ParagraphFormatMatchingRule
            {
                RuleName = $"规则{_currentSettings.MatchingRules.Count + 1}",
                IsEnabled = true,
                CreatedTime = DateTime.Now,
                LastModifiedTime = DateTime.Now
            };

            using var editForm = new ParagraphFormatRuleEditForm(newRule);
            if (editForm.ShowDialog(this) == DialogResult.OK)
            {
                _currentSettings.MatchingRules.Add(editForm.GetRule());
                LoadRulesList();
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// 编辑选中的规则
        /// </summary>
        private void EditSelectedRule()
        {
            if (_listViewRules.SelectedItems.Count == 0) return;

            var selectedItem = _listViewRules.SelectedItems[0];
            if (selectedItem.Tag is not ParagraphFormatMatchingRule rule) return;

            using var editForm = new ParagraphFormatRuleEditForm(rule);
            if (editForm.ShowDialog(this) == DialogResult.OK)
            {
                var updatedRule = editForm.GetRule();
                updatedRule.LastModifiedTime = DateTime.Now;

                var index = _currentSettings.MatchingRules.IndexOf(rule);
                if (index >= 0)
                {
                    _currentSettings.MatchingRules[index] = updatedRule;
                    LoadRulesList();
                    UpdateButtonStates();
                }
            }
        }

        /// <summary>
        /// 删除选中的规则
        /// </summary>
        private void DeleteSelectedRule()
        {
            if (_listViewRules.SelectedItems.Count == 0) return;

            var selectedItem = _listViewRules.SelectedItems[0];
            if (selectedItem.Tag is not ParagraphFormatMatchingRule rule) return;

            // 直接删除，不显示确认对话框
            _currentSettings.MatchingRules.Remove(rule);
            LoadRulesList();
            UpdateButtonStates();
        }

        /// <summary>
        /// 复制选中的规则
        /// </summary>
        private void CopySelectedRule()
        {
            if (_listViewRules.SelectedItems.Count == 0) return;

            var selectedItem = _listViewRules.SelectedItems[0];
            if (selectedItem.Tag is not ParagraphFormatMatchingRule rule) return;

            var copiedRule = CloneRule(rule);
            copiedRule.RuleName = $"{rule.RuleName} - 副本";
            copiedRule.CreatedTime = DateTime.Now;
            copiedRule.LastModifiedTime = DateTime.Now;

            _currentSettings.MatchingRules.Add(copiedRule);
            LoadRulesList();
            UpdateButtonStates();
        }

        /// <summary>
        /// 移动选中的规则
        /// </summary>
        private void MoveSelectedRule(int direction)
        {
            if (_listViewRules.SelectedItems.Count == 0) return;

            var selectedIndex = _listViewRules.SelectedIndices[0];
            var newIndex = selectedIndex + direction;

            if (newIndex < 0 || newIndex >= _currentSettings.MatchingRules.Count) return;

            var rule = _currentSettings.MatchingRules[selectedIndex];
            _currentSettings.MatchingRules.RemoveAt(selectedIndex);
            _currentSettings.MatchingRules.Insert(newIndex, rule);

            LoadRulesList();
            _listViewRules.Items[newIndex].Selected = true;
            _listViewRules.Items[newIndex].Focused = true;
            UpdateButtonStates();
        }

        /// <summary>
        /// 克隆规则
        /// </summary>
        private static ParagraphFormatMatchingRule CloneRule(ParagraphFormatMatchingRule source)
        {
            // 简单的深拷贝实现
            return new ParagraphFormatMatchingRule
            {
                IsEnabled = source.IsEnabled,
                RuleName = source.RuleName,
                MatchingConditions = CloneMatchingConditions(source.MatchingConditions),
                ParagraphFormat = CloneParagraphFormat(source.ParagraphFormat),
                FontFormat = CloneFontFormat(source.FontFormat),
                ReplacementScope = CloneReplacementScope(source.ReplacementScope)
            };
        }

        /// <summary>
        /// 克隆匹配条件
        /// </summary>
        private static ParagraphMatchingConditions CloneMatchingConditions(ParagraphMatchingConditions source)
        {
            return new ParagraphMatchingConditions
            {
                EnableStartsWith = source.EnableStartsWith,
                StartsWithText = source.StartsWithText,
                EnableContains = source.EnableContains,
                ContainsKeywords = new List<string>(source.ContainsKeywords),
                EnableEndsWith = source.EnableEndsWith,
                EndsWithText = source.EndsWithText,
                EnableRegex = source.EnableRegex,
                RegexPattern = source.RegexPattern,
                EnableCharacterCountLimit = source.EnableCharacterCountLimit,
                MinCharacterCount = source.MinCharacterCount,
                MaxCharacterCount = source.MaxCharacterCount,
                CaseSensitive = source.CaseSensitive,
                WholeWord = source.WholeWord
            };
        }

        /// <summary>
        /// 克隆段落格式
        /// </summary>
        private static Models.ParagraphFormatSettings CloneParagraphFormat(Models.ParagraphFormatSettings source)
        {
            return new Models.ParagraphFormatSettings
            {
                EnableParagraphFormat = source.EnableParagraphFormat,
                Alignment = source.Alignment,
                EnableIndentation = source.EnableIndentation,
                LeftIndent = source.LeftIndent,
                MarginLeft = source.MarginLeft,
                MarginRight = source.MarginRight,
                SpecialIndent = source.SpecialIndent,
                SpecialIndentValue = source.SpecialIndentValue,
                EnableSpacing = source.EnableSpacing,
                SpaceBefore = source.SpaceBefore,
                SpaceAfter = source.SpaceAfter,
                LineSpacingType = source.LineSpacingType,
                LineSpacingValue = source.LineSpacingValue,
                EnableChineseControl = source.EnableChineseControl,
                ChineseCharacterControl = source.ChineseCharacterControl,
                AllowLatinWordBreak = source.AllowLatinWordBreak,
                AllowPunctuationOverhang = source.AllowPunctuationOverhang,
                TextAlignment = source.TextAlignment,
                EnableFontAlignment = source.EnableFontAlignment,
                FontAlignment = source.FontAlignment,
                EnableRightToLeft = source.EnableRightToLeft,
                RightToLeft = source.RightToLeft
            };
        }

        /// <summary>
        /// 克隆字体格式
        /// </summary>
        private static Models.FontFormatSettings CloneFontFormat(Models.FontFormatSettings source)
        {
            return new Models.FontFormatSettings
            {
                EnableFontFormat = source.EnableFontFormat,
                SetChineseFont = source.SetChineseFont,
                ChineseFontName = source.ChineseFontName,
                SetLatinFont = source.SetLatinFont,
                LatinFontName = source.LatinFontName,
                FontStyle = source.FontStyle,
                SetFontSize = source.SetFontSize,
                FontSize = source.FontSize,
                SetFontColor = source.SetFontColor,
                FontColor = source.FontColor,
                SetUnderline = source.SetUnderline,
                UnderlineType = source.UnderlineType,
                UnderlineColor = source.UnderlineColor,
                SetTextEffects = source.SetTextEffects,
                Strikethrough = source.Strikethrough,
                DoubleStrikethrough = source.DoubleStrikethrough,
                Superscript = source.Superscript,
                Subscript = source.Subscript
            };
        }

        /// <summary>
        /// 克隆替换范围设置
        /// </summary>
        private static Models.ReplacementScope CloneReplacementScope(Models.ReplacementScope source)
        {
            return new Models.ReplacementScope
            {
                IncludeNormalSlides = source.IncludeNormalSlides,
                IncludeMasterSlides = source.IncludeMasterSlides,
                IncludeLayoutSlides = source.IncludeLayoutSlides
            };
        }

        #endregion
    }
}
