# PPT批量处理工具 - 日志系统说明

## 概述

本工具已完善了详细的日志系统，能够记录程序运行的各个关键环节，帮助用户了解程序状态、诊断问题和分析性能。

## 日志类型

### 1. 程序初始化日志 (`程序初始化`)
记录程序启动时的基本信息：
- 程序版本、启动时间
- 工作目录、操作系统信息
- .NET版本、机器名称、用户名
- Aspose.Slides.dll文件信息

### 2. 许可证日志 (`许可证`)
详细记录Aspose.Slides许可证的加载过程：
- 许可证文件路径和存在状态
- 许可证文件大小和修改时间
- 许可证加载和验证过程
- 许可证验证结果（成功/失败/试用版）

### 3. 配置加载日志 (`配置加载`)
记录配置服务的初始化：
- 配置服务启动状态
- 已加载的功能模块数量
- 配置加载成功/失败信息

### 4. 文件处理详情日志 (`文件处理详情`)
记录文件处理的详细过程：
- 用户操作参数（源路径、输出路径、处理模式等）
- 文件扫描结果（找到的文件数量、文件列表）
- 单个文件处理过程（文件信息、处理步骤、耗时）
- PowerPoint文件的详细处理信息

### 5. 内容删除日志 (`内容删除`)
记录内容删除操作的详情：
- 删除操作的类型和范围
- 删除的具体内容统计
- 删除操作的成功/失败状态

### 6. 性能统计日志 (`性能统计`)
记录处理性能的详细统计：
- 总文件数、成功/失败数量、成功率
- 总耗时、平均速度、每文件平均耗时
- 开始/结束时间

### 7. 异常详情日志 (`异常详情`)
记录详细的异常信息：
- 异常类型、异常消息
- 完整的堆栈跟踪
- 内部异常信息
- 相关文件路径

### 8. 系统状态日志 (`系统状态`)
记录系统环境和状态信息：
- 系统资源使用情况
- 环境变量信息
- 文件系统状态

## 日志配置

### 启用/禁用日志类型

在程序的"操作" → "日志设置"中可以配置：

```
✅ 程序初始化     - 记录程序启动信息
✅ 许可证         - 记录许可证加载过程
✅ 文件处理详情   - 记录详细的文件处理过程
✅ 配置加载       - 记录配置服务状态
✅ 内容删除       - 记录内容删除操作
✅ 异常详情       - 记录详细异常信息
✅ 系统状态       - 记录系统环境信息
❌ 性能统计       - 记录性能统计（默认关闭，避免日志过多）
❌ 网络通信       - 记录网络通信（如有）
❌ 调试信息       - 记录调试信息（开发用）
```

### 推荐配置

**日常使用**：
- 启用：程序初始化、许可证、异常详情、系统状态
- 禁用：文件处理详情、性能统计、调试信息

**问题诊断**：
- 启用：所有日志类型
- 特别关注：异常详情、文件处理详情

**性能分析**：
- 启用：性能统计、文件处理详情
- 关注：处理速度、耗时统计

## 日志文件位置

日志文件保存在程序目录下的 `Log` 文件夹中：
```
PPTPiliangChuli/
├── bin/Debug/net6.0-windows/
│   ├── Log/
│   │   ├── 2024-01-15_程序初始化.log
│   │   ├── 2024-01-15_许可证.log
│   │   ├── 2024-01-15_文件处理详情.log
│   │   ├── 2024-01-15_异常详情.log
│   │   └── ...
```

## 日志格式示例

### 程序初始化日志
```
[2024-01-15 10:30:15] [程序初始化] [INFO] === PPT批量处理工具启动 ===
[2024-01-15 10:30:15] [程序初始化] [INFO] 程序版本: 1.0.0.0
[2024-01-15 10:30:15] [程序初始化] [INFO] 启动时间: 2024-01-15 10:30:15
[2024-01-15 10:30:15] [程序初始化] [INFO] 工作目录: D:\PPTPiliangChuli\bin\Debug\net6.0-windows
```

### 许可证日志
```
[2024-01-15 10:30:16] [许可证] [INFO] === 开始加载Aspose.Slides许可证 ===
[2024-01-15 10:30:16] [许可证] [INFO] 许可证文件路径: D:\PPTPiliangChuli\bin\Debug\net6.0-windows\Aspose.Total.NET.lic
[2024-01-15 10:30:16] [许可证] [INFO] 许可证文件存在: True
[2024-01-15 10:30:16] [许可证] [INFO] 许可证文件大小: 2048 字节
[2024-01-15 10:30:16] [许可证] [INFO] === Aspose.Slides许可证加载成功 ===
```

### 文件处理详情日志
```
[2024-01-15 10:35:20] [文件处理详情] [INFO] === 用户点击开始处理按钮 ===
[2024-01-15 10:35:20] [文件处理详情] [INFO] 源路径: D:\TestPPT
[2024-01-15 10:35:20] [文件处理详情] [INFO] 输出路径: D:\Output
[2024-01-15 10:35:20] [文件处理详情] [INFO] 处理模式: 复制后处理
[2024-01-15 10:35:21] [文件处理详情] [INFO] 开始处理文件: test.pptx
[2024-01-15 10:35:21] [文件处理详情] [INFO] 文件大小: 1024.5 KB, 修改时间: 2024-01-15 09:30:00
[2024-01-15 10:35:22] [文件处理详情] [INFO] === 开始处理PowerPoint文件: test.pptx ===
[2024-01-15 10:35:22] [文件处理详情] [INFO] 演示文稿加载成功 - 幻灯片数量: 10
[2024-01-15 10:35:25] [文件处理详情] [INFO] === PowerPoint文件处理完成: test.pptx，总耗时: 3.25 秒 ===
```

### 性能统计日志
```
[2024-01-15 10:40:30] [性能统计] [INFO] === 批量处理性能统计 ===
[2024-01-15 10:40:30] [性能统计] [INFO] 总文件数: 50
[2024-01-15 10:40:30] [性能统计] [INFO] 成功处理: 48 (96.0%)
[2024-01-15 10:40:30] [性能统计] [INFO] 处理失败: 2
[2024-01-15 10:40:30] [性能统计] [INFO] 总耗时: 00:05:30
[2024-01-15 10:40:30] [性能统计] [INFO] 平均速度: 9.1 文件/分钟
[2024-01-15 10:40:30] [性能统计] [INFO] 平均每文件耗时: 6.60 秒
```

## 使用建议

1. **首次使用**：启用所有日志类型，了解程序运行状况
2. **日常使用**：只启用必要的日志类型，避免日志文件过大
3. **问题排查**：临时启用详细日志，定位问题后再关闭
4. **定期清理**：使用程序的"清空日志"功能清理旧日志文件
5. **许可证检查**：重点关注许可证日志，确保正版授权正常工作

## 注意事项

- 启用详细日志会增加磁盘空间占用
- 大量文件处理时，详细日志可能影响性能
- 日志文件按日期和类型分类存储，便于查找
- 异常详情日志对问题诊断最为重要，建议始终启用
