/// <summary>
/// 图表样式设置窗体文件
/// 用途：提供PPT文档中图表的样式设置功能，包括颜色方案、字体、主题、效果等
/// 功能：支持图表颜色方案设置、数据系列颜色自定义
/// 字体设置：支持图表标题、轴标签、数据标签的字体、大小、颜色设置
/// 主题设置：支持图表主题样式、背景、网格线样式等
/// 效果设置：支持图表阴影、发光、3D效果等特殊效果
/// 布局设置：支持图例位置、数据标签显示等布局参数
/// 符合Aspose.Slides API规范，使用Chart、ChartData、ChartSeries等接口
/// 作者：PPT批量处理工具
/// 创建时间：2024年
/// </summary>

using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 图表样式设置窗体
    /// </summary>
    public partial class ChartStyleSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 图表样式设置
        /// </summary>
        public ChartStyleSettings Settings { get; private set; } = new ChartStyleSettings();

        #region 控件字段
        private ComboBox? _cmbChartTheme, _cmbColorScheme;
        private Button? _btnPrimaryColor, _btnSecondaryColor, _btnAccentColor1, _btnAccentColor2, _btnAccentColor3, _btnAccentColor4;
        private ComboBox? _cmbTitleFontFamily, _cmbLabelFontFamily, _cmbLegendFontFamily;
        private NumericUpDown? _nudTitleFontSize, _nudLabelFontSize, _nudLegendFontSize;
        private Button? _btnTitleFontColor, _btnLabelFontColor, _btnLegendFontColor;
        private CheckBox? _chkShowBorder, _chkShowShadow, _chkShow3D, _chkShowAnimation;
        private Button? _btnBorderColor, _btnBackgroundColor;
        private ComboBox? _cmbBorderStyle, _cmbAnimationType;
        private NumericUpDown? _nudBorderWidth, _nudAnimationDuration;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;
        
        // 颜色存储
        private Color _primaryColor = Color.Blue;
        private Color _secondaryColor = Color.Orange;
        private Color _accentColor1 = Color.Green;
        private Color _accentColor2 = Color.Red;
        private Color _accentColor3 = Color.Purple;
        private Color _accentColor4 = Color.Brown;
        private Color _titleFontColor = Color.Black;
        private Color _labelFontColor = Color.Black;
        private Color _legendFontColor = Color.Black;
        private Color _borderColor = Color.Gray;
        private Color _backgroundColor = Color.White;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ChartStyleSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ChartStyleSettingsForm));
            SuspendLayout();
            // 
            // ChartStyleSettingsForm
            // 
            ClientSize = new Size(634, 611);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "ChartStyleSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "图表样式设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateThemeGroup();
            CreateColorGroup();
            CreateFontGroup();
            CreateBorderGroup();
            CreateEffectsGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建主题组
        /// </summary>
        private void CreateThemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "图表主题",
                Location = new Point(20, 20),
                Size = new Size(600, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblTheme = new Label
            {
                Text = "主题样式:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbChartTheme = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblColorScheme = new Label
            {
                Text = "配色方案:",
                Location = new Point(270, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbColorScheme = new ComboBox
            {
                Location = new Point(350, 23),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblTheme, _cmbChartTheme, lblColorScheme, _cmbColorScheme
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建颜色组
        /// </summary>
        private void CreateColorGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "自定义颜色",
                Location = new Point(20, 110),
                Size = new Size(600, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 第一行颜色
            var lblPrimary = new Label { Text = "主色:", Location = new Point(20, 25), Size = new Size(50, 20) };
            _btnPrimaryColor = CreateColorButton(_primaryColor, new Point(80, 23));
            _btnPrimaryColor.Click += (s, e) => SelectColor(ref _primaryColor, _btnPrimaryColor);

            var lblSecondary = new Label { Text = "辅色:", Location = new Point(150, 25), Size = new Size(50, 20) };
            _btnSecondaryColor = CreateColorButton(_secondaryColor, new Point(210, 23));
            _btnSecondaryColor.Click += (s, e) => SelectColor(ref _secondaryColor, _btnSecondaryColor);

            var lblAccent1 = new Label { Text = "强调1:", Location = new Point(280, 25), Size = new Size(50, 20) };
            _btnAccentColor1 = CreateColorButton(_accentColor1, new Point(340, 23));
            _btnAccentColor1.Click += (s, e) => SelectColor(ref _accentColor1, _btnAccentColor1);

            // 第二行颜色
            var lblAccent2 = new Label { Text = "强调2:", Location = new Point(20, 55), Size = new Size(50, 20) };
            _btnAccentColor2 = CreateColorButton(_accentColor2, new Point(80, 53));
            _btnAccentColor2.Click += (s, e) => SelectColor(ref _accentColor2, _btnAccentColor2);

            var lblAccent3 = new Label { Text = "强调3:", Location = new Point(150, 55), Size = new Size(50, 20) };
            _btnAccentColor3 = CreateColorButton(_accentColor3, new Point(210, 53));
            _btnAccentColor3.Click += (s, e) => SelectColor(ref _accentColor3, _btnAccentColor3);

            var lblAccent4 = new Label { Text = "强调4:", Location = new Point(280, 55), Size = new Size(50, 20) };
            _btnAccentColor4 = CreateColorButton(_accentColor4, new Point(340, 53));
            _btnAccentColor4.Click += (s, e) => SelectColor(ref _accentColor4, _btnAccentColor4);

            // 背景颜色
            var lblBackground = new Label { Text = "背景色:", Location = new Point(410, 25), Size = new Size(50, 20) };
            _btnBackgroundColor = CreateColorButton(_backgroundColor, new Point(470, 23));
            _btnBackgroundColor.Click += (s, e) => SelectColor(ref _backgroundColor, _btnBackgroundColor);

            groupBox.Controls.AddRange(new Control[] {
                lblPrimary, _btnPrimaryColor, lblSecondary, _btnSecondaryColor, lblAccent1, _btnAccentColor1,
                lblAccent2, _btnAccentColor2, lblAccent3, _btnAccentColor3, lblAccent4, _btnAccentColor4,
                lblBackground, _btnBackgroundColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建字体组
        /// </summary>
        private void CreateFontGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体设置",
                Location = new Point(20, 240),
                Size = new Size(600, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 标题字体
            var lblTitle = new Label { Text = "标题字体:", Location = new Point(20, 25), Size = new Size(70, 20) };
            _cmbTitleFontFamily = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _nudTitleFontSize = new NumericUpDown
            {
                Location = new Point(210, 23),
                Size = new Size(50, 23),
                Minimum = 8,
                Maximum = 36,
                Value = 14
            };

            _btnTitleFontColor = CreateColorButton(_titleFontColor, new Point(270, 23));
            _btnTitleFontColor.Click += (s, e) => SelectColor(ref _titleFontColor, _btnTitleFontColor);

            // 标签字体
            var lblLabel = new Label { Text = "标签字体:", Location = new Point(320, 25), Size = new Size(70, 20) };
            _cmbLabelFontFamily = new ComboBox
            {
                Location = new Point(400, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _nudLabelFontSize = new NumericUpDown
            {
                Location = new Point(510, 23),
                Size = new Size(50, 23),
                Minimum = 8,
                Maximum = 24,
                Value = 10
            };

            _btnLabelFontColor = CreateColorButton(_labelFontColor, new Point(570, 23));
            _btnLabelFontColor.Click += (s, e) => SelectColor(ref _labelFontColor, _btnLabelFontColor);

            // 图例字体
            var lblLegend = new Label { Text = "图例字体:", Location = new Point(20, 55), Size = new Size(70, 20) };
            _cmbLegendFontFamily = new ComboBox
            {
                Location = new Point(100, 53),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _nudLegendFontSize = new NumericUpDown
            {
                Location = new Point(210, 53),
                Size = new Size(50, 23),
                Minimum = 8,
                Maximum = 20,
                Value = 9
            };

            _btnLegendFontColor = CreateColorButton(_legendFontColor, new Point(270, 53));
            _btnLegendFontColor.Click += (s, e) => SelectColor(ref _legendFontColor, _btnLegendFontColor);

            groupBox.Controls.AddRange(new Control[] {
                lblTitle, _cmbTitleFontFamily, _nudTitleFontSize, _btnTitleFontColor,
                lblLabel, _cmbLabelFontFamily, _nudLabelFontSize, _btnLabelFontColor,
                lblLegend, _cmbLegendFontFamily, _nudLegendFontSize, _btnLegendFontColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建边框组
        /// </summary>
        private void CreateBorderGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "边框设置",
                Location = new Point(20, 370),
                Size = new Size(600, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _chkShowBorder = new CheckBox
            {
                Text = "显示边框",
                Location = new Point(20, 25),
                Size = new Size(80, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblStyle = new Label { Text = "样式:", Location = new Point(120, 25), Size = new Size(40, 20) };
            _cmbBorderStyle = new ComboBox
            {
                Location = new Point(170, 23),
                Size = new Size(80, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblWidth = new Label { Text = "宽度:", Location = new Point(270, 25), Size = new Size(40, 20) };
            _nudBorderWidth = new NumericUpDown
            {
                Location = new Point(320, 23),
                Size = new Size(50, 23),
                Minimum = 1,
                Maximum = 10,
                Value = 1
            };

            var lblColor = new Label { Text = "颜色:", Location = new Point(390, 25), Size = new Size(40, 20) };
            _btnBorderColor = CreateColorButton(_borderColor, new Point(440, 23));
            _btnBorderColor.Click += (s, e) => SelectColor(ref _borderColor, _btnBorderColor);

            groupBox.Controls.AddRange(new Control[] {
                _chkShowBorder, lblStyle, _cmbBorderStyle, lblWidth, _nudBorderWidth, lblColor, _btnBorderColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建效果组
        /// </summary>
        private void CreateEffectsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "视觉效果",
                Location = new Point(20, 460),
                Size = new Size(600, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _chkShowShadow = new CheckBox { Text = "阴影效果", Location = new Point(20, 25), Size = new Size(80, 20) };
            _chkShow3D = new CheckBox { Text = "3D效果", Location = new Point(120, 25), Size = new Size(70, 20) };
            _chkShowAnimation = new CheckBox { Text = "动画效果", Location = new Point(210, 25), Size = new Size(80, 20) };

            var lblAnimationType = new Label { Text = "动画类型:", Location = new Point(310, 25), Size = new Size(70, 20) };
            _cmbAnimationType = new ComboBox
            {
                Location = new Point(390, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblDuration = new Label { Text = "动画时长:", Location = new Point(20, 55), Size = new Size(70, 20) };
            _nudAnimationDuration = new NumericUpDown
            {
                Location = new Point(100, 53),
                Size = new Size(60, 23),
                Minimum = 0.5m,
                Maximum = 10.0m,
                DecimalPlaces = 1,
                Increment = 0.5m,
                Value = 2.0m
            };

            var lblSeconds = new Label { Text = "秒", Location = new Point(170, 55), Size = new Size(20, 20) };

            _btnPreview = new Button
            {
                Text = "预览效果",
                Location = new Point(500, 52),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 8F)
            };
            _btnPreview.Click += BtnPreview_Click;

            groupBox.Controls.AddRange(new Control[] {
                _chkShowShadow, _chkShow3D, _chkShowAnimation, lblAnimationType, _cmbAnimationType,
                lblDuration, _nudAnimationDuration, lblSeconds, _btnPreview
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(420, 580),
                Size = new Size(60, 30),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(490, 580),
                Size = new Size(60, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _btnReset = new Button
            {
                Text = "重置",
                Location = new Point(560, 580),
                Size = new Size(50, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 图表主题
            var themes = new string[] { "默认", "现代", "经典", "简洁", "彩色", "商务", "艺术" };
            _cmbChartTheme?.Items.AddRange(themes);
            if (_cmbChartTheme != null)
                _cmbChartTheme.SelectedItem = "默认";

            // 配色方案
            var colorSchemes = new string[] { "Office", "彩虹", "单色", "对比", "暖色调", "冷色调", "自定义" };
            _cmbColorScheme?.Items.AddRange(colorSchemes);
            if (_cmbColorScheme != null)
                _cmbColorScheme.SelectedItem = "Office";

            // 字体列表
            var fonts = new string[] { "Microsoft YaHei UI", "Arial", "Calibri", "Times New Roman" };
            _cmbTitleFontFamily?.Items.AddRange(fonts);
            _cmbLabelFontFamily?.Items.AddRange(fonts);
            _cmbLegendFontFamily?.Items.AddRange(fonts);
            
            if (_cmbTitleFontFamily != null) _cmbTitleFontFamily.SelectedItem = "Microsoft YaHei UI";
            if (_cmbLabelFontFamily != null) _cmbLabelFontFamily.SelectedItem = "Microsoft YaHei UI";
            if (_cmbLegendFontFamily != null) _cmbLegendFontFamily.SelectedItem = "Microsoft YaHei UI";

            // 边框样式
            var borderStyles = new string[] { "实线", "虚线", "点线", "无边框" };
            _cmbBorderStyle?.Items.AddRange(borderStyles);
            if (_cmbBorderStyle != null)
                _cmbBorderStyle.SelectedItem = "实线";

            // 动画类型
            var animationTypes = new string[] { "淡入", "飞入", "缩放", "旋转", "弹跳", "无动画" };
            _cmbAnimationType?.Items.AddRange(animationTypes);
            if (_cmbAnimationType != null)
                _cmbAnimationType.SelectedItem = "淡入";
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 创建颜色按钮
        /// </summary>
        /// <param name="color">颜色</param>
        /// <param name="location">位置</param>
        /// <returns>颜色按钮</returns>
        private Button CreateColorButton(Color color, Point location)
        {
            return new Button
            {
                Location = location,
                Size = new Size(30, 23),
                BackColor = color,
                FlatStyle = FlatStyle.Flat
            };
        }

        /// <summary>
        /// 选择颜色
        /// </summary>
        /// <param name="colorField">颜色字段</param>
        /// <param name="button">颜色按钮</param>
        private void SelectColor(ref Color colorField, Button? button)
        {
            if (button == null) return;

            using var colorDialog = new ColorDialog
            {
                Color = colorField,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                colorField = colorDialog.Color;
                button.BackColor = colorField;
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能显示当前图表样式的效果。\n\n" +
                           "在实际应用中，这里会显示图表样式预览。", "样式预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new ChartStyleSettings
                {
                    ChartTheme = _cmbChartTheme?.SelectedItem?.ToString() ?? "默认",
                    ColorScheme = _cmbColorScheme?.SelectedItem?.ToString() ?? "Office",
                    PrimaryColor = _primaryColor,
                    SecondaryColor = _secondaryColor,
                    AccentColor1 = _accentColor1,
                    AccentColor2 = _accentColor2,
                    AccentColor3 = _accentColor3,
                    AccentColor4 = _accentColor4,
                    BackgroundColor = _backgroundColor,
                    TitleFontFamily = _cmbTitleFontFamily?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    TitleFontSize = (int)(_nudTitleFontSize?.Value ?? 14),
                    TitleFontColor = _titleFontColor,
                    LabelFontFamily = _cmbLabelFontFamily?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    LabelFontSize = (int)(_nudLabelFontSize?.Value ?? 10),
                    LabelFontColor = _labelFontColor,
                    LegendFontFamily = _cmbLegendFontFamily?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    LegendFontSize = (int)(_nudLegendFontSize?.Value ?? 9),
                    LegendFontColor = _legendFontColor,
                    ShowBorder = _chkShowBorder?.Checked ?? false,
                    BorderStyle = _cmbBorderStyle?.SelectedItem?.ToString() ?? "实线",
                    BorderWidth = (int)(_nudBorderWidth?.Value ?? 1),
                    BorderColor = _borderColor,
                    ShowShadow = _chkShowShadow?.Checked ?? false,
                    Show3D = _chkShow3D?.Checked ?? false,
                    ShowAnimation = _chkShowAnimation?.Checked ?? false,
                    AnimationType = _cmbAnimationType?.SelectedItem?.ToString() ?? "淡入",
                    AnimationDuration = (double)(_nudAnimationDuration?.Value ?? 2.0m)
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();
            
            // 重置数值控件
            if (_nudTitleFontSize != null) _nudTitleFontSize.Value = 14;
            if (_nudLabelFontSize != null) _nudLabelFontSize.Value = 10;
            if (_nudLegendFontSize != null) _nudLegendFontSize.Value = 9;
            if (_nudBorderWidth != null) _nudBorderWidth.Value = 1;
            if (_nudAnimationDuration != null) _nudAnimationDuration.Value = 2.0m;
            
            // 重置复选框
            if (_chkShowBorder != null) _chkShowBorder.Checked = false;
            if (_chkShowShadow != null) _chkShowShadow.Checked = false;
            if (_chkShow3D != null) _chkShow3D.Checked = false;
            if (_chkShowAnimation != null) _chkShowAnimation.Checked = false;
            
            // 重置颜色
            _primaryColor = Color.Blue;
            _secondaryColor = Color.Orange;
            _accentColor1 = Color.Green;
            _accentColor2 = Color.Red;
            _accentColor3 = Color.Purple;
            _accentColor4 = Color.Brown;
            _titleFontColor = Color.Black;
            _labelFontColor = Color.Black;
            _legendFontColor = Color.Black;
            _borderColor = Color.Gray;
            _backgroundColor = Color.White;
            
            // 更新颜色按钮
            if (_btnPrimaryColor != null) _btnPrimaryColor.BackColor = _primaryColor;
            if (_btnSecondaryColor != null) _btnSecondaryColor.BackColor = _secondaryColor;
            if (_btnAccentColor1 != null) _btnAccentColor1.BackColor = _accentColor1;
            if (_btnAccentColor2 != null) _btnAccentColor2.BackColor = _accentColor2;
            if (_btnAccentColor3 != null) _btnAccentColor3.BackColor = _accentColor3;
            if (_btnAccentColor4 != null) _btnAccentColor4.BackColor = _accentColor4;
            if (_btnTitleFontColor != null) _btnTitleFontColor.BackColor = _titleFontColor;
            if (_btnLabelFontColor != null) _btnLabelFontColor.BackColor = _labelFontColor;
            if (_btnLegendFontColor != null) _btnLegendFontColor.BackColor = _legendFontColor;
            if (_btnBorderColor != null) _btnBorderColor.BackColor = _borderColor;
            if (_btnBackgroundColor != null) _btnBackgroundColor.BackColor = _backgroundColor;
        }

        #endregion
    }

    /// <summary>
    /// 图表样式设置类
    /// </summary>
    public class ChartStyleSettings
    {
        public string ChartTheme { get; set; } = "默认";
        public string ColorScheme { get; set; } = "Office";
        public Color PrimaryColor { get; set; } = Color.Blue;
        public Color SecondaryColor { get; set; } = Color.Orange;
        public Color AccentColor1 { get; set; } = Color.Green;
        public Color AccentColor2 { get; set; } = Color.Red;
        public Color AccentColor3 { get; set; } = Color.Purple;
        public Color AccentColor4 { get; set; } = Color.Brown;
        public Color BackgroundColor { get; set; } = Color.White;
        public string TitleFontFamily { get; set; } = "Microsoft YaHei UI";
        public int TitleFontSize { get; set; } = 14;
        public Color TitleFontColor { get; set; } = Color.Black;
        public string LabelFontFamily { get; set; } = "Microsoft YaHei UI";
        public int LabelFontSize { get; set; } = 10;
        public Color LabelFontColor { get; set; } = Color.Black;
        public string LegendFontFamily { get; set; } = "Microsoft YaHei UI";
        public int LegendFontSize { get; set; } = 9;
        public Color LegendFontColor { get; set; } = Color.Black;
        public bool ShowBorder { get; set; } = false;
        public string BorderStyle { get; set; } = "实线";
        public int BorderWidth { get; set; } = 1;
        public Color BorderColor { get; set; } = Color.Gray;
        public bool ShowShadow { get; set; } = false;
        public bool Show3D { get; set; } = false;
        public bool ShowAnimation { get; set; } = false;
        public string AnimationType { get; set; } = "淡入";
        public double AnimationDuration { get; set; } = 2.0;
    }
}
