{
  // 路径配置文件 - 控制PPT文件的输入输出路径和处理模式
  // 此文件定义了源文件路径、输出路径、处理模式和冲突处理方式

  // 源文件路径 - PPT文件的输入目录路径
  "SourcePath": "",

  // 输出路径 - 处理后PPT文件的输出目录路径
  "OutputPath": "",

  // 包含子文件夹 - 是否处理源路径下的子文件夹中的PPT文件
  "IncludeSubfolders": false,

  // 保持目录结构 - 是否在输出路径中保持原有的目录结构
  "KeepDirectoryStructure": true,

  // 处理模式 - 文件处理方式：0=直接处理源文件，1=复制到输出目录后处理，2=移动到输出目录后处理
  "ProcessMode": 0,

  // 冲突处理 - 文件名冲突时的处理方式：0=覆盖现有文件，1=跳过冲突文件，2=重命名新文件
  "ConflictHandling": 0
}
