/// <summary>
/// 文本样式设置窗体文件
/// 用途：提供PPT文档中文本的样式设置功能，包括字体、颜色、效果、对齐、间距等
/// 功能：支持字体选择、字体大小、字体样式（粗体、斜体、下划线、删除线）设置
/// 颜色设置：支持字体颜色、背景颜色、阴影颜色设置
/// 效果设置：支持阴影、轮廓、浮雕、雕刻等文本特效
/// 对齐设置：支持水平对齐、垂直对齐设置
/// 间距设置：支持行距、字符间距、缩进设置
/// 符合Aspose.Slides API规范，使用PortionFormat、ParagraphFormat等接口
/// 作者：PPT批量处理工具
/// 创建时间：2024年
/// </summary>

using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 文本样式设置窗体
    /// </summary>
    public partial class TextStyleSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 文本样式设置
        /// </summary>
        public TextStyleSettings Settings { get; private set; } = new TextStyleSettings();

        #region 控件字段
        private ComboBox? _cmbFontFamily, _cmbFontStyle;
        private NumericUpDown? _nudFontSize, _nudLineSpacing, _nudCharacterSpacing;
        private Button? _btnFontColor, _btnBackgroundColor, _btnShadowColor;
        private CheckBox? _chkBold, _chkItalic, _chkUnderline, _chkStrikethrough;
        private CheckBox? _chkShadow, _chkOutline, _chkEmboss, _chkEngrave;
        private ComboBox? _cmbTextAlignment, _cmbVerticalAlignment;
        private NumericUpDown? _nudIndentLeft, _nudIndentRight;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;

        // 颜色存储
        private Color _fontColor = Color.Black;
        private Color _backgroundColor = Color.Transparent;
        private Color _shadowColor = Color.Gray;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TextStyleSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TextStyleSettingsForm));
            SuspendLayout();
            // 
            // TextStyleSettingsForm
            // 
            ClientSize = new Size(578, 494);
            Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "TextStyleSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "文本样式设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateFontGroup();
            CreateColorGroup();
            CreateEffectsGroup();
            CreateAlignmentGroup();
            CreateSpacingGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建字体组
        /// </summary>
        private void CreateFontGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体设置",
                Location = new Point(20, 20),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblFamily = new Label
            {
                Text = "字体:",
                Location = new Point(20, 25),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbFontFamily = new ComboBox
            {
                Location = new Point(80, 23),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblSize = new Label
            {
                Text = "大小:",
                Location = new Point(250, 25),
                Size = new Size(40, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudFontSize = new NumericUpDown
            {
                Location = new Point(300, 23),
                Size = new Size(60, 23),
                Minimum = 8,
                Maximum = 72,
                Value = 12,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblStyle = new Label
            {
                Text = "样式:",
                Location = new Point(380, 25),
                Size = new Size(40, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbFontStyle = new ComboBox
            {
                Location = new Point(430, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            // 字体样式复选框
            _chkBold = new CheckBox { Text = "粗体", Location = new Point(20, 55), Size = new Size(60, 20) };
            _chkItalic = new CheckBox { Text = "斜体", Location = new Point(90, 55), Size = new Size(60, 20) };
            _chkUnderline = new CheckBox { Text = "下划线", Location = new Point(160, 55), Size = new Size(70, 20) };
            _chkStrikethrough = new CheckBox { Text = "删除线", Location = new Point(240, 55), Size = new Size(70, 20) };

            groupBox.Controls.AddRange(new Control[] {
                lblFamily, _cmbFontFamily, lblSize, _nudFontSize, lblStyle, _cmbFontStyle,
                _chkBold, _chkItalic, _chkUnderline, _chkStrikethrough
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建颜色组
        /// </summary>
        private void CreateColorGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "颜色设置",
                Location = new Point(20, 130),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblFont = new Label
            {
                Text = "字体颜色:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnFontColor = new Button
            {
                Location = new Point(100, 23),
                Size = new Size(50, 25),
                BackColor = _fontColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnFontColor.Click += (s, e) => SelectColor(ref _fontColor, _btnFontColor);

            var lblBackground = new Label
            {
                Text = "背景颜色:",
                Location = new Point(170, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnBackgroundColor = new Button
            {
                Location = new Point(250, 23),
                Size = new Size(50, 25),
                BackColor = _backgroundColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnBackgroundColor.Click += (s, e) => SelectColor(ref _backgroundColor, _btnBackgroundColor);

            var lblShadow = new Label
            {
                Text = "阴影颜色:",
                Location = new Point(320, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnShadowColor = new Button
            {
                Location = new Point(400, 23),
                Size = new Size(50, 25),
                BackColor = _shadowColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnShadowColor.Click += (s, e) => SelectColor(ref _shadowColor, _btnShadowColor);

            groupBox.Controls.AddRange(new Control[] {
                lblFont, _btnFontColor, lblBackground, _btnBackgroundColor, lblShadow, _btnShadowColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建效果组
        /// </summary>
        private void CreateEffectsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "文本效果",
                Location = new Point(20, 220),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _chkShadow = new CheckBox { Text = "阴影", Location = new Point(20, 25), Size = new Size(60, 20) };
            _chkOutline = new CheckBox { Text = "轮廓", Location = new Point(90, 25), Size = new Size(60, 20) };
            _chkEmboss = new CheckBox { Text = "浮雕", Location = new Point(160, 25), Size = new Size(60, 20) };
            _chkEngrave = new CheckBox { Text = "雕刻", Location = new Point(230, 25), Size = new Size(60, 20) };

            groupBox.Controls.AddRange(new Control[] { _chkShadow, _chkOutline, _chkEmboss, _chkEngrave });
            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建对齐组
        /// </summary>
        private void CreateAlignmentGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "对齐方式",
                Location = new Point(20, 310),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblHorizontal = new Label
            {
                Text = "水平对齐:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTextAlignment = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblVertical = new Label
            {
                Text = "垂直对齐:",
                Location = new Point(220, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbVerticalAlignment = new ComboBox
            {
                Location = new Point(300, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblHorizontal, _cmbTextAlignment, lblVertical, _cmbVerticalAlignment
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建间距组
        /// </summary>
        private void CreateSpacingGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "间距设置",
                Location = new Point(20, 400),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblLine = new Label
            {
                Text = "行距:",
                Location = new Point(20, 25),
                Size = new Size(40, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudLineSpacing = new NumericUpDown
            {
                Location = new Point(70, 23),
                Size = new Size(60, 23),
                Minimum = 0.5m,
                Maximum = 5.0m,
                DecimalPlaces = 1,
                Increment = 0.1m,
                Value = 1.0m
            };

            var lblChar = new Label
            {
                Text = "字符间距:",
                Location = new Point(150, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudCharacterSpacing = new NumericUpDown
            {
                Location = new Point(230, 23),
                Size = new Size(60, 23),
                Minimum = -10,
                Maximum = 50,
                Value = 0
            };

            var lblIndent = new Label
            {
                Text = "缩进:",
                Location = new Point(310, 25),
                Size = new Size(40, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblLeft = new Label { Text = "左:", Location = new Point(360, 25), Size = new Size(25, 20) };
            _nudIndentLeft = new NumericUpDown
            {
                Location = new Point(390, 23),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };

            var lblRight = new Label { Text = "右:", Location = new Point(450, 25), Size = new Size(25, 20) };
            _nudIndentRight = new NumericUpDown
            {
                Location = new Point(480, 23),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };

            groupBox.Controls.AddRange(new Control[] {
                lblLine, _nudLineSpacing, lblChar, _nudCharacterSpacing,
                lblIndent, lblLeft, _nudIndentLeft, lblRight, _nudIndentRight
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnPreview = new Button
            {
                Text = "预览",
                Location = new Point(320, 500),
                Size = new Size(60, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnPreview.Click += BtnPreview_Click;

            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(390, 500),
                Size = new Size(60, 30),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(460, 500),
                Size = new Size(60, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _btnReset = new Button
            {
                Text = "重置",
                Location = new Point(530, 500),
                Size = new Size(50, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnPreview, _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 加载字体列表
            var commonFonts = new string[]
            {
                "Microsoft YaHei UI", "Microsoft YaHei", "SimSun", "SimHei", "KaiTi",
                "Arial", "Times New Roman", "Calibri", "Verdana", "Tahoma"
            };
            _cmbFontFamily?.Items.AddRange(commonFonts);
            if (_cmbFontFamily != null)
                _cmbFontFamily.SelectedItem = "Microsoft YaHei UI";

            // 字体样式
            var fontStyles = new string[] { "常规", "粗体", "斜体", "粗斜体" };
            _cmbFontStyle?.Items.AddRange(fontStyles);
            if (_cmbFontStyle != null)
                _cmbFontStyle.SelectedItem = "常规";

            // 水平对齐
            var alignments = new string[] { "左对齐", "居中", "右对齐", "两端对齐" };
            _cmbTextAlignment?.Items.AddRange(alignments);
            if (_cmbTextAlignment != null)
                _cmbTextAlignment.SelectedItem = "左对齐";

            // 垂直对齐
            var verticalAlignments = new string[] { "顶部", "居中", "底部" };
            _cmbVerticalAlignment?.Items.AddRange(verticalAlignments);
            if (_cmbVerticalAlignment != null)
                _cmbVerticalAlignment.SelectedItem = "顶部";

            // 设置控件文字对齐
            SetControlsTextAlignment();
        }

        /// <summary>
        /// 设置控件文字对齐
        /// </summary>
        private void SetControlsTextAlignment()
        {
            // 设置数值输入框文字居中
            SetNumericUpDownTextAlign(_nudFontSize);
            SetNumericUpDownTextAlign(_nudLineSpacing);
            SetNumericUpDownTextAlign(_nudCharacterSpacing);
            SetNumericUpDownTextAlign(_nudIndentLeft);
            SetNumericUpDownTextAlign(_nudIndentRight);

            // 设置下拉框文字居中
            SetComboBoxTextAlign(_cmbFontFamily);
            SetComboBoxTextAlign(_cmbFontStyle);
            SetComboBoxTextAlign(_cmbTextAlignment);
            SetComboBoxTextAlign(_cmbVerticalAlignment);
        }

        /// <summary>
        /// 设置NumericUpDown文字居中显示
        /// </summary>
        private static void SetNumericUpDownTextAlign(NumericUpDown? numericUpDown)
        {
            if (numericUpDown != null)
                numericUpDown.TextAlign = HorizontalAlignment.Center;
        }

        /// <summary>
        /// 设置ComboBox文字居中显示
        /// </summary>
        private static void SetComboBoxTextAlign(ComboBox? comboBox)
        {
            if (comboBox == null) return;

            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = comboBox.Items[e.Index].ToString();
                if (!string.IsNullOrEmpty(text))
                {
                    var textBounds = e.Bounds;
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                    // 修复编译警告：确保字体不为null
                    var font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;
                    TextRenderer.DrawText(e.Graphics, text, font, textBounds, e.ForeColor, textFlags);
                }

                e.DrawFocusRectangle();
            };
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 选择颜色
        /// </summary>
        /// <param name="colorField">颜色字段</param>
        /// <param name="button">颜色按钮</param>
        private void SelectColor(ref Color colorField, Button? button)
        {
            if (button == null) return;

            using var colorDialog = new ColorDialog
            {
                Color = colorField,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                colorField = colorDialog.Color;
                button.BackColor = colorField;
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能显示当前文本样式的效果。\n\n" +
                           "在实际应用中，这里会显示样式预览。", "样式预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new TextStyleSettings
                {
                    FontFamily = _cmbFontFamily?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    FontSize = (int)(_nudFontSize?.Value ?? 12),
                    FontStyle = _cmbFontStyle?.SelectedItem?.ToString() ?? "常规",
                    IsBold = _chkBold?.Checked ?? false,
                    IsItalic = _chkItalic?.Checked ?? false,
                    IsUnderline = _chkUnderline?.Checked ?? false,
                    IsStrikethrough = _chkStrikethrough?.Checked ?? false,
                    FontColor = _fontColor,
                    BackgroundColor = _backgroundColor,
                    ShadowColor = _shadowColor,
                    HasShadow = _chkShadow?.Checked ?? false,
                    HasOutline = _chkOutline?.Checked ?? false,
                    HasEmboss = _chkEmboss?.Checked ?? false,
                    HasEngrave = _chkEngrave?.Checked ?? false,
                    TextAlignment = _cmbTextAlignment?.SelectedItem?.ToString() ?? "左对齐",
                    VerticalAlignment = _cmbVerticalAlignment?.SelectedItem?.ToString() ?? "顶部",
                    LineSpacing = (double)(_nudLineSpacing?.Value ?? 1.0m),
                    CharacterSpacing = (int)(_nudCharacterSpacing?.Value ?? 0),
                    IndentLeft = (int)(_nudIndentLeft?.Value ?? 0),
                    IndentRight = (int)(_nudIndentRight?.Value ?? 0)
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();

            // 重置数值控件
            if (_nudFontSize != null) _nudFontSize.Value = 12;
            if (_nudLineSpacing != null) _nudLineSpacing.Value = 1.0m;
            if (_nudCharacterSpacing != null) _nudCharacterSpacing.Value = 0;
            if (_nudIndentLeft != null) _nudIndentLeft.Value = 0;
            if (_nudIndentRight != null) _nudIndentRight.Value = 0;

            // 重置复选框
            if (_chkBold != null) _chkBold.Checked = false;
            if (_chkItalic != null) _chkItalic.Checked = false;
            if (_chkUnderline != null) _chkUnderline.Checked = false;
            if (_chkStrikethrough != null) _chkStrikethrough.Checked = false;
            if (_chkShadow != null) _chkShadow.Checked = false;
            if (_chkOutline != null) _chkOutline.Checked = false;
            if (_chkEmboss != null) _chkEmboss.Checked = false;
            if (_chkEngrave != null) _chkEngrave.Checked = false;

            // 重置颜色
            _fontColor = Color.Black;
            _backgroundColor = Color.Transparent;
            _shadowColor = Color.Gray;
            if (_btnFontColor != null) _btnFontColor.BackColor = _fontColor;
            if (_btnBackgroundColor != null) _btnBackgroundColor.BackColor = _backgroundColor;
            if (_btnShadowColor != null) _btnShadowColor.BackColor = _shadowColor;
        }

        #endregion
    }

    /// <summary>
    /// 文本样式设置类
    /// </summary>
    public class TextStyleSettings
    {
        public string FontFamily { get; set; } = "Microsoft YaHei UI";
        public int FontSize { get; set; } = 12;
        public string FontStyle { get; set; } = "常规";
        public bool IsBold { get; set; } = false;
        public bool IsItalic { get; set; } = false;
        public bool IsUnderline { get; set; } = false;
        public bool IsStrikethrough { get; set; } = false;
        public Color FontColor { get; set; } = Color.Black;
        public Color BackgroundColor { get; set; } = Color.Transparent;
        public Color ShadowColor { get; set; } = Color.Gray;
        public bool HasShadow { get; set; } = false;
        public bool HasOutline { get; set; } = false;
        public bool HasEmboss { get; set; } = false;
        public bool HasEngrave { get; set; } = false;
        public string TextAlignment { get; set; } = "左对齐";
        public string VerticalAlignment { get; set; } = "顶部";
        public double LineSpacing { get; set; } = 1.0;
        public int CharacterSpacing { get; set; } = 0;
        public int IndentLeft { get; set; } = 0;
        public int IndentRight { get; set; } = 0;
    }
}
