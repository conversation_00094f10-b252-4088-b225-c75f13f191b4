﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup />
  <ItemGroup>
    <Compile Update="Forms\ChartStyleSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ContentDeletionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ContentDeletionForm.Helpers.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ContentLayoutSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ContentReplacementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ContentReplacementForm.Events.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ContentReplacementForm.Helpers.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ContentReplacementForm.Settings.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CustomColorReplacementRuleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CustomColorSchemeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CustomFontSchemeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CustomLayoutSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\DocumentPropertiesSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\FilenamePatternReplacementRuleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\FilenameReplacementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\FontNameReplacementRuleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\FontStyleReplacementRuleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\HandoutMasterSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\HeaderFooterSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\IllegalWordsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ImageReplacementRuleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\KeywordEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\LogSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\NotesMasterSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PageSetupForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ParagraphFormatMatchingForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ParagraphFormatRuleEditForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Update="Forms\ParagraphFormatRuleEditForm.Events.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ParagraphFormatRuleEditForm.Methods.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PictureLayoutSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PositionHelperForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PPTFormatConversionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PPTFormatConversionForm.DataMethods.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PPTFormatSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ScheduleSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ShapeStyleReplacementRuleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ShapeStyleSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\SupportedFormatsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TableStyleSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TextBoxReplacementRuleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TextReplacementRuleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TextStyleSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ThemeEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TitleSlideLayoutSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TwoColumnLayoutSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
</Project>