using System;
using System.Drawing;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 两栏布局设置窗体
    /// </summary>
    public partial class TwoColumnLayoutSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 两栏布局设置
        /// </summary>
        public TwoColumnLayoutSettings Settings { get; private set; } = new TwoColumnLayoutSettings();

        #region 控件字段
        private NumericUpDown? _nudLeftColumnWidth, _nudRightColumnWidth;
        private NumericUpDown? _nudColumnSpacing, _nudTopMargin, _nudBottomMargin, _nudLeftMargin, _nudRightMargin;
        private ComboBox? _cmbLeftContentType, _cmbRightContentType;
        private CheckBox? _chkShowColumnTitles, _chkAutoBalance, _chkEqualHeight;
        private TextBox? _txtLeftColumnTitle, _txtRightColumnTitle;
        private ComboBox? _cmbTitleAlignment;
        private ComboBox? _cmbTitleFontFamily, _cmbContentFontFamily;
        private NumericUpDown? _nudTitleFontSize, _nudContentFontSize;
        private Button? _btnTitleFontColor, _btnContentFontColor;
        private Button? _btnOK, _btnCancel, _btnReset, _btnPreview;

        // 颜色存储
        private Color _titleFontColor = Color.Black;
        private Color _contentFontColor = Color.Black;
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TwoColumnLayoutSettingsForm()
        {
            InitializeComponent();
            InitializeControls();
            LoadDefaultValues();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TwoColumnLayoutSettingsForm));
            SuspendLayout();
            // 
            // TwoColumnLayoutSettingsForm
            // 
            ClientSize = new Size(778, 644);
            Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon?)resources.GetObject("$this.Icon");
            MaximizeBox = false;
            MinimizeBox = false;
            MinimumSize = new Size(800, 700);
            Name = "TwoColumnLayoutSettingsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "两栏布局设置";
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            CreateColumnWidthGroup();
            CreateSpacingGroup();
            CreateContentTypeGroup();
            CreateTitleGroup();
            CreateFormatGroup();
            CreateActionButtons();
        }

        /// <summary>
        /// 创建栏宽度组
        /// </summary>
        private void CreateColumnWidthGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "栏宽度设置",
                Location = new Point(20, 20),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblLeft = new Label
            {
                Text = "左栏宽度:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudLeftColumnWidth = new NumericUpDown
            {
                Location = new Point(100, 23),
                Size = new Size(60, 23),
                Minimum = 10,
                Maximum = 90,
                Value = 50,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblLeftUnit = new Label { Text = "%", Location = new Point(170, 25), Size = new Size(20, 20) };

            var lblRight = new Label
            {
                Text = "右栏宽度:",
                Location = new Point(200, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudRightColumnWidth = new NumericUpDown
            {
                Location = new Point(280, 23),
                Size = new Size(60, 23),
                Minimum = 10,
                Maximum = 90,
                Value = 50,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblRightUnit = new Label { Text = "%", Location = new Point(350, 25), Size = new Size(20, 20) };

            _chkAutoBalance = new CheckBox
            {
                Text = "自动平衡宽度",
                Location = new Point(380, 25),
                Size = new Size(120, 20),
                Checked = true,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            // 自动平衡事件
            _chkAutoBalance.CheckedChanged += (s, e) =>
            {
                if (_chkAutoBalance.Checked)
                {
                    _nudLeftColumnWidth!.Value = 50;
                    _nudRightColumnWidth!.Value = 50;
                    _nudLeftColumnWidth.Enabled = false;
                    _nudRightColumnWidth.Enabled = false;
                }
                else
                {
                    _nudLeftColumnWidth!.Enabled = true;
                    _nudRightColumnWidth!.Enabled = true;
                }
            };

            groupBox.Controls.AddRange(new Control[] {
                lblLeft, _nudLeftColumnWidth, lblLeftUnit, lblRight, _nudRightColumnWidth, lblRightUnit, _chkAutoBalance
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建间距组
        /// </summary>
        private void CreateSpacingGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "间距和边距",
                Location = new Point(20, 110),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblSpacing = new Label
            {
                Text = "栏间距:",
                Location = new Point(20, 25),
                Size = new Size(60, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _nudColumnSpacing = new NumericUpDown
            {
                Location = new Point(90, 23),
                Size = new Size(60, 23),
                Minimum = 5,
                Maximum = 100,
                Value = 20,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblSpacingUnit = new Label { Text = "磅", Location = new Point(160, 25), Size = new Size(20, 20) };

            _chkEqualHeight = new CheckBox
            {
                Text = "等高显示",
                Location = new Point(200, 25),
                Size = new Size(80, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            // 边距设置
            var lblMargins = new Label
            {
                Text = "页面边距:",
                Location = new Point(20, 55),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblTop = new Label { Text = "上:", Location = new Point(100, 55), Size = new Size(20, 20) };
            _nudTopMargin = new NumericUpDown
            {
                Location = new Point(125, 53),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 20
            };

            var lblLeft = new Label { Text = "左:", Location = new Point(185, 55), Size = new Size(20, 20) };
            _nudLeftMargin = new NumericUpDown
            {
                Location = new Point(210, 53),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 20
            };

            var lblRight = new Label { Text = "右:", Location = new Point(270, 55), Size = new Size(20, 20) };
            _nudRightMargin = new NumericUpDown
            {
                Location = new Point(295, 53),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 20
            };

            var lblBottom = new Label { Text = "下:", Location = new Point(355, 55), Size = new Size(20, 20) };
            _nudBottomMargin = new NumericUpDown
            {
                Location = new Point(380, 53),
                Size = new Size(50, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 20
            };

            groupBox.Controls.AddRange(new Control[] {
                lblSpacing, _nudColumnSpacing, lblSpacingUnit, _chkEqualHeight,
                lblMargins, lblTop, _nudTopMargin, lblLeft, _nudLeftMargin,
                lblRight, _nudRightMargin, lblBottom, _nudBottomMargin
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建内容类型组
        /// </summary>
        private void CreateContentTypeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "内容类型",
                Location = new Point(20, 220),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblLeftType = new Label
            {
                Text = "左栏内容:",
                Location = new Point(20, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbLeftContentType = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblRightType = new Label
            {
                Text = "右栏内容:",
                Location = new Point(240, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbRightContentType = new ComboBox
            {
                Location = new Point(320, 23),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            groupBox.Controls.AddRange(new Control[] {
                lblLeftType, _cmbLeftContentType, lblRightType, _cmbRightContentType
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建标题组
        /// </summary>
        private void CreateTitleGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "栏标题设置",
                Location = new Point(20, 310),
                Size = new Size(550, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            _chkShowColumnTitles = new CheckBox
            {
                Text = "显示栏标题",
                Location = new Point(20, 25),
                Size = new Size(100, 20),
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            var lblLeftTitle = new Label
            {
                Text = "左栏标题:",
                Location = new Point(140, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtLeftColumnTitle = new TextBox
            {
                Location = new Point(220, 23),
                Size = new Size(100, 23),
                Text = "左栏内容",
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblRightTitle = new Label
            {
                Text = "右栏标题:",
                Location = new Point(340, 25),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _txtRightColumnTitle = new TextBox
            {
                Location = new Point(420, 23),
                Size = new Size(100, 23),
                Text = "右栏内容",
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            var lblAlignment = new Label
            {
                Text = "标题对齐:",
                Location = new Point(20, 55),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbTitleAlignment = new ComboBox
            {
                Location = new Point(100, 53),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            groupBox.Controls.AddRange(new Control[] {
                _chkShowColumnTitles, lblLeftTitle, _txtLeftColumnTitle, lblRightTitle, _txtRightColumnTitle,
                lblAlignment, _cmbTitleAlignment
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建格式组
        /// </summary>
        private void CreateFormatGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体格式",
                Location = new Point(20, 420),
                Size = new Size(550, 80),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 标题字体
            var lblTitleFont = new Label { Text = "标题字体:", Location = new Point(20, 25), Size = new Size(70, 20) };
            _cmbTitleFontFamily = new ComboBox
            {
                Location = new Point(100, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _nudTitleFontSize = new NumericUpDown
            {
                Location = new Point(210, 23),
                Size = new Size(50, 23),
                Minimum = 8,
                Maximum = 36,
                Value = 14
            };

            _btnTitleFontColor = new Button
            {
                Location = new Point(270, 23),
                Size = new Size(30, 23),
                BackColor = _titleFontColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnTitleFontColor.Click += (s, e) => SelectColor(ref _titleFontColor, _btnTitleFontColor);

            // 内容字体
            var lblContentFont = new Label { Text = "内容字体:", Location = new Point(320, 25), Size = new Size(70, 20) };
            _cmbContentFontFamily = new ComboBox
            {
                Location = new Point(400, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 8F)
            };

            _nudContentFontSize = new NumericUpDown
            {
                Location = new Point(510, 23),
                Size = new Size(50, 23),
                Minimum = 8,
                Maximum = 24,
                Value = 12
            };

            _btnContentFontColor = new Button
            {
                Location = new Point(570, 23),
                Size = new Size(30, 23),
                BackColor = _contentFontColor,
                FlatStyle = FlatStyle.Flat
            };
            _btnContentFontColor.Click += (s, e) => SelectColor(ref _contentFontColor, _btnContentFontColor);

            groupBox.Controls.AddRange(new Control[] {
                lblTitleFont, _cmbTitleFontFamily, _nudTitleFontSize, _btnTitleFontColor,
                lblContentFont, _cmbContentFontFamily, _nudContentFontSize, _btnContentFontColor
            });

            this.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建操作按钮
        /// </summary>
        private void CreateActionButtons()
        {
            _btnPreview = new Button
            {
                Text = "预览",
                Location = new Point(320, 520),
                Size = new Size(60, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnPreview.Click += BtnPreview_Click;

            _btnOK = new Button
            {
                Text = "确定",
                Location = new Point(390, 520),
                Size = new Size(60, 30),
                DialogResult = DialogResult.OK,
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnOK.Click += BtnOK_Click;

            _btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(460, 520),
                Size = new Size(60, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("Microsoft YaHei UI", 9F)
            };

            _btnReset = new Button
            {
                Text = "重置",
                Location = new Point(530, 520),
                Size = new Size(50, 30),
                Font = new Font("Microsoft YaHei UI", 9F)
            };
            _btnReset.Click += BtnReset_Click;

            this.Controls.AddRange(new Control[] { _btnPreview, _btnOK, _btnCancel, _btnReset });
        }

        /// <summary>
        /// 加载默认值
        /// </summary>
        private void LoadDefaultValues()
        {
            // 内容类型选项
            var contentTypes = new string[] { "文本内容", "图片内容", "表格内容", "图表内容", "混合内容" };
            _cmbLeftContentType?.Items.AddRange(contentTypes);
            _cmbRightContentType?.Items.AddRange(contentTypes);
            if (_cmbLeftContentType != null) _cmbLeftContentType.SelectedItem = "文本内容";
            if (_cmbRightContentType != null) _cmbRightContentType.SelectedItem = "文本内容";

            // 对齐方式
            var alignments = new string[] { "左对齐", "居中", "右对齐" };
            _cmbTitleAlignment?.Items.AddRange(alignments);
            if (_cmbTitleAlignment != null) _cmbTitleAlignment.SelectedItem = "居中";

            // 字体列表
            var fonts = new string[] { "Microsoft YaHei UI", "Microsoft YaHei", "SimSun", "Arial", "Calibri" };
            _cmbTitleFontFamily?.Items.AddRange(fonts);
            _cmbContentFontFamily?.Items.AddRange(fonts);
            if (_cmbTitleFontFamily != null) _cmbTitleFontFamily.SelectedItem = "Microsoft YaHei UI";
            if (_cmbContentFontFamily != null) _cmbContentFontFamily.SelectedItem = "Microsoft YaHei UI";

            // 设置控件文字对齐
            SetControlsTextAlignment();
        }

        /// <summary>
        /// 设置控件文字对齐
        /// </summary>
        private void SetControlsTextAlignment()
        {
            // 设置数值输入框文字居中
            SetNumericUpDownTextAlign(_nudLeftColumnWidth);
            SetNumericUpDownTextAlign(_nudRightColumnWidth);
            SetNumericUpDownTextAlign(_nudColumnSpacing);
            SetNumericUpDownTextAlign(_nudTopMargin);
            SetNumericUpDownTextAlign(_nudBottomMargin);
            SetNumericUpDownTextAlign(_nudLeftMargin);
            SetNumericUpDownTextAlign(_nudRightMargin);
            SetNumericUpDownTextAlign(_nudTitleFontSize);
            SetNumericUpDownTextAlign(_nudContentFontSize);

            // 设置下拉框文字居中
            SetComboBoxTextAlign(_cmbLeftContentType);
            SetComboBoxTextAlign(_cmbRightContentType);
            SetComboBoxTextAlign(_cmbTitleAlignment);
            SetComboBoxTextAlign(_cmbTitleFontFamily);
            SetComboBoxTextAlign(_cmbContentFontFamily);

            // 设置文本框文字居中
            if (_txtLeftColumnTitle != null) _txtLeftColumnTitle.TextAlign = HorizontalAlignment.Center;
            if (_txtRightColumnTitle != null) _txtRightColumnTitle.TextAlign = HorizontalAlignment.Center;
        }

        /// <summary>
        /// 设置NumericUpDown文字居中显示
        /// </summary>
        private static void SetNumericUpDownTextAlign(NumericUpDown? numericUpDown)
        {
            if (numericUpDown != null)
                numericUpDown.TextAlign = HorizontalAlignment.Center;
        }

        /// <summary>
        /// 设置ComboBox文字居中显示
        /// </summary>
        private static void SetComboBoxTextAlign(ComboBox? comboBox)
        {
            if (comboBox == null) return;

            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = comboBox.Items[e.Index].ToString();
                if (!string.IsNullOrEmpty(text))
                {
                    var textBounds = e.Bounds;
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                    // 修复编译警告：确保字体不为null
                    var font = e.Font ?? comboBox.Font ?? SystemFonts.DefaultFont;
                    TextRenderer.DrawText(e.Graphics, text, font, textBounds, e.ForeColor, textFlags);
                }

                e.DrawFocusRectangle();
            };
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 选择颜色
        /// </summary>
        /// <param name="colorField">颜色字段</param>
        /// <param name="button">颜色按钮</param>
        private void SelectColor(ref Color colorField, Button? button)
        {
            if (button == null) return;

            using var colorDialog = new ColorDialog
            {
                Color = colorField,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                colorField = colorDialog.Color;
                button.BackColor = colorField;
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 预览按钮点击事件
        /// </summary>
        private void BtnPreview_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("预览功能显示当前两栏布局的效果。\n\n" +
                           "在实际应用中，这里会显示布局预览图。", "布局预览",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                // 收集设置
                Settings = new TwoColumnLayoutSettings
                {
                    LeftColumnWidth = (int)(_nudLeftColumnWidth?.Value ?? 50),
                    RightColumnWidth = (int)(_nudRightColumnWidth?.Value ?? 50),
                    ColumnSpacing = (int)(_nudColumnSpacing?.Value ?? 20),
                    TopMargin = (int)(_nudTopMargin?.Value ?? 20),
                    BottomMargin = (int)(_nudBottomMargin?.Value ?? 20),
                    LeftMargin = (int)(_nudLeftMargin?.Value ?? 20),
                    RightMargin = (int)(_nudRightMargin?.Value ?? 20),
                    LeftContentType = _cmbLeftContentType?.SelectedItem?.ToString() ?? "文本内容",
                    RightContentType = _cmbRightContentType?.SelectedItem?.ToString() ?? "文本内容",
                    ShowColumnTitles = _chkShowColumnTitles?.Checked ?? false,
                    LeftColumnTitle = _txtLeftColumnTitle?.Text ?? "左栏内容",
                    RightColumnTitle = _txtRightColumnTitle?.Text ?? "右栏内容",
                    TitleAlignment = _cmbTitleAlignment?.SelectedItem?.ToString() ?? "居中",
                    AutoBalance = _chkAutoBalance?.Checked ?? true,
                    EqualHeight = _chkEqualHeight?.Checked ?? false,
                    TitleFontFamily = _cmbTitleFontFamily?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    TitleFontSize = (int)(_nudTitleFontSize?.Value ?? 14),
                    TitleFontColor = _titleFontColor,
                    ContentFontFamily = _cmbContentFontFamily?.SelectedItem?.ToString() ?? "Microsoft YaHei UI",
                    ContentFontSize = (int)(_nudContentFontSize?.Value ?? 12),
                    ContentFontColor = _contentFontColor
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            LoadDefaultValues();

            // 重置数值控件
            if (_nudLeftColumnWidth != null) _nudLeftColumnWidth.Value = 50;
            if (_nudRightColumnWidth != null) _nudRightColumnWidth.Value = 50;
            if (_nudColumnSpacing != null) _nudColumnSpacing.Value = 20;
            if (_nudTopMargin != null) _nudTopMargin.Value = 20;
            if (_nudBottomMargin != null) _nudBottomMargin.Value = 20;
            if (_nudLeftMargin != null) _nudLeftMargin.Value = 20;
            if (_nudRightMargin != null) _nudRightMargin.Value = 20;
            if (_nudTitleFontSize != null) _nudTitleFontSize.Value = 14;
            if (_nudContentFontSize != null) _nudContentFontSize.Value = 12;

            // 重置复选框
            if (_chkShowColumnTitles != null) _chkShowColumnTitles.Checked = false;
            if (_chkAutoBalance != null) _chkAutoBalance.Checked = true;
            if (_chkEqualHeight != null) _chkEqualHeight.Checked = false;

            // 重置文本框
            if (_txtLeftColumnTitle != null) _txtLeftColumnTitle.Text = "左栏内容";
            if (_txtRightColumnTitle != null) _txtRightColumnTitle.Text = "右栏内容";

            // 重置颜色
            _titleFontColor = Color.Black;
            _contentFontColor = Color.Black;
            if (_btnTitleFontColor != null) _btnTitleFontColor.BackColor = _titleFontColor;
            if (_btnContentFontColor != null) _btnContentFontColor.BackColor = _contentFontColor;
        }

        #endregion
    }

    /// <summary>
    /// 两栏布局设置类
    /// </summary>
    public class TwoColumnLayoutSettings
    {
        public int LeftColumnWidth { get; set; } = 50;
        public int RightColumnWidth { get; set; } = 50;
        public int ColumnSpacing { get; set; } = 20;
        public int TopMargin { get; set; } = 20;
        public int BottomMargin { get; set; } = 20;
        public int LeftMargin { get; set; } = 20;
        public int RightMargin { get; set; } = 20;
        public string LeftContentType { get; set; } = "文本内容";
        public string RightContentType { get; set; } = "文本内容";
        public bool ShowColumnTitles { get; set; } = false;
        public string LeftColumnTitle { get; set; } = "左栏内容";
        public string RightColumnTitle { get; set; } = "右栏内容";
        public string TitleAlignment { get; set; } = "居中";
        public bool AutoBalance { get; set; } = true;
        public bool EqualHeight { get; set; } = false;
        public string TitleFontFamily { get; set; } = "Microsoft YaHei UI";
        public int TitleFontSize { get; set; } = 14;
        public Color TitleFontColor { get; set; } = Color.Black;
        public string ContentFontFamily { get; set; } = "Microsoft YaHei UI";
        public int ContentFontSize { get; set; } = 12;
        public Color ContentFontColor { get; set; } = Color.Black;
    }
}
