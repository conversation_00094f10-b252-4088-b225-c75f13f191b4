using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 文件名替换服务类 - 负责处理文件名模式替换功能，支持文本替换和正则匹配，包含规则验证和冲突处理
    /// </summary>
    public class FilenameReplacementService
    {
        /// <summary>
        /// 应用文件名替换规则
        /// </summary>
        /// <param name="originalFilePath">原始文件路径</param>
        /// <param name="settings">文件名替换设置</param>
        /// <returns>新的文件路径</returns>
        public string ApplyFilenameReplacement(string originalFilePath, FilenameReplacementSettings settings)
        {
            return ApplyFilenameReplacement(originalFilePath, settings, null);
        }

        /// <summary>
        /// 应用文件名替换规则（使用全局冲突处理设置）
        /// </summary>
        /// <param name="originalFilePath">原始文件路径</param>
        /// <param name="settings">文件名替换设置</param>
        /// <param name="globalConflictHandling">全局冲突处理方式（如果为null则使用settings中的设置）</param>
        /// <returns>新的文件路径</returns>
        public string ApplyFilenameReplacement(string originalFilePath, FilenameReplacementSettings settings, FilenameConflictHandlingType? globalConflictHandling)
        {
            try
            {
                if (!settings.PatternReplacement.IsEnabled ||
                    settings.PatternReplacement.ReplacementRules == null ||
                    !settings.PatternReplacement.ReplacementRules.Any(r => r.IsEnabled))
                {
                    return originalFilePath;
                }

                var directory = Path.GetDirectoryName(originalFilePath) ?? "";
                var fileName = Path.GetFileName(originalFilePath);
                var extension = Path.GetExtension(fileName);
                var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

                var newFileName = fileName;

                // 按顺序应用所有启用的规则
                foreach (var rule in settings.PatternReplacement.ReplacementRules.Where(r => r.IsEnabled))
                {
                    newFileName = ApplyRule(newFileName, rule);
                }

                // 如果文件名没有变化，返回原路径
                if (newFileName == fileName)
                {
                    return originalFilePath;
                }

                // 构建新的文件路径
                var newFilePath = Path.Combine(directory, newFileName);

                // 如果提供了全局冲突处理设置，则使用全局设置；否则使用功能自身的设置
                if (globalConflictHandling.HasValue)
                {
                    // 使用全局冲突处理设置，但不在这里处理冲突，由调用方处理
                    // 这里只返回新的文件路径，冲突处理由FileProcessingService负责
                    return newFilePath;
                }
                else
                {
                    // 使用功能自身的冲突处理设置
                    newFilePath = HandleFilenameConflict(newFilePath, settings.ConflictHandling);
                    return newFilePath;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"应用文件名替换规则失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 应用单个替换规则
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="rule">替换规则</param>
        /// <returns>替换后的文件名</returns>
        private string ApplyRule(string fileName, FilenamePatternReplacementRule rule)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(rule.SourcePattern))
                {
                    return fileName;
                }

                var targetText = rule.IncludeExtension ? fileName : Path.GetFileNameWithoutExtension(fileName);
                var extension = rule.IncludeExtension ? "" : Path.GetExtension(fileName);

                string result;

                if (rule.MatchType == FilenameMatchType.Regex)
                {
                    // 正则表达式替换
                    var regexOptions = rule.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
                    var regex = new Regex(rule.SourcePattern, regexOptions);
                    result = regex.Replace(targetText, rule.TargetPattern);
                }
                else
                {
                    // 文本替换
                    var comparison = rule.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
                    result = targetText.Replace(rule.SourcePattern, rule.TargetPattern, comparison);
                }

                // 如果不包含扩展名，需要重新添加扩展名
                if (!rule.IncludeExtension && !string.IsNullOrEmpty(extension))
                {
                    result += extension;
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"应用替换规则 '{rule.RuleName}' 失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理文件名冲突
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="conflictHandling">冲突处理设置</param>
        /// <returns>处理后的文件路径</returns>
        private string HandleFilenameConflict(string filePath, FilenameConflictHandlingSettings conflictHandling)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return filePath;
                }

                switch (conflictHandling.ConflictHandlingType)
                {
                    case FilenameConflictHandlingType.Overwrite:
                        return filePath;

                    case FilenameConflictHandlingType.Skip:
                        throw new InvalidOperationException($"文件已存在，跳过处理: {filePath}");

                    case FilenameConflictHandlingType.AutoRename:
                        return GenerateUniqueFilename(filePath, conflictHandling.AutoRenameFormat);

                    case FilenameConflictHandlingType.Ask:
                        // 在实际应用中，这里应该弹出对话框询问用户
                        // 目前默认使用自动重命名
                        return GenerateUniqueFilename(filePath, conflictHandling.AutoRenameFormat);

                    default:
                        return filePath;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"处理文件名冲突失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成唯一的文件名
        /// </summary>
        /// <param name="originalPath">原始文件路径</param>
        /// <param name="renameFormat">重命名格式</param>
        /// <returns>唯一的文件路径</returns>
        private string GenerateUniqueFilename(string originalPath, string renameFormat)
        {
            var directory = Path.GetDirectoryName(originalPath) ?? "";
            var fileName = Path.GetFileNameWithoutExtension(originalPath);
            var extension = Path.GetExtension(originalPath);

            var index = 1;
            string newPath;

            do
            {
                var newFileName = renameFormat
                    .Replace("{filename}", fileName)
                    .Replace("{index}", index.ToString())
                    .Replace("{timestamp}", DateTime.Now.ToString("yyyyMMdd_HHmmss"));

                newPath = Path.Combine(directory, newFileName + extension);
                index++;
            }
            while (File.Exists(newPath) && index < 10000); // 防止无限循环

            if (index >= 10000)
            {
                throw new InvalidOperationException("无法生成唯一的文件名");
            }

            return newPath;
        }

        /// <summary>
        /// 验证替换规则
        /// </summary>
        /// <param name="rule">替换规则</param>
        /// <returns>验证结果</returns>
        public ValidationResult ValidateRule(FilenamePatternReplacementRule rule)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(rule.SourcePattern))
            {
                result.IsValid = false;
                result.ErrorMessage = "源文本不能为空";
                return result;
            }

            if (rule.MatchType == FilenameMatchType.Regex)
            {
                try
                {
                    var regex = new Regex(rule.SourcePattern);
                }
                catch (Exception ex)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"正则表达式格式错误: {ex.Message}";
                    return result;
                }
            }

            // 检查目标文本是否包含非法字符
            var invalidChars = Path.GetInvalidFileNameChars();
            if (!string.IsNullOrEmpty(rule.TargetPattern) && rule.TargetPattern.Any(c => invalidChars.Contains(c)))
            {
                result.IsValid = false;
                result.ErrorMessage = $"目标文本包含非法字符: {string.Join(", ", rule.TargetPattern.Where(c => invalidChars.Contains(c)).Distinct())}";
                return result;
            }

            // 检查目标文本是否会导致空文件名（当不包含扩展名时）
            if (!rule.IncludeExtension && string.IsNullOrWhiteSpace(rule.TargetPattern))
            {
                result.IsValid = false;
                result.ErrorMessage = "当不包含扩展名时，目标文本不能为空";
                return result;
            }

            return result;
        }

        /// <summary>
        /// 预览替换结果
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="rule">替换规则</param>
        /// <returns>预览结果</returns>
        public string PreviewReplacement(string fileName, FilenamePatternReplacementRule rule)
        {
            try
            {
                var validationResult = ValidateRule(rule);
                if (!validationResult.IsValid)
                {
                    return $"规则验证失败: {validationResult.ErrorMessage}";
                }

                return ApplyRule(fileName, rule);
            }
            catch (Exception ex)
            {
                return $"预览失败: {ex.Message}";
            }
        }
    }

    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = "";
    }
}
