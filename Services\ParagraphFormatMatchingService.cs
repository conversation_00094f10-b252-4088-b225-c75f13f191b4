using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Aspose.Slides;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 段落格式匹配处理服务 - 负责根据配置的匹配规则对PPT中的段落进行格式匹配和应用，支持Aspose.Slides API
    /// </summary>
    public class ParagraphFormatMatchingService
    {
        /// <summary>
        /// 应用段落格式匹配规则到演示文稿
        /// </summary>
        /// <param name="presentation">演示文稿对象</param>
        /// <param name="settings">段落格式匹配设置</param>
        /// <returns>处理结果</returns>
        public async Task<ParagraphMatchingResult> ApplyParagraphFormatMatchingAsync(
            IPresentation presentation,
            ParagraphFormatMatchingSettings settings)
        {
            var result = new ParagraphMatchingResult();

            if (!settings.EnableParagraphFormatMatching || settings.MatchingRules.Count == 0)
            {
                result.IsSuccess = true;
                result.Message = "段落格式匹配功能未启用或无匹配规则";
                return result;
            }

            try
            {
                // 获取启用的规则
                var enabledRules = settings.MatchingRules.Where(r => r.IsEnabled).ToList();
                if (enabledRules.Count == 0)
                {
                    result.IsSuccess = true;
                    result.Message = "没有启用的匹配规则";
                    return result;
                }

                // 根据替换范围处理不同类型的幻灯片
                await ProcessPresentationByScope(presentation, enabledRules, result);

                result.IsSuccess = true;
                result.Message = $"成功处理 {result.ProcessedParagraphs} 个段落，匹配 {result.MatchedParagraphs} 个段落";
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"处理段落格式匹配时发生错误: {ex.Message}";
                result.Exception = ex;
            }

            return result;
        }

        /// <summary>
        /// 根据替换范围处理演示文稿
        /// </summary>
        private async Task ProcessPresentationByScope(
            IPresentation presentation,
            List<ParagraphFormatMatchingRule> rules,
            ParagraphMatchingResult result)
        {
            // 按替换范围分组规则
            var normalSlideRules = rules.Where(r => r.ReplacementScope.IncludeNormalSlides).ToList();
            var masterSlideRules = rules.Where(r => r.ReplacementScope.IncludeMasterSlides).ToList();
            var layoutSlideRules = rules.Where(r => r.ReplacementScope.IncludeLayoutSlides).ToList();

            // 处理普通幻灯片
            if (normalSlideRules.Count > 0)
            {
                foreach (var slide in presentation.Slides)
                {
                    await ProcessSlideAsync(slide, normalSlideRules, result);
                }
            }

            // 处理母版页
            if (masterSlideRules.Count > 0)
            {
                foreach (var masterSlide in presentation.Masters)
                {
                    await ProcessMasterSlideAsync(masterSlide, masterSlideRules, result);
                }
            }

            // 处理母版版式页
            if (layoutSlideRules.Count > 0)
            {
                foreach (var masterSlide in presentation.Masters)
                {
                    foreach (var layoutSlide in masterSlide.LayoutSlides)
                    {
                        await ProcessLayoutSlideAsync(layoutSlide, layoutSlideRules, result);
                    }
                }
            }
        }

        /// <summary>
        /// 处理单个幻灯片
        /// </summary>
        private async Task ProcessSlideAsync(
            ISlide slide,
            List<ParagraphFormatMatchingRule> rules,
            ParagraphMatchingResult result)
        {
            // 处理幻灯片中的所有形状
            foreach (var shape in slide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    await ProcessTextFrameAsync(autoShape.TextFrame, rules, result);
                }
                else if (shape is ITable table)
                {
                    await ProcessTableAsync(table, rules, result);
                }
                else if (shape is IGroupShape groupShape)
                {
                    await ProcessGroupShapeAsync(groupShape, rules, result);
                }
            }

            // 处理幻灯片备注
            if (slide.NotesSlideManager.NotesSlide != null)
            {
                var notesSlide = slide.NotesSlideManager.NotesSlide;
                foreach (var shape in notesSlide.Shapes)
                {
                    if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                    {
                        await ProcessTextFrameAsync(autoShape.TextFrame, rules, result);
                    }
                }
            }
        }

        /// <summary>
        /// 处理母版页
        /// </summary>
        private async Task ProcessMasterSlideAsync(
            IMasterSlide masterSlide,
            List<ParagraphFormatMatchingRule> rules,
            ParagraphMatchingResult result)
        {
            // 处理母版页中的所有形状
            foreach (var shape in masterSlide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    await ProcessTextFrameAsync(autoShape.TextFrame, rules, result);
                }
                else if (shape is ITable table)
                {
                    await ProcessTableAsync(table, rules, result);
                }
                else if (shape is IGroupShape groupShape)
                {
                    await ProcessGroupShapeAsync(groupShape, rules, result);
                }
            }
        }

        /// <summary>
        /// 处理母版版式页
        /// </summary>
        private async Task ProcessLayoutSlideAsync(
            ILayoutSlide layoutSlide,
            List<ParagraphFormatMatchingRule> rules,
            ParagraphMatchingResult result)
        {
            // 处理版式页中的所有形状
            foreach (var shape in layoutSlide.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    await ProcessTextFrameAsync(autoShape.TextFrame, rules, result);
                }
                else if (shape is ITable table)
                {
                    await ProcessTableAsync(table, rules, result);
                }
                else if (shape is IGroupShape groupShape)
                {
                    await ProcessGroupShapeAsync(groupShape, rules, result);
                }
            }

            // 注意：ILayoutSlide没有Placeholders属性，占位符已经包含在Shapes中
        }

        /// <summary>
        /// 处理文本框
        /// </summary>
        private async Task ProcessTextFrameAsync(
            ITextFrame textFrame,
            List<ParagraphFormatMatchingRule> rules,
            ParagraphMatchingResult result)
        {
            if (textFrame.Paragraphs == null) return;

            foreach (var paragraph in textFrame.Paragraphs)
            {
                result.ProcessedParagraphs++;

                // 获取段落文本
                var paragraphText = GetParagraphText(paragraph);
                if (string.IsNullOrEmpty(paragraphText)) continue;

                // 检查每个规则
                foreach (var rule in rules)
                {
                    if (IsParagraphMatch(paragraphText, rule.MatchingConditions))
                    {
                        // 应用格式
                        await ApplyFormatToParagraphAsync(paragraph, rule);
                        result.MatchedParagraphs++;
                        result.AppliedRules.Add($"规则 '{rule.RuleName}' 应用到段落: {paragraphText.Substring(0, Math.Min(50, paragraphText.Length))}...");
                        break; // 只应用第一个匹配的规则
                    }
                }
            }
        }

        /// <summary>
        /// 处理表格
        /// </summary>
        private async Task ProcessTableAsync(
            ITable table,
            List<ParagraphFormatMatchingRule> rules,
            ParagraphMatchingResult result)
        {
            for (int row = 0; row < table.Rows.Count; row++)
            {
                for (int col = 0; col < table.Columns.Count; col++)
                {
                    var cell = table[col, row];
                    if (cell.TextFrame != null)
                    {
                        await ProcessTextFrameAsync(cell.TextFrame, rules, result);
                    }
                }
            }
        }

        /// <summary>
        /// 处理组合形状
        /// </summary>
        private async Task ProcessGroupShapeAsync(
            IGroupShape groupShape,
            List<ParagraphFormatMatchingRule> rules,
            ParagraphMatchingResult result)
        {
            foreach (var shape in groupShape.Shapes)
            {
                if (shape is IAutoShape autoShape && autoShape.TextFrame != null)
                {
                    await ProcessTextFrameAsync(autoShape.TextFrame, rules, result);
                }
                else if (shape is ITable table)
                {
                    await ProcessTableAsync(table, rules, result);
                }
                else if (shape is IGroupShape nestedGroup)
                {
                    await ProcessGroupShapeAsync(nestedGroup, rules, result);
                }
            }
        }

        /// <summary>
        /// 获取段落文本
        /// </summary>
        private string GetParagraphText(IParagraph paragraph)
        {
            if (paragraph.Portions == null) return string.Empty;

            return string.Join("", paragraph.Portions.Select(p => p.Text));
        }

        /// <summary>
        /// 检查段落是否匹配条件
        /// </summary>
        private bool IsParagraphMatch(string paragraphText, ParagraphMatchingConditions conditions)
        {
            // 字符数限制检查
            if (conditions.EnableCharacterCountLimit)
            {
                var length = paragraphText.Length;
                if (length < conditions.MinCharacterCount || length > conditions.MaxCharacterCount)
                {
                    return false;
                }
            }

            bool hasMatch = false;

            // 段落开头匹配
            if (conditions.EnableStartsWith && !string.IsNullOrEmpty(conditions.StartsWithText))
            {
                var comparison = conditions.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
                if (paragraphText.StartsWith(conditions.StartsWithText, comparison))
                {
                    hasMatch = true;
                }
            }

            // 段落包含关键词匹配
            if (conditions.EnableContains && conditions.ContainsKeywords.Count > 0)
            {
                var comparison = conditions.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
                foreach (var keyword in conditions.ContainsKeywords)
                {
                    if (!string.IsNullOrEmpty(keyword))
                    {
                        if (conditions.WholeWord)
                        {
                            // 全词匹配
                            var pattern = $@"\b{Regex.Escape(keyword)}\b";
                            var options = conditions.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
                            if (Regex.IsMatch(paragraphText, pattern, options))
                            {
                                hasMatch = true;
                                break;
                            }
                        }
                        else
                        {
                            // 普通包含匹配
                            if (paragraphText.Contains(keyword, comparison))
                            {
                                hasMatch = true;
                                break;
                            }
                        }
                    }
                }
            }

            // 段落结尾匹配
            if (conditions.EnableEndsWith && !string.IsNullOrEmpty(conditions.EndsWithText))
            {
                var comparison = conditions.CaseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
                if (paragraphText.EndsWith(conditions.EndsWithText, comparison))
                {
                    hasMatch = true;
                }
            }

            // 正则表达式匹配
            if (conditions.EnableRegex && !string.IsNullOrEmpty(conditions.RegexPattern))
            {
                try
                {
                    var options = conditions.CaseSensitive ? RegexOptions.None : RegexOptions.IgnoreCase;
                    if (Regex.IsMatch(paragraphText, conditions.RegexPattern, options))
                    {
                        hasMatch = true;
                    }
                }
                catch (ArgumentException)
                {
                    // 正则表达式无效，忽略此条件
                }
            }

            return hasMatch;
        }

        /// <summary>
        /// 应用格式到段落
        /// </summary>
        private async Task ApplyFormatToParagraphAsync(IParagraph paragraph, ParagraphFormatMatchingRule rule)
        {
            // 应用段落格式
            if (rule.ParagraphFormat.EnableParagraphFormat)
            {
                await ApplyParagraphFormatAsync(paragraph, rule.ParagraphFormat);
            }

            // 应用字体格式
            if (rule.FontFormat.EnableFontFormat)
            {
                await ApplyFontFormatAsync(paragraph, rule.FontFormat);
            }
        }

        /// <summary>
        /// 应用段落格式
        /// </summary>
        private async Task ApplyParagraphFormatAsync(IParagraph paragraph, ParagraphFormatSettings format)
        {
            var paragraphFormat = paragraph.ParagraphFormat;

            // 对齐方式
            paragraphFormat.Alignment = ConvertAlignment(format.Alignment);

            // 缩进设置
            if (format.EnableIndentation)
            {
                paragraphFormat.Indent = format.LeftIndent;
                paragraphFormat.MarginLeft = format.MarginLeft;
                paragraphFormat.MarginRight = format.MarginRight;

                // 特殊缩进
                switch (format.SpecialIndent)
                {
                    case SpecialIndentType.FirstLine:
                        paragraphFormat.Indent = format.SpecialIndentValue;
                        break;
                    case SpecialIndentType.Hanging:
                        paragraphFormat.Indent = -format.SpecialIndentValue;
                        break;
                }
            }

            // 间距设置
            if (format.EnableSpacing)
            {
                paragraphFormat.SpaceBefore = format.SpaceBefore;
                paragraphFormat.SpaceAfter = format.SpaceAfter;

                // 行间距
                switch (format.LineSpacingType)
                {
                    case LineSpacingType.Single:
                        paragraphFormat.SpaceWithin = 1.0f;
                        break;
                    case LineSpacingType.OneAndHalf:
                        paragraphFormat.SpaceWithin = 1.5f;
                        break;
                    case LineSpacingType.Double:
                        paragraphFormat.SpaceWithin = 2.0f;
                        break;
                    case LineSpacingType.Multiple:
                        paragraphFormat.SpaceWithin = format.LineSpacingValue;
                        break;
                    case LineSpacingType.Fixed:
                        paragraphFormat.SpaceWithin = format.LineSpacingValue / 12.0f; // 转换为行数
                        break;
                }
            }

            // 中文控制选项
            if (format.EnableChineseControl)
            {
                paragraphFormat.EastAsianLineBreak = format.ChineseCharacterControl ? NullableBool.True : NullableBool.False;
                paragraphFormat.LatinLineBreak = format.AllowLatinWordBreak ? NullableBool.True : NullableBool.False;
                paragraphFormat.HangingPunctuation = format.AllowPunctuationOverhang ? NullableBool.True : NullableBool.False;
            }

            // 字体对齐
            if (format.EnableFontAlignment)
            {
                paragraphFormat.FontAlignment = ConvertFontAlignment(format.FontAlignment);
            }

            // 从右到左书写
            if (format.EnableRightToLeft)
            {
                paragraphFormat.RightToLeft = format.RightToLeft ? NullableBool.True : NullableBool.False;
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 应用字体格式
        /// </summary>
        private async Task ApplyFontFormatAsync(IParagraph paragraph, FontFormatSettings format)
        {
            if (paragraph.Portions == null) return;

            foreach (var portion in paragraph.Portions)
            {
                var portionFormat = portion.PortionFormat;

                // 中文字体
                if (format.SetChineseFont && !string.IsNullOrEmpty(format.ChineseFontName))
                {
                    portionFormat.EastAsianFont = new FontData(format.ChineseFontName);
                }

                // 西文字体
                if (format.SetLatinFont && !string.IsNullOrEmpty(format.LatinFontName))
                {
                    portionFormat.LatinFont = new FontData(format.LatinFontName);
                }

                // 字体样式
                switch (format.FontStyle)
                {
                    case FontStyleType.Regular:
                        portionFormat.FontBold = NullableBool.False;
                        portionFormat.FontItalic = NullableBool.False;
                        break;
                    case FontStyleType.Bold:
                        portionFormat.FontBold = NullableBool.True;
                        portionFormat.FontItalic = NullableBool.False;
                        break;
                    case FontStyleType.Italic:
                        portionFormat.FontBold = NullableBool.False;
                        portionFormat.FontItalic = NullableBool.True;
                        break;
                    case FontStyleType.BoldItalic:
                        portionFormat.FontBold = NullableBool.True;
                        portionFormat.FontItalic = NullableBool.True;
                        break;
                }

                // 字体大小
                if (format.SetFontSize)
                {
                    portionFormat.FontHeight = format.FontSize;
                }

                // 字体颜色
                if (format.SetFontColor && !string.IsNullOrEmpty(format.FontColor))
                {
                    try
                    {
                        var color = System.Drawing.ColorTranslator.FromHtml(format.FontColor);
                        portionFormat.FillFormat.FillType = FillType.Solid;
                        portionFormat.FillFormat.SolidFillColor.Color = color;
                    }
                    catch
                    {
                        // 颜色格式无效，忽略
                    }
                }

                // 下划线
                if (format.SetUnderline)
                {
                    portionFormat.FontUnderline = ConvertUnderlineType(format.UnderlineType);

                    if (!string.IsNullOrEmpty(format.UnderlineColor))
                    {
                        try
                        {
                            var color = System.Drawing.ColorTranslator.FromHtml(format.UnderlineColor);
                            portionFormat.UnderlineFillFormat.FillType = FillType.Solid;
                            portionFormat.UnderlineFillFormat.SolidFillColor.Color = color;
                        }
                        catch
                        {
                            // 颜色格式无效，忽略
                        }
                    }
                }

                // 文字效果
                if (format.SetTextEffects)
                {
                    portionFormat.StrikethroughType = format.Strikethrough ?
                        (format.DoubleStrikethrough ? TextStrikethroughType.Double : TextStrikethroughType.Single) :
                        TextStrikethroughType.None;

                    if (format.Superscript)
                    {
                        portionFormat.Escapement = 30; // 上标
                    }
                    else if (format.Subscript)
                    {
                        portionFormat.Escapement = -30; // 下标
                    }
                    else
                    {
                        portionFormat.Escapement = 0; // 正常
                    }
                }
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 转换对齐方式
        /// </summary>
        private TextAlignment ConvertAlignment(ParagraphAlignment alignment)
        {
            return alignment switch
            {
                ParagraphAlignment.Left => TextAlignment.Left,
                ParagraphAlignment.Center => TextAlignment.Center,
                ParagraphAlignment.Right => TextAlignment.Right,
                ParagraphAlignment.Justify => TextAlignment.Justify,
                ParagraphAlignment.Distribute => TextAlignment.Distributed,
                _ => TextAlignment.Left
            };
        }

        /// <summary>
        /// 转换字体对齐方式
        /// </summary>
        private FontAlignment ConvertFontAlignment(FontAlignmentType fontAlignment)
        {
            return fontAlignment switch
            {
                FontAlignmentType.Automatic => FontAlignment.Automatic,
                FontAlignmentType.Top => FontAlignment.Top,
                FontAlignmentType.Center => FontAlignment.Center,
                FontAlignmentType.Baseline => FontAlignment.Baseline,
                FontAlignmentType.Bottom => FontAlignment.Bottom,
                _ => FontAlignment.Automatic
            };
        }

        /// <summary>
        /// 转换下划线类型
        /// </summary>
        private TextUnderlineType ConvertUnderlineType(UnderlineType underlineType)
        {
            return underlineType switch
            {
                UnderlineType.None => TextUnderlineType.None,
                UnderlineType.Single => TextUnderlineType.Single,
                UnderlineType.Double => TextUnderlineType.Double,
                UnderlineType.Heavy => TextUnderlineType.Heavy,
                UnderlineType.Dotted => TextUnderlineType.Dotted,
                UnderlineType.Dashed => TextUnderlineType.Dashed,
                UnderlineType.DashDot => TextUnderlineType.DotDash,
                UnderlineType.DashDotDot => TextUnderlineType.DotDotDash,
                UnderlineType.Wavy => TextUnderlineType.Wavy,
                _ => TextUnderlineType.None
            };
        }
    }

    /// <summary>
    /// 段落匹配处理结果
    /// </summary>
    public class ParagraphMatchingResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; } = false;

        /// <summary>
        /// 处理消息
        /// </summary>
        public string Message { get; set; } = "";

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 处理的段落总数
        /// </summary>
        public int ProcessedParagraphs { get; set; } = 0;

        /// <summary>
        /// 匹配的段落数
        /// </summary>
        public int MatchedParagraphs { get; set; } = 0;

        /// <summary>
        /// 应用的规则列表
        /// </summary>
        public List<string> AppliedRules { get; set; } = new List<string>();
    }
}